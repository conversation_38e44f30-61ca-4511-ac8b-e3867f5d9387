{"ast": null, "code": "import{initializeApp}from'firebase/app';import{getAuth,connectAuthEmulator}from'firebase/auth';import{getFirestore,enableNetwork,disableNetwork,persistentLocalCache}from'firebase/firestore';import{getStorage}from'firebase/storage';// Firebase configuration\n// For demo purposes, using Firebase emulator\n// TODO: Replace with your actual Firebase config for production\nconst firebaseConfig={apiKey:\"demo-api-key\",authDomain:\"demo-project.firebaseapp.com\",projectId:\"demo-cyber-pos\",storageBucket:\"demo-project.appspot.com\",messagingSenderId:\"123456789\",appId:\"demo-app-id\"};// Initialize Firebase\nconst app=initializeApp(firebaseConfig);// Initialize Firebase services with modern persistence\nexport const auth=getAuth(app);export const db=getFirestore(app,{localCache:persistentLocalCache({// Default tab manager supports multiple tabs automatically\n})});export const storage=getStorage(app);// Connect to emulators in development\nif(process.env.NODE_ENV==='development'){try{// Only connect if not already connected\n// Check if emulator is already connected by trying to connect\nconnectAuthEmulator(auth,'http://localhost:9099');// Note: Firestore emulator connection would go here if using emulator\n// connectFirestoreEmulator(db, 'localhost', 8080);\n}catch(error){console.log('Emulators not available, using production Firebase');}}// Offline persistence is now automatically enabled through FirestoreSettings.localCache\n// No need for a separate enableOfflineSupport function with the new approach\nexport const enableOfflineSupport=async()=>{// This function is kept for backward compatibility but is no longer needed\n// Persistence is automatically enabled through the localCache configuration above\nconsole.log('Firebase offline persistence is automatically enabled with the new configuration');};// Network status management\nexport const goOffline=async()=>{try{await disableNetwork(db);console.log('Firebase network disabled');}catch(error){console.error('Error disabling network:',error);}};export const goOnline=async()=>{try{await enableNetwork(db);console.log('Firebase network enabled');}catch(error){console.error('Error enabling network:',error);}};export default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "connectAuthEmulator", "getFirestore", "enableNetwork", "disableNetwork", "persistentLocalCache", "getStorage", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db", "localCache", "storage", "process", "env", "NODE_ENV", "error", "console", "log", "enableOfflineSupport", "goOffline", "goOnline"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\nimport {\n  getFirestore,\n  enableNetwork,\n  disableNetwork,\n  connectFirestoreEmulator,\n  persistentLocalCache\n} from 'firebase/firestore';\nimport { getStorage, connectStorageEmulator } from 'firebase/storage';\n\n// Firebase configuration\n// For demo purposes, using Firebase emulator\n// TODO: Replace with your actual Firebase config for production\nconst firebaseConfig = {\n  apiKey: \"demo-api-key\",\n  authDomain: \"demo-project.firebaseapp.com\",\n  projectId: \"demo-cyber-pos\",\n  storageBucket: \"demo-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"demo-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services with modern persistence\nexport const auth = getAuth(app);\nexport const db = getFirestore(app, {\n  localCache: persistentLocalCache({\n    // Default tab manager supports multiple tabs automatically\n  })\n});\nexport const storage = getStorage(app);\n\n// Connect to emulators in development\nif (process.env.NODE_ENV === 'development') {\n  try {\n    // Only connect if not already connected\n    // Check if emulator is already connected by trying to connect\n    connectAuthEmulator(auth, 'http://localhost:9099');\n    // Note: Firestore emulator connection would go here if using emulator\n    // connectFirestoreEmulator(db, 'localhost', 8080);\n  } catch (error) {\n    console.log('Emulators not available, using production Firebase');\n  }\n}\n\n// Offline persistence is now automatically enabled through FirestoreSettings.localCache\n// No need for a separate enableOfflineSupport function with the new approach\nexport const enableOfflineSupport = async () => {\n  // This function is kept for backward compatibility but is no longer needed\n  // Persistence is automatically enabled through the localCache configuration above\n  console.log('Firebase offline persistence is automatically enabled with the new configuration');\n};\n\n// Network status management\nexport const goOffline = async () => {\n  try {\n    await disableNetwork(db);\n    console.log('Firebase network disabled');\n  } catch (error) {\n    console.error('Error disabling network:', error);\n  }\n};\n\nexport const goOnline = async () => {\n  try {\n    await enableNetwork(db);\n    console.log('Firebase network enabled');\n  } catch (error) {\n    console.error('Error enabling network:', error);\n  }\n};\n\nexport default app;\n"], "mappings": "AAAA,OAASA,aAAa,KAAQ,cAAc,CAC5C,OAASC,OAAO,CAAEC,mBAAmB,KAAQ,eAAe,CAC5D,OACEC,YAAY,CACZC,aAAa,CACbC,cAAc,CAEdC,oBAAoB,KACf,oBAAoB,CAC3B,OAASC,UAAU,KAAgC,kBAAkB,CAErE;AACA;AACA;AACA,KAAM,CAAAC,cAAc,CAAG,CACrBC,MAAM,CAAE,cAAc,CACtBC,UAAU,CAAE,8BAA8B,CAC1CC,SAAS,CAAE,gBAAgB,CAC3BC,aAAa,CAAE,0BAA0B,CACzCC,iBAAiB,CAAE,WAAW,CAC9BC,KAAK,CAAE,aACT,CAAC,CAED;AACA,KAAM,CAAAC,GAAG,CAAGf,aAAa,CAACQ,cAAc,CAAC,CAEzC;AACA,MAAO,MAAM,CAAAQ,IAAI,CAAGf,OAAO,CAACc,GAAG,CAAC,CAChC,MAAO,MAAM,CAAAE,EAAE,CAAGd,YAAY,CAACY,GAAG,CAAE,CAClCG,UAAU,CAAEZ,oBAAoB,CAAC,CAC/B;AAAA,CACD,CACH,CAAC,CAAC,CACF,MAAO,MAAM,CAAAa,OAAO,CAAGZ,UAAU,CAACQ,GAAG,CAAC,CAEtC;AACA,GAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1C,GAAI,CACF;AACA;AACApB,mBAAmB,CAACc,IAAI,CAAE,uBAAuB,CAAC,CAClD;AACA;AACF,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC,CACnE,CACF,CAEA;AACA;AACA,MAAO,MAAM,CAAAC,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CAC9C;AACA;AACAF,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC,CACjG,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,SAAS,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAtB,cAAc,CAACY,EAAE,CAAC,CACxBO,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAC1C,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CACF,CAAC,CAED,MAAO,MAAM,CAAAK,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF,KAAM,CAAAxB,aAAa,CAACa,EAAE,CAAC,CACvBO,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CACzC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAAC,CAED,cAAe,CAAAR,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}