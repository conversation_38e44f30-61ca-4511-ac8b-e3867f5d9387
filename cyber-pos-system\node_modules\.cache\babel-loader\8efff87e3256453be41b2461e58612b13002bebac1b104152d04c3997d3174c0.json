{"ast": null, "code": "'use strict';\n\n// in old WebKit versions, `element.classList` is not an instance of global `DOMTokenList`\nvar documentCreateElement = require('../internals/document-create-element');\nvar classList = documentCreateElement('span').classList;\nvar DOMTokenListPrototype = classList && classList.constructor && classList.constructor.prototype;\nmodule.exports = DOMTokenListPrototype === Object.prototype ? undefined : DOMTokenListPrototype;", "map": {"version": 3, "names": ["documentCreateElement", "require", "classList", "DOMTokenListPrototype", "constructor", "prototype", "module", "exports", "Object", "undefined"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/dom-token-list-prototype.js"], "sourcesContent": ["'use strict';\n// in old WebKit versions, `element.classList` is not an instance of global `DOMTokenList`\nvar documentCreateElement = require('../internals/document-create-element');\n\nvar classList = documentCreateElement('span').classList;\nvar DOMTokenListPrototype = classList && classList.constructor && classList.constructor.prototype;\n\nmodule.exports = DOMTokenListPrototype === Object.prototype ? undefined : DOMTokenListPrototype;\n"], "mappings": "AAAA,YAAY;;AACZ;AACA,IAAIA,qBAAqB,GAAGC,OAAO,CAAC,sCAAsC,CAAC;AAE3E,IAAIC,SAAS,GAAGF,qBAAqB,CAAC,MAAM,CAAC,CAACE,SAAS;AACvD,IAAIC,qBAAqB,GAAGD,SAAS,IAAIA,SAAS,CAACE,WAAW,IAAIF,SAAS,CAACE,WAAW,CAACC,SAAS;AAEjGC,MAAM,CAACC,OAAO,GAAGJ,qBAAqB,KAAKK,MAAM,CAACH,SAAS,GAAGI,SAAS,GAAGN,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}