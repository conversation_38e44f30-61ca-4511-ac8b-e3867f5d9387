{"ast": null, "code": "'use strict';\n\nvar fails = require('../internals/fails');\nmodule.exports = !fails(function () {\n  function F() {/* empty */}\n  F.prototype.constructor = null;\n  // eslint-disable-next-line es/no-object-getprototypeof -- required for testing\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});", "map": {"version": 3, "names": ["fails", "require", "module", "exports", "F", "prototype", "constructor", "Object", "getPrototypeOf"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/correct-prototype-getter.js"], "sourcesContent": ["'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  function F() { /* empty */ }\n  F.prototype.constructor = null;\n  // eslint-disable-next-line es/no-object-getprototypeof -- required for testing\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAEzCC,MAAM,CAACC,OAAO,GAAG,CAACH,KAAK,CAAC,YAAY;EAClC,SAASI,CAACA,CAAA,EAAG,CAAE;EACfA,CAAC,CAACC,SAAS,CAACC,WAAW,GAAG,IAAI;EAC9B;EACA,OAAOC,MAAM,CAACC,cAAc,CAAC,IAAIJ,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAACC,SAAS;AACvD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}