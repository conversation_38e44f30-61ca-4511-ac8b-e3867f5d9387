{"ast": null, "code": "'use strict';\n\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  var len = toIntegerOrInfinity(argument);\n  return len > 0 ? min(len, 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};", "map": {"version": 3, "names": ["toIntegerOrInfinity", "require", "min", "Math", "module", "exports", "argument", "len"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/to-length.js"], "sourcesContent": ["'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  var len = toIntegerOrInfinity(argument);\n  return len > 0 ? min(len, 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,mBAAmB,GAAGC,OAAO,CAAC,qCAAqC,CAAC;AAExE,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG;;AAElB;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIC,GAAG,GAAGP,mBAAmB,CAACM,QAAQ,CAAC;EACvC,OAAOC,GAAG,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}