{"ast": null, "code": "'use strict';\n\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('(?<a>b)', 'g') -> /(?<a>b)/g and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\nmodule.exports = fails(function () {\n  var re = $RegExp('(?<a>b)', 'g');\n  return re.exec('b').groups.a !== 'b' || 'b'.replace(re, '$<a>c') !== 'bc';\n});", "map": {"version": 3, "names": ["fails", "require", "globalThis", "$RegExp", "RegExp", "module", "exports", "re", "exec", "groups", "a", "replace"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/regexp-unsupported-ncg.js"], "sourcesContent": ["'use strict';\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('(?<a>b)', 'g') -> /(?<a>b)/g and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\n\nmodule.exports = fails(function () {\n  var re = $RegExp('(?<a>b)', 'g');\n  return re.exec('b').groups.a !== 'b' ||\n    'b'.replace(re, '$<a>c') !== 'bc';\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;;AAEpD;AACA,IAAIE,OAAO,GAAGD,UAAU,CAACE,MAAM;AAE/BC,MAAM,CAACC,OAAO,GAAGN,KAAK,CAAC,YAAY;EACjC,IAAIO,EAAE,GAAGJ,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;EAChC,OAAOI,EAAE,CAACC,IAAI,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,CAAC,KAAK,GAAG,IAClC,GAAG,CAACC,OAAO,CAACJ,EAAE,EAAE,OAAO,CAAC,KAAK,IAAI;AACrC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}