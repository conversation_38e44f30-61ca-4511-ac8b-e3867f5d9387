{"ast": null, "code": "'use strict';\n\nvar toLength = require('../internals/to-length');\n\n// `LengthOfArrayLike` abstract operation\n// https://tc39.es/ecma262/#sec-lengthofarraylike\nmodule.exports = function (obj) {\n  return toLength(obj.length);\n};", "map": {"version": 3, "names": ["to<PERSON><PERSON><PERSON>", "require", "module", "exports", "obj", "length"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/length-of-array-like.js"], "sourcesContent": ["'use strict';\nvar toLength = require('../internals/to-length');\n\n// `LengthOfArrayLike` abstract operation\n// https://tc39.es/ecma262/#sec-lengthofarraylike\nmodule.exports = function (obj) {\n  return toLength(obj.length);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,wBAAwB,CAAC;;AAEhD;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC9B,OAAOJ,QAAQ,CAACI,GAAG,CAACC,MAAM,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}