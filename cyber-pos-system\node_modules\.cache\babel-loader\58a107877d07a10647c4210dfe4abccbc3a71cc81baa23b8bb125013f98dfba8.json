{"ast": null, "code": "'use strict';\n\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toIntegerOrInfinity(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};", "map": {"version": 3, "names": ["toIntegerOrInfinity", "require", "max", "Math", "min", "module", "exports", "index", "length", "integer"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/to-absolute-index.js"], "sourcesContent": ["'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toIntegerOrInfinity(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,mBAAmB,GAAGC,OAAO,CAAC,qCAAqC,CAAC;AAExE,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG;AAClB,IAAIE,GAAG,GAAGD,IAAI,CAACC,GAAG;;AAElB;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAEC,MAAM,EAAE;EACxC,IAAIC,OAAO,GAAGT,mBAAmB,CAACO,KAAK,CAAC;EACxC,OAAOE,OAAO,GAAG,CAAC,GAAGP,GAAG,CAACO,OAAO,GAAGD,MAAM,EAAE,CAAC,CAAC,GAAGJ,GAAG,CAACK,OAAO,EAAED,MAAM,CAAC;AACtE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}