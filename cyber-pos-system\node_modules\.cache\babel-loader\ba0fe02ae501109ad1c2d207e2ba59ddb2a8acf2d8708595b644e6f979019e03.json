{"ast": null, "code": "'use strict';\n\nvar call = require('../internals/function-call');\nvar hasOwn = require('../internals/has-own-property');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar regExpFlagsDetection = require('../internals/regexp-flags-detection');\nvar regExpFlagsGetterImplementation = require('../internals/regexp-flags');\nvar RegExpPrototype = RegExp.prototype;\nmodule.exports = regExpFlagsDetection.correct ? function (it) {\n  return it.flags;\n} : function (it) {\n  return !regExpFlagsDetection.correct && isPrototypeOf(RegExpPrototype, it) && !hasOwn(it, 'flags') ? call(regExpFlagsGetterImplementation, it) : it.flags;\n};", "map": {"version": 3, "names": ["call", "require", "hasOwn", "isPrototypeOf", "regExpFlagsDetection", "regExpFlagsGetterImplementation", "RegExpPrototype", "RegExp", "prototype", "module", "exports", "correct", "it", "flags"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/regexp-get-flags.js"], "sourcesContent": ["'use strict';\nvar call = require('../internals/function-call');\nvar hasOwn = require('../internals/has-own-property');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar regExpFlagsDetection = require('../internals/regexp-flags-detection');\nvar regExpFlagsGetterImplementation = require('../internals/regexp-flags');\n\nvar RegExpPrototype = RegExp.prototype;\n\nmodule.exports = regExpFlagsDetection.correct ? function (it) {\n  return it.flags;\n} : function (it) {\n  return (!regExpFlagsDetection.correct && isPrototypeOf(RegExpPrototype, it) && !hasOwn(it, 'flags'))\n    ? call(regExpFlagsGetterImplementation, it)\n    : it.flags;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIC,MAAM,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AACrD,IAAIE,aAAa,GAAGF,OAAO,CAAC,qCAAqC,CAAC;AAClE,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,qCAAqC,CAAC;AACzE,IAAII,+BAA+B,GAAGJ,OAAO,CAAC,2BAA2B,CAAC;AAE1E,IAAIK,eAAe,GAAGC,MAAM,CAACC,SAAS;AAEtCC,MAAM,CAACC,OAAO,GAAGN,oBAAoB,CAACO,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC5D,OAAOA,EAAE,CAACC,KAAK;AACjB,CAAC,GAAG,UAAUD,EAAE,EAAE;EAChB,OAAQ,CAACR,oBAAoB,CAACO,OAAO,IAAIR,aAAa,CAACG,eAAe,EAAEM,EAAE,CAAC,IAAI,CAACV,MAAM,CAACU,EAAE,EAAE,OAAO,CAAC,GAC/FZ,IAAI,CAACK,+BAA+B,EAAEO,EAAE,CAAC,GACzCA,EAAE,CAACC,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}