{"ast": null, "code": "'use strict';\n\nvar anObject = require('../internals/an-object');\n\n// `RegExp.prototype.flags` getter implementation\n// https://tc39.es/ecma262/#sec-get-regexp.prototype.flags\nmodule.exports = function () {\n  var that = anObject(this);\n  var result = '';\n  if (that.hasIndices) result += 'd';\n  if (that.global) result += 'g';\n  if (that.ignoreCase) result += 'i';\n  if (that.multiline) result += 'm';\n  if (that.dotAll) result += 's';\n  if (that.unicode) result += 'u';\n  if (that.unicodeSets) result += 'v';\n  if (that.sticky) result += 'y';\n  return result;\n};", "map": {"version": 3, "names": ["anObject", "require", "module", "exports", "that", "result", "hasIndices", "global", "ignoreCase", "multiline", "dotAll", "unicode", "unicodeSets", "sticky"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/regexp-flags.js"], "sourcesContent": ["'use strict';\nvar anObject = require('../internals/an-object');\n\n// `RegExp.prototype.flags` getter implementation\n// https://tc39.es/ecma262/#sec-get-regexp.prototype.flags\nmodule.exports = function () {\n  var that = anObject(this);\n  var result = '';\n  if (that.hasIndices) result += 'd';\n  if (that.global) result += 'g';\n  if (that.ignoreCase) result += 'i';\n  if (that.multiline) result += 'm';\n  if (that.dotAll) result += 's';\n  if (that.unicode) result += 'u';\n  if (that.unicodeSets) result += 'v';\n  if (that.sticky) result += 'y';\n  return result;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,wBAAwB,CAAC;;AAEhD;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,IAAIC,IAAI,GAAGJ,QAAQ,CAAC,IAAI,CAAC;EACzB,IAAIK,MAAM,GAAG,EAAE;EACf,IAAID,IAAI,CAACE,UAAU,EAAED,MAAM,IAAI,GAAG;EAClC,IAAID,IAAI,CAACG,MAAM,EAAEF,MAAM,IAAI,GAAG;EAC9B,IAAID,IAAI,CAACI,UAAU,EAAEH,MAAM,IAAI,GAAG;EAClC,IAAID,IAAI,CAACK,SAAS,EAAEJ,MAAM,IAAI,GAAG;EACjC,IAAID,IAAI,CAACM,MAAM,EAAEL,MAAM,IAAI,GAAG;EAC9B,IAAID,IAAI,CAACO,OAAO,EAAEN,MAAM,IAAI,GAAG;EAC/B,IAAID,IAAI,CAACQ,WAAW,EAAEP,MAAM,IAAI,GAAG;EACnC,IAAID,IAAI,CAACS,MAAM,EAAER,MAAM,IAAI,GAAG;EAC9B,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}