{"ast": null, "code": "'use strict';\n\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar IS_PURE = require('../internals/is-pure');\nvar FunctionName = require('../internals/function-name');\nvar isCallable = require('../internals/is-callable');\nvar createIteratorConstructor = require('../internals/iterator-create-constructor');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar Iterators = require('../internals/iterators');\nvar IteratorsCore = require('../internals/iterators-core');\nvar PROPER_FUNCTION_NAME = FunctionName.PROPER;\nvar CONFIGURABLE_FUNCTION_NAME = FunctionName.CONFIGURABLE;\nvar IteratorPrototype = IteratorsCore.IteratorPrototype;\nvar BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;\nvar ITERATOR = wellKnownSymbol('iterator');\nvar KEYS = 'keys';\nvar VALUES = 'values';\nvar ENTRIES = 'entries';\nvar returnThis = function () {\n  return this;\n};\nmodule.exports = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {\n  createIteratorConstructor(IteratorConstructor, NAME, next);\n  var getIterationMethod = function (KIND) {\n    if (KIND === DEFAULT && defaultIterator) return defaultIterator;\n    if (!BUGGY_SAFARI_ITERATORS && KIND && KIND in IterablePrototype) return IterablePrototype[KIND];\n    switch (KIND) {\n      case KEYS:\n        return function keys() {\n          return new IteratorConstructor(this, KIND);\n        };\n      case VALUES:\n        return function values() {\n          return new IteratorConstructor(this, KIND);\n        };\n      case ENTRIES:\n        return function entries() {\n          return new IteratorConstructor(this, KIND);\n        };\n    }\n    return function () {\n      return new IteratorConstructor(this);\n    };\n  };\n  var TO_STRING_TAG = NAME + ' Iterator';\n  var INCORRECT_VALUES_NAME = false;\n  var IterablePrototype = Iterable.prototype;\n  var nativeIterator = IterablePrototype[ITERATOR] || IterablePrototype['@@iterator'] || DEFAULT && IterablePrototype[DEFAULT];\n  var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);\n  var anyNativeIterator = NAME === 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;\n  var CurrentIteratorPrototype, methods, KEY;\n\n  // fix native\n  if (anyNativeIterator) {\n    CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));\n    if (CurrentIteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {\n      if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {\n        if (setPrototypeOf) {\n          setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);\n        } else if (!isCallable(CurrentIteratorPrototype[ITERATOR])) {\n          defineBuiltIn(CurrentIteratorPrototype, ITERATOR, returnThis);\n        }\n      }\n      // Set @@toStringTag to native iterators\n      setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);\n      if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;\n    }\n  }\n\n  // fix Array.prototype.{ values, @@iterator }.name in V8 / FF\n  if (PROPER_FUNCTION_NAME && DEFAULT === VALUES && nativeIterator && nativeIterator.name !== VALUES) {\n    if (!IS_PURE && CONFIGURABLE_FUNCTION_NAME) {\n      createNonEnumerableProperty(IterablePrototype, 'name', VALUES);\n    } else {\n      INCORRECT_VALUES_NAME = true;\n      defaultIterator = function values() {\n        return call(nativeIterator, this);\n      };\n    }\n  }\n\n  // export additional methods\n  if (DEFAULT) {\n    methods = {\n      values: getIterationMethod(VALUES),\n      keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),\n      entries: getIterationMethod(ENTRIES)\n    };\n    if (FORCED) for (KEY in methods) {\n      if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {\n        defineBuiltIn(IterablePrototype, KEY, methods[KEY]);\n      }\n    } else $({\n      target: NAME,\n      proto: true,\n      forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME\n    }, methods);\n  }\n\n  // define iterator\n  if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {\n    defineBuiltIn(IterablePrototype, ITERATOR, defaultIterator, {\n      name: DEFAULT\n    });\n  }\n  Iterators[NAME] = defaultIterator;\n  return methods;\n};", "map": {"version": 3, "names": ["$", "require", "call", "IS_PURE", "FunctionName", "isCallable", "createIteratorConstructor", "getPrototypeOf", "setPrototypeOf", "setToStringTag", "createNonEnumerableProperty", "defineBuiltIn", "wellKnownSymbol", "Iterators", "IteratorsCore", "PROPER_FUNCTION_NAME", "PROPER", "CONFIGURABLE_FUNCTION_NAME", "CONFIGURABLE", "IteratorPrototype", "BUGGY_SAFARI_ITERATORS", "ITERATOR", "KEYS", "VALUES", "ENTRIES", "returnThis", "module", "exports", "Iterable", "NAME", "IteratorConstructor", "next", "DEFAULT", "IS_SET", "FORCED", "getIterationMethod", "KIND", "defaultIterator", "IterablePrototype", "keys", "values", "entries", "TO_STRING_TAG", "INCORRECT_VALUES_NAME", "prototype", "nativeIterator", "anyNativeIterator", "CurrentIteratorPrototype", "methods", "KEY", "Object", "name", "target", "proto", "forced"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/iterator-define.js"], "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar IS_PURE = require('../internals/is-pure');\nvar FunctionName = require('../internals/function-name');\nvar isCallable = require('../internals/is-callable');\nvar createIteratorConstructor = require('../internals/iterator-create-constructor');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar Iterators = require('../internals/iterators');\nvar IteratorsCore = require('../internals/iterators-core');\n\nvar PROPER_FUNCTION_NAME = FunctionName.PROPER;\nvar CONFIGURABLE_FUNCTION_NAME = FunctionName.CONFIGURABLE;\nvar IteratorPrototype = IteratorsCore.IteratorPrototype;\nvar BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;\nvar ITERATOR = wellKnownSymbol('iterator');\nvar KEYS = 'keys';\nvar VALUES = 'values';\nvar ENTRIES = 'entries';\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {\n  createIteratorConstructor(IteratorConstructor, NAME, next);\n\n  var getIterationMethod = function (KIND) {\n    if (KIND === DEFAULT && defaultIterator) return defaultIterator;\n    if (!BUGGY_SAFARI_ITERATORS && KIND && KIND in IterablePrototype) return IterablePrototype[KIND];\n\n    switch (KIND) {\n      case KEYS: return function keys() { return new IteratorConstructor(this, KIND); };\n      case VALUES: return function values() { return new IteratorConstructor(this, KIND); };\n      case ENTRIES: return function entries() { return new IteratorConstructor(this, KIND); };\n    }\n\n    return function () { return new IteratorConstructor(this); };\n  };\n\n  var TO_STRING_TAG = NAME + ' Iterator';\n  var INCORRECT_VALUES_NAME = false;\n  var IterablePrototype = Iterable.prototype;\n  var nativeIterator = IterablePrototype[ITERATOR]\n    || IterablePrototype['@@iterator']\n    || DEFAULT && IterablePrototype[DEFAULT];\n  var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);\n  var anyNativeIterator = NAME === 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;\n  var CurrentIteratorPrototype, methods, KEY;\n\n  // fix native\n  if (anyNativeIterator) {\n    CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));\n    if (CurrentIteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {\n      if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {\n        if (setPrototypeOf) {\n          setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);\n        } else if (!isCallable(CurrentIteratorPrototype[ITERATOR])) {\n          defineBuiltIn(CurrentIteratorPrototype, ITERATOR, returnThis);\n        }\n      }\n      // Set @@toStringTag to native iterators\n      setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);\n      if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;\n    }\n  }\n\n  // fix Array.prototype.{ values, @@iterator }.name in V8 / FF\n  if (PROPER_FUNCTION_NAME && DEFAULT === VALUES && nativeIterator && nativeIterator.name !== VALUES) {\n    if (!IS_PURE && CONFIGURABLE_FUNCTION_NAME) {\n      createNonEnumerableProperty(IterablePrototype, 'name', VALUES);\n    } else {\n      INCORRECT_VALUES_NAME = true;\n      defaultIterator = function values() { return call(nativeIterator, this); };\n    }\n  }\n\n  // export additional methods\n  if (DEFAULT) {\n    methods = {\n      values: getIterationMethod(VALUES),\n      keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),\n      entries: getIterationMethod(ENTRIES)\n    };\n    if (FORCED) for (KEY in methods) {\n      if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {\n        defineBuiltIn(IterablePrototype, KEY, methods[KEY]);\n      }\n    } else $({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME }, methods);\n  }\n\n  // define iterator\n  if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {\n    defineBuiltIn(IterablePrototype, ITERATOR, defaultIterator, { name: DEFAULT });\n  }\n  Iterators[NAME] = defaultIterator;\n\n  return methods;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AACtC,IAAIC,IAAI,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIE,OAAO,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIG,YAAY,GAAGH,OAAO,CAAC,4BAA4B,CAAC;AACxD,IAAII,UAAU,GAAGJ,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIK,yBAAyB,GAAGL,OAAO,CAAC,0CAA0C,CAAC;AACnF,IAAIM,cAAc,GAAGN,OAAO,CAAC,sCAAsC,CAAC;AACpE,IAAIO,cAAc,GAAGP,OAAO,CAAC,sCAAsC,CAAC;AACpE,IAAIQ,cAAc,GAAGR,OAAO,CAAC,gCAAgC,CAAC;AAC9D,IAAIS,2BAA2B,GAAGT,OAAO,CAAC,6CAA6C,CAAC;AACxF,IAAIU,aAAa,GAAGV,OAAO,CAAC,8BAA8B,CAAC;AAC3D,IAAIW,eAAe,GAAGX,OAAO,CAAC,gCAAgC,CAAC;AAC/D,IAAIY,SAAS,GAAGZ,OAAO,CAAC,wBAAwB,CAAC;AACjD,IAAIa,aAAa,GAAGb,OAAO,CAAC,6BAA6B,CAAC;AAE1D,IAAIc,oBAAoB,GAAGX,YAAY,CAACY,MAAM;AAC9C,IAAIC,0BAA0B,GAAGb,YAAY,CAACc,YAAY;AAC1D,IAAIC,iBAAiB,GAAGL,aAAa,CAACK,iBAAiB;AACvD,IAAIC,sBAAsB,GAAGN,aAAa,CAACM,sBAAsB;AACjE,IAAIC,QAAQ,GAAGT,eAAe,CAAC,UAAU,CAAC;AAC1C,IAAIU,IAAI,GAAG,MAAM;AACjB,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,OAAO,GAAG,SAAS;AAEvB,IAAIC,UAAU,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,IAAI;AAAE,CAAC;AAE7CC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAEC,IAAI,EAAEC,mBAAmB,EAAEC,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAC7F5B,yBAAyB,CAACwB,mBAAmB,EAAED,IAAI,EAAEE,IAAI,CAAC;EAE1D,IAAII,kBAAkB,GAAG,SAAAA,CAAUC,IAAI,EAAE;IACvC,IAAIA,IAAI,KAAKJ,OAAO,IAAIK,eAAe,EAAE,OAAOA,eAAe;IAC/D,IAAI,CAACjB,sBAAsB,IAAIgB,IAAI,IAAIA,IAAI,IAAIE,iBAAiB,EAAE,OAAOA,iBAAiB,CAACF,IAAI,CAAC;IAEhG,QAAQA,IAAI;MACV,KAAKd,IAAI;QAAE,OAAO,SAASiB,IAAIA,CAAA,EAAG;UAAE,OAAO,IAAIT,mBAAmB,CAAC,IAAI,EAAEM,IAAI,CAAC;QAAE,CAAC;MACjF,KAAKb,MAAM;QAAE,OAAO,SAASiB,MAAMA,CAAA,EAAG;UAAE,OAAO,IAAIV,mBAAmB,CAAC,IAAI,EAAEM,IAAI,CAAC;QAAE,CAAC;MACrF,KAAKZ,OAAO;QAAE,OAAO,SAASiB,OAAOA,CAAA,EAAG;UAAE,OAAO,IAAIX,mBAAmB,CAAC,IAAI,EAAEM,IAAI,CAAC;QAAE,CAAC;IACzF;IAEA,OAAO,YAAY;MAAE,OAAO,IAAIN,mBAAmB,CAAC,IAAI,CAAC;IAAE,CAAC;EAC9D,CAAC;EAED,IAAIY,aAAa,GAAGb,IAAI,GAAG,WAAW;EACtC,IAAIc,qBAAqB,GAAG,KAAK;EACjC,IAAIL,iBAAiB,GAAGV,QAAQ,CAACgB,SAAS;EAC1C,IAAIC,cAAc,GAAGP,iBAAiB,CAACjB,QAAQ,CAAC,IAC3CiB,iBAAiB,CAAC,YAAY,CAAC,IAC/BN,OAAO,IAAIM,iBAAiB,CAACN,OAAO,CAAC;EAC1C,IAAIK,eAAe,GAAG,CAACjB,sBAAsB,IAAIyB,cAAc,IAAIV,kBAAkB,CAACH,OAAO,CAAC;EAC9F,IAAIc,iBAAiB,GAAGjB,IAAI,KAAK,OAAO,GAAGS,iBAAiB,CAACG,OAAO,IAAII,cAAc,GAAGA,cAAc;EACvG,IAAIE,wBAAwB,EAAEC,OAAO,EAAEC,GAAG;;EAE1C;EACA,IAAIH,iBAAiB,EAAE;IACrBC,wBAAwB,GAAGxC,cAAc,CAACuC,iBAAiB,CAAC5C,IAAI,CAAC,IAAI0B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjF,IAAImB,wBAAwB,KAAKG,MAAM,CAACN,SAAS,IAAIG,wBAAwB,CAAChB,IAAI,EAAE;MAClF,IAAI,CAAC5B,OAAO,IAAII,cAAc,CAACwC,wBAAwB,CAAC,KAAK5B,iBAAiB,EAAE;QAC9E,IAAIX,cAAc,EAAE;UAClBA,cAAc,CAACuC,wBAAwB,EAAE5B,iBAAiB,CAAC;QAC7D,CAAC,MAAM,IAAI,CAACd,UAAU,CAAC0C,wBAAwB,CAAC1B,QAAQ,CAAC,CAAC,EAAE;UAC1DV,aAAa,CAACoC,wBAAwB,EAAE1B,QAAQ,EAAEI,UAAU,CAAC;QAC/D;MACF;MACA;MACAhB,cAAc,CAACsC,wBAAwB,EAAEL,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;MACnE,IAAIvC,OAAO,EAAEU,SAAS,CAAC6B,aAAa,CAAC,GAAGjB,UAAU;IACpD;EACF;;EAEA;EACA,IAAIV,oBAAoB,IAAIiB,OAAO,KAAKT,MAAM,IAAIsB,cAAc,IAAIA,cAAc,CAACM,IAAI,KAAK5B,MAAM,EAAE;IAClG,IAAI,CAACpB,OAAO,IAAIc,0BAA0B,EAAE;MAC1CP,2BAA2B,CAAC4B,iBAAiB,EAAE,MAAM,EAAEf,MAAM,CAAC;IAChE,CAAC,MAAM;MACLoB,qBAAqB,GAAG,IAAI;MAC5BN,eAAe,GAAG,SAASG,MAAMA,CAAA,EAAG;QAAE,OAAOtC,IAAI,CAAC2C,cAAc,EAAE,IAAI,CAAC;MAAE,CAAC;IAC5E;EACF;;EAEA;EACA,IAAIb,OAAO,EAAE;IACXgB,OAAO,GAAG;MACRR,MAAM,EAAEL,kBAAkB,CAACZ,MAAM,CAAC;MAClCgB,IAAI,EAAEN,MAAM,GAAGI,eAAe,GAAGF,kBAAkB,CAACb,IAAI,CAAC;MACzDmB,OAAO,EAAEN,kBAAkB,CAACX,OAAO;IACrC,CAAC;IACD,IAAIU,MAAM,EAAE,KAAKe,GAAG,IAAID,OAAO,EAAE;MAC/B,IAAI5B,sBAAsB,IAAIuB,qBAAqB,IAAI,EAAEM,GAAG,IAAIX,iBAAiB,CAAC,EAAE;QAClF3B,aAAa,CAAC2B,iBAAiB,EAAEW,GAAG,EAAED,OAAO,CAACC,GAAG,CAAC,CAAC;MACrD;IACF,CAAC,MAAMjD,CAAC,CAAC;MAAEoD,MAAM,EAAEvB,IAAI;MAAEwB,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAElC,sBAAsB,IAAIuB;IAAsB,CAAC,EAAEK,OAAO,CAAC;EAC3G;;EAEA;EACA,IAAI,CAAC,CAAC7C,OAAO,IAAI+B,MAAM,KAAKI,iBAAiB,CAACjB,QAAQ,CAAC,KAAKgB,eAAe,EAAE;IAC3E1B,aAAa,CAAC2B,iBAAiB,EAAEjB,QAAQ,EAAEgB,eAAe,EAAE;MAAEc,IAAI,EAAEnB;IAAQ,CAAC,CAAC;EAChF;EACAnB,SAAS,CAACgB,IAAI,CAAC,GAAGQ,eAAe;EAEjC,OAAOW,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}