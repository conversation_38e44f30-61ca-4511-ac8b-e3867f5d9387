{"ast": null, "code": "'use strict';\n\n// a string of all valid unicode whitespaces\nmodule.exports = '\\u0009\\u000A\\u000B\\u000C\\u000D\\u0020\\u00A0\\u1680\\u2000\\u2001\\u2002' + '\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF';", "map": {"version": 3, "names": ["module", "exports"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/whitespaces.js"], "sourcesContent": ["'use strict';\n// a string of all valid unicode whitespaces\nmodule.exports = '\\u0009\\u000A\\u000B\\u000C\\u000D\\u0020\\u00A0\\u1680\\u2000\\u2001\\u2002' +\n  '\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF';\n"], "mappings": "AAAA,YAAY;;AACZ;AACAA,MAAM,CAACC,OAAO,GAAG,oEAAoE,GACnF,sFAAsF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}