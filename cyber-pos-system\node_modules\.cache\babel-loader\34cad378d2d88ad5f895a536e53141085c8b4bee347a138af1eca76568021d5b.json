{"ast": null, "code": "'use strict';\n\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar $Object = Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () {\n  return arguments;\n}()) === 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) {/* empty */}\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n  // @@toStringTag case\n  : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == 'string' ? tag\n  // builtinTag case\n  : CORRECT_ARGUMENTS ? classofRaw(O)\n  // ES3 arguments fallback\n  : (result = classofRaw(O)) === 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};", "map": {"version": 3, "names": ["TO_STRING_TAG_SUPPORT", "require", "isCallable", "classofRaw", "wellKnownSymbol", "TO_STRING_TAG", "$Object", "Object", "CORRECT_ARGUMENTS", "arguments", "tryGet", "it", "key", "error", "module", "exports", "O", "tag", "result", "undefined", "callee"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/classof.js"], "sourcesContent": ["'use strict';\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar $Object = Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) === 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) === 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,qBAAqB,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AACzE,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIG,eAAe,GAAGH,OAAO,CAAC,gCAAgC,CAAC;AAE/D,IAAII,aAAa,GAAGD,eAAe,CAAC,aAAa,CAAC;AAClD,IAAIE,OAAO,GAAGC,MAAM;;AAEpB;AACA,IAAIC,iBAAiB,GAAGL,UAAU,CAAC,YAAY;EAAE,OAAOM,SAAS;AAAE,CAAC,CAAC,CAAC,CAAC,KAAK,WAAW;;AAEvF;AACA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,EAAE,EAAEC,GAAG,EAAE;EAC9B,IAAI;IACF,OAAOD,EAAE,CAACC,GAAG,CAAC;EAChB,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAE;AACpB,CAAC;;AAED;AACAC,MAAM,CAACC,OAAO,GAAGf,qBAAqB,GAAGG,UAAU,GAAG,UAAUQ,EAAE,EAAE;EAClE,IAAIK,CAAC,EAAEC,GAAG,EAAEC,MAAM;EAClB,OAAOP,EAAE,KAAKQ,SAAS,GAAG,WAAW,GAAGR,EAAE,KAAK,IAAI,GAAG;EACpD;EAAA,EACE,QAAQM,GAAG,GAAGP,MAAM,CAACM,CAAC,GAAGV,OAAO,CAACK,EAAE,CAAC,EAAEN,aAAa,CAAC,CAAC,IAAI,QAAQ,GAAGY;EACtE;EAAA,EACET,iBAAiB,GAAGL,UAAU,CAACa,CAAC;EAClC;EAAA,EACE,CAACE,MAAM,GAAGf,UAAU,CAACa,CAAC,CAAC,MAAM,QAAQ,IAAId,UAAU,CAACc,CAAC,CAACI,MAAM,CAAC,GAAG,WAAW,GAAGF,MAAM;AAC1F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}