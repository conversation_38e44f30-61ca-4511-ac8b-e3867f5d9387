{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\settings\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Settings as SettingsIcon, Save, Users, Database } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport UserManagement from '../users/UserManagement';\nimport { initializeDemoData } from '../../utils/seedData';\nimport { seedTestData } from '../../utils/testDataSeeder';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const {\n    hasPermission,\n    currentUser\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('general');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const tabs = [{\n    id: 'general',\n    name: 'General',\n    icon: SettingsIcon\n  }, ...(hasPermission('admin') ? [{\n    id: 'users',\n    name: 'User Management',\n    icon: Users\n  }] : []), {\n    id: 'data',\n    name: 'Data Management',\n    icon: Database\n  }];\n  const handleInitializeDemo = async () => {\n    setLoading(true);\n    setMessage('');\n    try {\n      await initializeDemoData();\n      setMessage('Demo data initialized successfully!');\n    } catch (error) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSeedTestData = async () => {\n    if (!currentUser) {\n      setMessage('Error: User not authenticated');\n      return;\n    }\n    setLoading(true);\n    setMessage('');\n    try {\n      await seedTestData(currentUser.id);\n      setMessage('Test data with transactions seeded successfully! You can now view reports with sample data.');\n    } catch (error) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n            className: \"h-6 w-6 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"System Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"-mb-px flex space-x-8 px-6\",\n          children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab.id),\n            className: `${activeTab === tab.id ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`,\n            children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this), tab.name]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [activeTab === 'general' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"General Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Business Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  defaultValue: \"Cyber Services & Stationery\",\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Currency\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"KSh\",\n                    children: \"KSh (Kenyan Shilling)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"USD\",\n                    children: \"USD (US Dollar)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"EUR\",\n                    children: \"EUR (Euro)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Tax Rate (%)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  defaultValue: \"16\",\n                  step: \"0.01\",\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Receipt Footer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  defaultValue: \"Thank you for your business!\",\n                  rows: 3,\n                  className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Save, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this), \"Save General Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), activeTab === 'users' && hasPermission('admin') && /*#__PURE__*/_jsxDEV(UserManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this), activeTab === 'data' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Data Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(Database, {\n                    className: \"h-5 w-5 text-yellow-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-sm font-medium text-yellow-800\",\n                    children: \"Demo Data Initialization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2 text-sm text-yellow-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Initialize the system with demo services and products for testing purposes. This will add sample cyber services and stationery items to your database.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 153,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 rounded-md mb-4 ${message.includes('Error') ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-green-50 text-green-700 border border-green-200'}`,\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleInitializeDemo,\n              disabled: loading,\n              className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50\",\n              children: [/*#__PURE__*/_jsxDEV(Database, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), loading ? 'Initializing...' : 'Initialize Demo Data']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"l9zvFP268whCUdMILM5s2AzqV5k=\", false, function () {\n  return [useAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "Settings", "SettingsIcon", "Save", "Users", "Database", "useAuth", "UserManagement", "initializeDemoData", "seedTestData", "jsxDEV", "_jsxDEV", "_s", "hasPermission", "currentUser", "activeTab", "setActiveTab", "loading", "setLoading", "message", "setMessage", "tabs", "id", "name", "icon", "handleInitializeDemo", "error", "handleSeedTestData", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "tab", "onClick", "type", "defaultValue", "value", "step", "rows", "includes", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/settings/Settings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Settings as SettingsIcon, Save, Users, Database } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport UserManagement from '../users/UserManagement';\nimport { initializeDemoData } from '../../utils/seedData';\nimport { seedTestData } from '../../utils/testDataSeeder';\n\nconst Settings: React.FC = () => {\n  const { hasPermission, currentUser } = useAuth();\n  const [activeTab, setActiveTab] = useState('general');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const tabs = [\n    { id: 'general', name: 'General', icon: SettingsIcon },\n    ...(hasPermission('admin') ? [{ id: 'users', name: 'User Management', icon: Users }] : []),\n    { id: 'data', name: 'Data Management', icon: Database },\n  ];\n\n  const handleInitializeDemo = async () => {\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await initializeDemoData();\n      setMessage('Demo data initialized successfully!');\n    } catch (error: any) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSeedTestData = async () => {\n    if (!currentUser) {\n      setMessage('Error: User not authenticated');\n      return;\n    }\n\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await seedTestData(currentUser.id);\n      setMessage('Test data with transactions seeded successfully! You can now view reports with sample data.');\n    } catch (error: any) {\n      setMessage(`Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <SettingsIcon className=\"h-6 w-6 text-primary-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">System Settings</h1>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`${\n                  activeTab === tab.id\n                    ? 'border-primary-500 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}\n              >\n                <tab.icon className=\"h-4 w-4 mr-2\" />\n                {tab.name}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'general' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">General Settings</h3>\n                <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Business Name</label>\n                    <input\n                      type=\"text\"\n                      defaultValue=\"Cyber Services & Stationery\"\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Currency</label>\n                    <select className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\">\n                      <option value=\"KSh\">KSh (Kenyan Shilling)</option>\n                      <option value=\"USD\">USD (US Dollar)</option>\n                      <option value=\"EUR\">EUR (Euro)</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Tax Rate (%)</label>\n                    <input\n                      type=\"number\"\n                      defaultValue=\"16\"\n                      step=\"0.01\"\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Receipt Footer</label>\n                    <textarea\n                      defaultValue=\"Thank you for your business!\"\n                      rows={3}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <button className=\"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\">\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    Save General Settings\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'users' && hasPermission('admin') && (\n            <UserManagement />\n          )}\n\n          {activeTab === 'data' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Data Management</h3>\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6\">\n                  <div className=\"flex\">\n                    <div className=\"flex-shrink-0\">\n                      <Database className=\"h-5 w-5 text-yellow-400\" />\n                    </div>\n                    <div className=\"ml-3\">\n                      <h3 className=\"text-sm font-medium text-yellow-800\">\n                        Demo Data Initialization\n                      </h3>\n                      <div className=\"mt-2 text-sm text-yellow-700\">\n                        <p>\n                          Initialize the system with demo services and products for testing purposes.\n                          This will add sample cyber services and stationery items to your database.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {message && (\n                  <div className={`p-4 rounded-md mb-4 ${\n                    message.includes('Error')\n                      ? 'bg-red-50 text-red-700 border border-red-200'\n                      : 'bg-green-50 text-green-700 border border-green-200'\n                  }`}>\n                    {message}\n                  </div>\n                )}\n\n                <button\n                  onClick={handleInitializeDemo}\n                  disabled={loading}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50\"\n                >\n                  <Database className=\"h-4 w-4 mr-2\" />\n                  {loading ? 'Initializing...' : 'Initialize Demo Data'}\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,IAAIC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,cAAc;AAC9E,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMV,QAAkB,GAAGA,CAAA,KAAM;EAAAW,EAAA;EAC/B,MAAM;IAAEC,aAAa;IAAEC;EAAY,CAAC,GAAGR,OAAO,CAAC,CAAC;EAChD,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMqB,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAEtB;EAAa,CAAC,EACtD,IAAIW,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC;IAAES,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAEpB;EAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAC1F;IAAEkB,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAEnB;EAAS,CAAC,CACxD;EAED,MAAMoB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvCP,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMZ,kBAAkB,CAAC,CAAC;MAC1BY,UAAU,CAAC,qCAAqC,CAAC;IACnD,CAAC,CAAC,OAAOM,KAAU,EAAE;MACnBN,UAAU,CAAC,UAAUM,KAAK,CAACP,OAAO,EAAE,CAAC;IACvC,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACb,WAAW,EAAE;MAChBM,UAAU,CAAC,+BAA+B,CAAC;MAC3C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMX,YAAY,CAACK,WAAW,CAACQ,EAAE,CAAC;MAClCF,UAAU,CAAC,6FAA6F,CAAC;IAC3G,CAAC,CAAC,OAAOM,KAAU,EAAE;MACnBN,UAAU,CAAC,UAAUM,KAAK,CAACP,OAAO,EAAE,CAAC;IACvC,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEP,OAAA;IAAKiB,SAAS,EAAC,WAAW;IAAAC,QAAA,eAExBlB,OAAA;MAAKiB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzClB,OAAA;QAAKiB,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDlB,OAAA;UAAKiB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClB,OAAA,CAACT,YAAY;YAAC0B,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DtB,OAAA;YAAIiB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKiB,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eACvClB,OAAA;UAAKiB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACxCR,IAAI,CAACa,GAAG,CAAEC,GAAG,iBACZxB,OAAA;YAEEyB,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACmB,GAAG,CAACb,EAAE,CAAE;YACpCM,SAAS,EAAE,GACTb,SAAS,KAAKoB,GAAG,CAACb,EAAE,GAChB,qCAAqC,GACrC,4EAA4E,+EACF;YAAAO,QAAA,gBAEhFlB,OAAA,CAACwB,GAAG,CAACX,IAAI;cAACI,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACpCE,GAAG,CAACZ,IAAI;UAAA,GATJY,GAAG,CAACb,EAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUL,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKiB,SAAS,EAAC,KAAK;QAAAC,QAAA,GACjBd,SAAS,KAAK,SAAS,iBACtBJ,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBlB,OAAA;YAAAkB,QAAA,gBACElB,OAAA;cAAIiB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EtB,OAAA;cAAKiB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlB,OAAA;gBAAAkB,QAAA,gBACElB,OAAA;kBAAOiB,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChFtB,OAAA;kBACE0B,IAAI,EAAC,MAAM;kBACXC,YAAY,EAAC,6BAA6B;kBAC1CV,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtB,OAAA;gBAAAkB,QAAA,gBACElB,OAAA;kBAAOiB,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3EtB,OAAA;kBAAQiB,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,gBAClJlB,OAAA;oBAAQ4B,KAAK,EAAC,KAAK;oBAAAV,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClDtB,OAAA;oBAAQ4B,KAAK,EAAC,KAAK;oBAAAV,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtB,OAAA;oBAAQ4B,KAAK,EAAC,KAAK;oBAAAV,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNtB,OAAA;gBAAAkB,QAAA,gBACElB,OAAA;kBAAOiB,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/EtB,OAAA;kBACE0B,IAAI,EAAC,QAAQ;kBACbC,YAAY,EAAC,IAAI;kBACjBE,IAAI,EAAC,MAAM;kBACXZ,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtB,OAAA;gBAAAkB,QAAA,gBACElB,OAAA;kBAAOiB,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjFtB,OAAA;kBACE2B,YAAY,EAAC,8BAA8B;kBAC3CG,IAAI,EAAE,CAAE;kBACRb,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtB,OAAA;cAAKiB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBlB,OAAA;gBAAQiB,SAAS,EAAC,uFAAuF;gBAAAC,QAAA,gBACvGlB,OAAA,CAACR,IAAI;kBAACyB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,yBAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAlB,SAAS,KAAK,OAAO,IAAIF,aAAa,CAAC,OAAO,CAAC,iBAC9CF,OAAA,CAACJ,cAAc;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClB,EAEAlB,SAAS,KAAK,MAAM,iBACnBJ,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBlB,OAAA;YAAAkB,QAAA,gBACElB,OAAA;cAAIiB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EtB,OAAA;cAAKiB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,eACxElB,OAAA;gBAAKiB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBlB,OAAA;kBAAKiB,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BlB,OAAA,CAACN,QAAQ;oBAACuB,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNtB,OAAA;kBAAKiB,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBlB,OAAA;oBAAIiB,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEpD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLtB,OAAA;oBAAKiB,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,eAC3ClB,OAAA;sBAAAkB,QAAA,EAAG;oBAGH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELd,OAAO,iBACNR,OAAA;cAAKiB,SAAS,EAAE,uBACdT,OAAO,CAACuB,QAAQ,CAAC,OAAO,CAAC,GACrB,8CAA8C,GAC9C,oDAAoD,EACvD;cAAAb,QAAA,EACAV;YAAO;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,eAEDtB,OAAA;cACEyB,OAAO,EAAEX,oBAAqB;cAC9BkB,QAAQ,EAAE1B,OAAQ;cAClBW,SAAS,EAAC,0KAA0K;cAAAC,QAAA,gBAEpLlB,OAAA,CAACN,QAAQ;gBAACuB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACpChB,OAAO,GAAG,iBAAiB,GAAG,sBAAsB;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CAnLIX,QAAkB;EAAA,QACiBK,OAAO;AAAA;AAAAsC,EAAA,GAD1C3C,QAAkB;AAqLxB,eAAeA,QAAQ;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}