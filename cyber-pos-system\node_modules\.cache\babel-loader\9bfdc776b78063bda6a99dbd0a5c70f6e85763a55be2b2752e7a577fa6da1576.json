{"ast": null, "code": "'use strict';\n\nvar isPossiblePrototype = require('../internals/is-possible-prototype');\nvar $String = String;\nvar $TypeError = TypeError;\nmodule.exports = function (argument) {\n  if (isPossiblePrototype(argument)) return argument;\n  throw new $TypeError(\"Can't set \" + $String(argument) + ' as a prototype');\n};", "map": {"version": 3, "names": ["isPossiblePrototype", "require", "$String", "String", "$TypeError", "TypeError", "module", "exports", "argument"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/a-possible-prototype.js"], "sourcesContent": ["'use strict';\nvar isPossiblePrototype = require('../internals/is-possible-prototype');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\nmodule.exports = function (argument) {\n  if (isPossiblePrototype(argument)) return argument;\n  throw new $TypeError(\"Can't set \" + $String(argument) + ' as a prototype');\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,mBAAmB,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAEvE,IAAIC,OAAO,GAAGC,MAAM;AACpB,IAAIC,UAAU,GAAGC,SAAS;AAE1BC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIR,mBAAmB,CAACQ,QAAQ,CAAC,EAAE,OAAOA,QAAQ;EAClD,MAAM,IAAIJ,UAAU,CAAC,YAAY,GAAGF,OAAO,CAACM,QAAQ,CAAC,GAAG,iBAAiB,CAAC;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}