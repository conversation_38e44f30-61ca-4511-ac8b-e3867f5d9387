{"ast": null, "code": "'use strict';\n\nvar isCallable = require('../internals/is-callable');\nvar definePropertyModule = require('../internals/object-define-property');\nvar makeBuiltIn = require('../internals/make-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\nmodule.exports = function (O, key, value, options) {\n  if (!options) options = {};\n  var simple = options.enumerable;\n  var name = options.name !== undefined ? options.name : key;\n  if (isCallable(value)) makeBuiltIn(value, name, options);\n  if (options.global) {\n    if (simple) O[key] = value;else defineGlobalProperty(key, value);\n  } else {\n    try {\n      if (!options.unsafe) delete O[key];else if (O[key]) simple = true;\n    } catch (error) {/* empty */}\n    if (simple) O[key] = value;else definePropertyModule.f(O, key, {\n      value: value,\n      enumerable: false,\n      configurable: !options.nonConfigurable,\n      writable: !options.nonWritable\n    });\n  }\n  return O;\n};", "map": {"version": 3, "names": ["isCallable", "require", "definePropertyModule", "makeBuiltIn", "defineGlobalProperty", "module", "exports", "O", "key", "value", "options", "simple", "enumerable", "name", "undefined", "global", "unsafe", "error", "f", "configurable", "nonConfigurable", "writable", "nonWritable"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/define-built-in.js"], "sourcesContent": ["'use strict';\nvar isCallable = require('../internals/is-callable');\nvar definePropertyModule = require('../internals/object-define-property');\nvar makeBuiltIn = require('../internals/make-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nmodule.exports = function (O, key, value, options) {\n  if (!options) options = {};\n  var simple = options.enumerable;\n  var name = options.name !== undefined ? options.name : key;\n  if (isCallable(value)) makeBuiltIn(value, name, options);\n  if (options.global) {\n    if (simple) O[key] = value;\n    else defineGlobalProperty(key, value);\n  } else {\n    try {\n      if (!options.unsafe) delete O[key];\n      else if (O[key]) simple = true;\n    } catch (error) { /* empty */ }\n    if (simple) O[key] = value;\n    else definePropertyModule.f(O, key, {\n      value: value,\n      enumerable: false,\n      configurable: !options.nonConfigurable,\n      writable: !options.nonWritable\n    });\n  } return O;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,qCAAqC,CAAC;AACzE,IAAIE,WAAW,GAAGF,OAAO,CAAC,4BAA4B,CAAC;AACvD,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,qCAAqC,CAAC;AAEzEI,MAAM,CAACC,OAAO,GAAG,UAAUC,CAAC,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACjD,IAAI,CAACA,OAAO,EAAEA,OAAO,GAAG,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAGD,OAAO,CAACE,UAAU;EAC/B,IAAIC,IAAI,GAAGH,OAAO,CAACG,IAAI,KAAKC,SAAS,GAAGJ,OAAO,CAACG,IAAI,GAAGL,GAAG;EAC1D,IAAIR,UAAU,CAACS,KAAK,CAAC,EAAEN,WAAW,CAACM,KAAK,EAAEI,IAAI,EAAEH,OAAO,CAAC;EACxD,IAAIA,OAAO,CAACK,MAAM,EAAE;IAClB,IAAIJ,MAAM,EAAEJ,CAAC,CAACC,GAAG,CAAC,GAAGC,KAAK,CAAC,KACtBL,oBAAoB,CAACI,GAAG,EAAEC,KAAK,CAAC;EACvC,CAAC,MAAM;IACL,IAAI;MACF,IAAI,CAACC,OAAO,CAACM,MAAM,EAAE,OAAOT,CAAC,CAACC,GAAG,CAAC,CAAC,KAC9B,IAAID,CAAC,CAACC,GAAG,CAAC,EAAEG,MAAM,GAAG,IAAI;IAChC,CAAC,CAAC,OAAOM,KAAK,EAAE,CAAE;IAClB,IAAIN,MAAM,EAAEJ,CAAC,CAACC,GAAG,CAAC,GAAGC,KAAK,CAAC,KACtBP,oBAAoB,CAACgB,CAAC,CAACX,CAAC,EAAEC,GAAG,EAAE;MAClCC,KAAK,EAAEA,KAAK;MACZG,UAAU,EAAE,KAAK;MACjBO,YAAY,EAAE,CAACT,OAAO,CAACU,eAAe;MACtCC,QAAQ,EAAE,CAACX,OAAO,CAACY;IACrB,CAAC,CAAC;EACJ;EAAE,OAAOf,CAAC;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}