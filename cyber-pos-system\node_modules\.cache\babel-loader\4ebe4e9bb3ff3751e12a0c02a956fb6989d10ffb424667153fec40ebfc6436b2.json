{"ast": null, "code": "'use strict';\n\nvar charAt = require('../internals/string-multibyte').charAt;\n\n// `AdvanceStringIndex` abstract operation\n// https://tc39.es/ecma262/#sec-advancestringindex\nmodule.exports = function (S, index, unicode) {\n  return index + (unicode ? charAt(S, index).length : 1);\n};", "map": {"version": 3, "names": ["char<PERSON>t", "require", "module", "exports", "S", "index", "unicode", "length"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/advance-string-index.js"], "sourcesContent": ["'use strict';\nvar charAt = require('../internals/string-multibyte').charAt;\n\n// `AdvanceStringIndex` abstract operation\n// https://tc39.es/ecma262/#sec-advancestringindex\nmodule.exports = function (S, index, unicode) {\n  return index + (unicode ? charAt(S, index).length : 1);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,+BAA+B,CAAC,CAACD,MAAM;;AAE5D;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,UAAUC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAC5C,OAAOD,KAAK,IAAIC,OAAO,GAAGN,MAAM,CAACI,CAAC,EAAEC,KAAK,CAAC,CAACE,MAAM,GAAG,CAAC,CAAC;AACxD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}