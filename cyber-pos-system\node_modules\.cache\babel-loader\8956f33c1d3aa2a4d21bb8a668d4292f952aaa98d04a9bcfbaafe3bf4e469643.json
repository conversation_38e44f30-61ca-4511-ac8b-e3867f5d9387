{"ast": null, "code": "import { initializeApp } from 'firebase/app';\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\nimport { getFirestore, enableNetwork, disableNetwork, persistentLocalCache } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\n// Firebase configuration\n// For demo purposes, using Firebase emulator\n// TODO: Replace with your actual Firebase config for production\nconst firebaseConfig = {\n  apiKey: \"demo-api-key\",\n  authDomain: \"demo-project.firebaseapp.com\",\n  projectId: \"demo-cyber-pos\",\n  storageBucket: \"demo-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"demo-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services with modern persistence\nexport const auth = getAuth(app);\nexport const db = getFirestore(app, {\n  localCache: persistentLocalCache({\n    // Default tab manager supports multiple tabs automatically\n  })\n});\nexport const storage = getStorage(app);\n\n// Connect to emulators in development\nif (process.env.NODE_ENV === 'development') {\n  try {\n    // Only connect if not already connected\n    // Check if emulator is already connected by trying to connect\n    connectAuthEmulator(auth, 'http://localhost:9099');\n    // Note: Firestore emulator connection would go here if using emulator\n    // connectFirestoreEmulator(db, 'localhost', 8080);\n  } catch (error) {\n    console.log('Emulators not available, using production Firebase');\n  }\n}\n\n// Offline persistence is now automatically enabled through FirestoreSettings.localCache\n// No need for a separate enableOfflineSupport function with the new approach\nexport const enableOfflineSupport = async () => {\n  // This function is kept for backward compatibility but is no longer needed\n  // Persistence is automatically enabled through the localCache configuration above\n  console.log('Firebase offline persistence is automatically enabled with the new configuration');\n};\n\n// Network status management\nexport const goOffline = async () => {\n  try {\n    await disableNetwork(db);\n    console.log('Firebase network disabled');\n  } catch (error) {\n    console.error('Error disabling network:', error);\n  }\n};\nexport const goOnline = async () => {\n  try {\n    await enableNetwork(db);\n    console.log('Firebase network enabled');\n  } catch (error) {\n    console.error('Error enabling network:', error);\n  }\n};\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "connectAuthEmulator", "getFirestore", "enableNetwork", "disableNetwork", "persistentLocalCache", "getStorage", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db", "localCache", "storage", "process", "env", "NODE_ENV", "error", "console", "log", "enableOfflineSupport", "goOffline", "goOnline"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/config/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth, connectAuthEmulator } from 'firebase/auth';\nimport {\n  getFirestore,\n  enableNetwork,\n  disableNetwork,\n  connectFirestoreEmulator,\n  persistentLocalCache\n} from 'firebase/firestore';\nimport { getStorage, connectStorageEmulator } from 'firebase/storage';\n\n// Firebase configuration\n// For demo purposes, using Firebase emulator\n// TODO: Replace with your actual Firebase config for production\nconst firebaseConfig = {\n  apiKey: \"demo-api-key\",\n  authDomain: \"demo-project.firebaseapp.com\",\n  projectId: \"demo-cyber-pos\",\n  storageBucket: \"demo-project.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"demo-app-id\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services with modern persistence\nexport const auth = getAuth(app);\nexport const db = getFirestore(app, {\n  localCache: persistentLocalCache({\n    // Default tab manager supports multiple tabs automatically\n  })\n});\nexport const storage = getStorage(app);\n\n// Connect to emulators in development\nif (process.env.NODE_ENV === 'development') {\n  try {\n    // Only connect if not already connected\n    // Check if emulator is already connected by trying to connect\n    connectAuthEmulator(auth, 'http://localhost:9099');\n    // Note: Firestore emulator connection would go here if using emulator\n    // connectFirestoreEmulator(db, 'localhost', 8080);\n  } catch (error) {\n    console.log('Emulators not available, using production Firebase');\n  }\n}\n\n// Offline persistence is now automatically enabled through FirestoreSettings.localCache\n// No need for a separate enableOfflineSupport function with the new approach\nexport const enableOfflineSupport = async () => {\n  // This function is kept for backward compatibility but is no longer needed\n  // Persistence is automatically enabled through the localCache configuration above\n  console.log('Firebase offline persistence is automatically enabled with the new configuration');\n};\n\n// Network status management\nexport const goOffline = async () => {\n  try {\n    await disableNetwork(db);\n    console.log('Firebase network disabled');\n  } catch (error) {\n    console.error('Error disabling network:', error);\n  }\n};\n\nexport const goOnline = async () => {\n  try {\n    await enableNetwork(db);\n    console.log('Firebase network enabled');\n  } catch (error) {\n    console.error('Error enabling network:', error);\n  }\n};\n\nexport default app;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,eAAe;AAC5D,SACEC,YAAY,EACZC,aAAa,EACbC,cAAc,EAEdC,oBAAoB,QACf,oBAAoB;AAC3B,SAASC,UAAU,QAAgC,kBAAkB;;AAErE;AACA;AACA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,cAAc;EACtBC,UAAU,EAAE,8BAA8B;EAC1CC,SAAS,EAAE,gBAAgB;EAC3BC,aAAa,EAAE,0BAA0B;EACzCC,iBAAiB,EAAE,WAAW;EAC9BC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGf,aAAa,CAACQ,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMQ,IAAI,GAAGf,OAAO,CAACc,GAAG,CAAC;AAChC,OAAO,MAAME,EAAE,GAAGd,YAAY,CAACY,GAAG,EAAE;EAClCG,UAAU,EAAEZ,oBAAoB,CAAC;IAC/B;EAAA,CACD;AACH,CAAC,CAAC;AACF,OAAO,MAAMa,OAAO,GAAGZ,UAAU,CAACQ,GAAG,CAAC;;AAEtC;AACA,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1C,IAAI;IACF;IACA;IACApB,mBAAmB,CAACc,IAAI,EAAE,uBAAuB,CAAC;IAClD;IACA;EACF,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EACnE;AACF;;AAEA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C;EACA;EACAF,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;AACjG,CAAC;;AAED;AACA,OAAO,MAAME,SAAS,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACF,MAAMtB,cAAc,CAACY,EAAE,CAAC;IACxBO,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAC1C,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;EAClD;AACF,CAAC;AAED,OAAO,MAAMK,QAAQ,GAAG,MAAAA,CAAA,KAAY;EAClC,IAAI;IACF,MAAMxB,aAAa,CAACa,EAAE,CAAC;IACvBO,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;EACjD;AACF,CAAC;AAED,eAAeR,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}