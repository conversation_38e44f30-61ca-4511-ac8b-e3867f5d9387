[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "27", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx": "28", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx": "29", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx": "30", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx": "31", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts": "32", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts": "33", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts": "34", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts": "35", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx": "36"}, {"size": 362, "mtime": 1751001516282, "results": "37", "hashOfConfig": "38"}, {"size": 550, "mtime": 1751002257124, "results": "39", "hashOfConfig": "38"}, {"size": 2402, "mtime": 1751003134738, "results": "40", "hashOfConfig": "38"}, {"size": 5912, "mtime": 1751002889593, "results": "41", "hashOfConfig": "38"}, {"size": 6144, "mtime": 1751003013744, "results": "42", "hashOfConfig": "38"}, {"size": 5937, "mtime": 1751001851404, "results": "43", "hashOfConfig": "38"}, {"size": 8045, "mtime": 1751003816943, "results": "44", "hashOfConfig": "38"}, {"size": 26580, "mtime": 1751008212115, "results": "45", "hashOfConfig": "38"}, {"size": 8562, "mtime": 1751007249570, "results": "46", "hashOfConfig": "38"}, {"size": 16613, "mtime": 1751004208648, "results": "47", "hashOfConfig": "38"}, {"size": 6483, "mtime": 1751001825178, "results": "48", "hashOfConfig": "38"}, {"size": 13480, "mtime": 1751003649429, "results": "49", "hashOfConfig": "38"}, {"size": 665, "mtime": 1751001779724, "results": "50", "hashOfConfig": "38"}, {"size": 2525, "mtime": 1751005564372, "results": "51", "hashOfConfig": "38"}, {"size": 7270, "mtime": 1751002967950, "results": "52", "hashOfConfig": "38"}, {"size": 12716, "mtime": 1751002934527, "results": "53", "hashOfConfig": "38"}, {"size": 4406, "mtime": 1751003274192, "results": "54", "hashOfConfig": "38"}, {"size": 9054, "mtime": 1751003411814, "results": "55", "hashOfConfig": "38"}, {"size": 3964, "mtime": 1751003442458, "results": "56", "hashOfConfig": "38"}, {"size": 8498, "mtime": 1751003577995, "results": "57", "hashOfConfig": "38"}, {"size": 5162, "mtime": 1751003518513, "results": "58", "hashOfConfig": "38"}, {"size": 6079, "mtime": 1751003779872, "results": "59", "hashOfConfig": "38"}, {"size": 5107, "mtime": 1751003753867, "results": "60", "hashOfConfig": "38"}, {"size": 15195, "mtime": 1751005588991, "results": "61", "hashOfConfig": "38"}, {"size": 13401, "mtime": 1751003915007, "results": "62", "hashOfConfig": "38"}, {"size": 10910, "mtime": 1751003957303, "results": "63", "hashOfConfig": "38"}, {"size": 11914, "mtime": 1751004034012, "results": "64", "hashOfConfig": "38"}, {"size": 12558, "mtime": 1751004256417, "results": "65", "hashOfConfig": "38"}, {"size": 11536, "mtime": 1751004300206, "results": "66", "hashOfConfig": "38"}, {"size": 10775, "mtime": 1751004342324, "results": "67", "hashOfConfig": "38"}, {"size": 12479, "mtime": 1751004391960, "results": "68", "hashOfConfig": "38"}, {"size": 5054, "mtime": 1751005576405, "results": "69", "hashOfConfig": "38"}, {"size": 7130, "mtime": 1751005110286, "results": "70", "hashOfConfig": "38"}, {"size": 10667, "mtime": 1751006877684, "results": "71", "hashOfConfig": "38"}, {"size": 7956, "mtime": 1751007184267, "results": "72", "hashOfConfig": "38"}, {"size": 11201, "mtime": 1751006931474, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["182"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["183"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["184", "185", "186", "187", "188", "189", "190", "191"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", ["192", "193", "194"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", ["195", "196", "197", "198", "199"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["200", "201", "202", "203"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["204", "205"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["206", "207", "208"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["209", "210"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["211", "212"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["213"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", ["214", "215"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["216", "217"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["218"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["219"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts", ["220", "221", "222", "223", "224", "225"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx", ["226", "227"], [], {"ruleId": "228", "severity": 1, "message": "229", "line": 17, "column": 7, "nodeType": "230", "messageId": "231", "endLine": 17, "endColumn": 62}, {"ruleId": "228", "severity": 1, "message": "232", "line": 16, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 16, "endColumn": 8}, {"ruleId": "228", "severity": 1, "message": "233", "line": 10, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 10, "endColumn": 7}, {"ruleId": "228", "severity": 1, "message": "234", "line": 11, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 11, "endColumn": 13}, {"ruleId": "228", "severity": 1, "message": "235", "line": 12, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 12, "endColumn": 10}, {"ruleId": "228", "severity": 1, "message": "236", "line": 13, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 13, "endColumn": 9}, {"ruleId": "228", "severity": 1, "message": "237", "line": 14, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 14, "endColumn": 7}, {"ruleId": "228", "severity": 1, "message": "238", "line": 15, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 15, "endColumn": 8}, {"ruleId": "228", "severity": 1, "message": "239", "line": 21, "column": 10, "nodeType": "230", "messageId": "231", "endLine": 21, "endColumn": 17}, {"ruleId": "228", "severity": 1, "message": "240", "line": 21, "column": 19, "nodeType": "230", "messageId": "231", "endLine": 21, "endColumn": 26}, {"ruleId": "228", "severity": 1, "message": "241", "line": 11, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 11, "endColumn": 9}, {"ruleId": "228", "severity": 1, "message": "242", "line": 26, "column": 10, "nodeType": "230", "messageId": "231", "endLine": 26, "endColumn": 13}, {"ruleId": "243", "severity": 1, "message": "244", "line": 57, "column": 6, "nodeType": "245", "endLine": 57, "endColumn": 22, "suggestions": "246"}, {"ruleId": "228", "severity": 1, "message": "241", "line": 6, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 6, "endColumn": 9}, {"ruleId": "228", "severity": 1, "message": "247", "line": 9, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 9, "endColumn": 15}, {"ruleId": "228", "severity": 1, "message": "248", "line": 10, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 10, "endColumn": 12}, {"ruleId": "228", "severity": 1, "message": "249", "line": 14, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 14, "endColumn": 11}, {"ruleId": "228", "severity": 1, "message": "250", "line": 15, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 15, "endColumn": 9}, {"ruleId": "228", "severity": 1, "message": "251", "line": 9, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 9, "endColumn": 13}, {"ruleId": "228", "severity": 1, "message": "252", "line": 10, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 10, "endColumn": 6}, {"ruleId": "228", "severity": 1, "message": "253", "line": 14, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 14, "endColumn": 4}, {"ruleId": "228", "severity": 1, "message": "254", "line": 15, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 15, "endColumn": 7}, {"ruleId": "228", "severity": 1, "message": "255", "line": 7, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 7, "endColumn": 27}, {"ruleId": "228", "severity": 1, "message": "256", "line": 10, "column": 22, "nodeType": "230", "messageId": "231", "endLine": 10, "endColumn": 44}, {"ruleId": "228", "severity": 1, "message": "233", "line": 13, "column": 10, "nodeType": "230", "messageId": "231", "endLine": 13, "endColumn": 14}, {"ruleId": "228", "severity": 1, "message": "239", "line": 13, "column": 16, "nodeType": "230", "messageId": "231", "endLine": 13, "endColumn": 23}, {"ruleId": "228", "severity": 1, "message": "240", "line": 13, "column": 25, "nodeType": "230", "messageId": "231", "endLine": 13, "endColumn": 32}, {"ruleId": "228", "severity": 1, "message": "236", "line": 8, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 8, "endColumn": 9}, {"ruleId": "243", "severity": 1, "message": "257", "line": 34, "column": 6, "nodeType": "245", "endLine": 34, "endColumn": 8, "suggestions": "258"}, {"ruleId": "228", "severity": 1, "message": "259", "line": 5, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 5, "endColumn": 10}, {"ruleId": "228", "severity": 1, "message": "232", "line": 11, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 11, "endColumn": 8}, {"ruleId": "228", "severity": 1, "message": "252", "line": 2, "column": 25, "nodeType": "230", "messageId": "231", "endLine": 2, "endColumn": 28}, {"ruleId": "228", "severity": 1, "message": "259", "line": 5, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 5, "endColumn": 10}, {"ruleId": "228", "severity": 1, "message": "232", "line": 11, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 11, "endColumn": 8}, {"ruleId": "228", "severity": 1, "message": "260", "line": 3, "column": 10, "nodeType": "230", "messageId": "231", "endLine": 3, "endColumn": 31}, {"ruleId": "243", "severity": 1, "message": "261", "line": 87, "column": 6, "nodeType": "245", "endLine": 87, "endColumn": 8, "suggestions": "262"}, {"ruleId": "228", "severity": 1, "message": "251", "line": 5, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 5, "endColumn": 13}, {"ruleId": "228", "severity": 1, "message": "263", "line": 9, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 9, "endColumn": 10}, {"ruleId": "228", "severity": 1, "message": "264", "line": 1, "column": 20, "nodeType": "230", "messageId": "231", "endLine": 1, "endColumn": 29}, {"ruleId": "228", "severity": 1, "message": "265", "line": 9, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 9, "endColumn": 13}, {"ruleId": "228", "severity": 1, "message": "266", "line": 10, "column": 3, "nodeType": "230", "messageId": "231", "endLine": 10, "endColumn": 8}, {"ruleId": "228", "severity": 1, "message": "240", "line": 13, "column": 23, "nodeType": "230", "messageId": "231", "endLine": 13, "endColumn": 30}, {"ruleId": "228", "severity": 1, "message": "239", "line": 13, "column": 32, "nodeType": "230", "messageId": "231", "endLine": 13, "endColumn": 39}, {"ruleId": "228", "severity": 1, "message": "233", "line": 13, "column": 41, "nodeType": "230", "messageId": "231", "endLine": 13, "endColumn": 45}, {"ruleId": "228", "severity": 1, "message": "240", "line": 12, "column": 10, "nodeType": "230", "messageId": "231", "endLine": 12, "endColumn": 17}, {"ruleId": "243", "severity": 1, "message": "267", "line": 45, "column": 6, "nodeType": "245", "endLine": 45, "endColumn": 16, "suggestions": "268"}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'Filter' is defined but never used.", "'Bar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadReport'. Either include it or remove the dependency array.", "ArrayExpression", ["269"], "'TrendingDown' is defined but never used.", "'BarChart3' is defined but never used.", "'Download' is defined but never used.", "'Upload' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'connectFirestoreEmulator' is defined but never used.", "'connectStorageEmulator' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["270"], "'getDocs' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["271"], "'Printer' is defined but never used.", "'useEffect' is defined but never used.", "'startAfter' is defined but never used.", "'limit' is defined but never used.", "React Hook useEffect has missing dependencies: 'calculateMetrics' and 'generateAlerts'. Either include them or remove the dependency array.", ["272"], {"desc": "273", "fix": "274"}, {"desc": "275", "fix": "276"}, {"desc": "277", "fix": "278"}, {"desc": "279", "fix": "280"}, "Update the dependencies array to be: [loadReport, selectedPeriod]", {"range": "281", "text": "282"}, "Update the dependencies array to be: [loadUsers]", {"range": "283", "text": "284"}, "Update the dependencies array to be: [removeFromCart]", {"range": "285", "text": "286"}, "Update the dependencies array to be: [calculateMetrics, generateAlerts, products]", {"range": "287", "text": "288"}, [1381, 1397], "[load<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>]", [865, 867], "[loadUsers]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]", [1163, 1173], "[calculateMetrics, generateAlerts, products]"]