{"ast": null, "code": "'use strict';\n\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar create = require('../internals/object-create');\nvar defineProperty = require('../internals/object-define-property').f;\nvar UNSCOPABLES = wellKnownSymbol('unscopables');\nvar ArrayPrototype = Array.prototype;\n\n// Array.prototype[@@unscopables]\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\nif (ArrayPrototype[UNSCOPABLES] === undefined) {\n  defineProperty(ArrayPrototype, UNSCOPABLES, {\n    configurable: true,\n    value: create(null)\n  });\n}\n\n// add a key to Array.prototype[@@unscopables]\nmodule.exports = function (key) {\n  ArrayPrototype[UNSCOPABLES][key] = true;\n};", "map": {"version": 3, "names": ["wellKnownSymbol", "require", "create", "defineProperty", "f", "UNSCOPABLES", "ArrayPrototype", "Array", "prototype", "undefined", "configurable", "value", "module", "exports", "key"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/add-to-unscopables.js"], "sourcesContent": ["'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar create = require('../internals/object-create');\nvar defineProperty = require('../internals/object-define-property').f;\n\nvar UNSCOPABLES = wellKnownSymbol('unscopables');\nvar ArrayPrototype = Array.prototype;\n\n// Array.prototype[@@unscopables]\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\nif (ArrayPrototype[UNSCOPABLES] === undefined) {\n  defineProperty(ArrayPrototype, UNSCOPABLES, {\n    configurable: true,\n    value: create(null)\n  });\n}\n\n// add a key to Array.prototype[@@unscopables]\nmodule.exports = function (key) {\n  ArrayPrototype[UNSCOPABLES][key] = true;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,gCAAgC,CAAC;AAC/D,IAAIC,MAAM,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAClD,IAAIE,cAAc,GAAGF,OAAO,CAAC,qCAAqC,CAAC,CAACG,CAAC;AAErE,IAAIC,WAAW,GAAGL,eAAe,CAAC,aAAa,CAAC;AAChD,IAAIM,cAAc,GAAGC,KAAK,CAACC,SAAS;;AAEpC;AACA;AACA,IAAIF,cAAc,CAACD,WAAW,CAAC,KAAKI,SAAS,EAAE;EAC7CN,cAAc,CAACG,cAAc,EAAED,WAAW,EAAE;IAC1CK,YAAY,EAAE,IAAI;IAClBC,KAAK,EAAET,MAAM,CAAC,IAAI;EACpB,CAAC,CAAC;AACJ;;AAEA;AACAU,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC9BR,cAAc,CAACD,WAAW,CAAC,CAACS,GAAG,CAAC,GAAG,IAAI;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}