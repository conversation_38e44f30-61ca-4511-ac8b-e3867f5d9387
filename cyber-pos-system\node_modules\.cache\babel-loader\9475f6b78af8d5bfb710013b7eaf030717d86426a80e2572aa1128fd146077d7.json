{"ast": null, "code": "import React,{useState}from'react';import{Package,Plus,Search,AlertTriangle,Calendar,Edit,Trash2,RefreshCw}from'lucide-react';import{useAuth}from'../../contexts/AuthContext';import{useProducts}from'../../hooks/useProducts';import ProductModal from'./ProductModal';import InventoryStats from'./InventoryStats';import StockAdjustmentModal from'./StockAdjustmentModal';import LowStockAlert from'./LowStockAlert';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Inventory=()=>{const{hasPermission}=useAuth();const{products,loading,error,updateProduct,deleteProduct,updateStock,getLowStockProducts,getExpiringProducts,getProductCategories}=useProducts();const[activeView,setActiveView]=useState('products');const[showProductModal,setShowProductModal]=useState(false);const[showStockModal,setShowStockModal]=useState(false);const[editingProduct,setEditingProduct]=useState(null);const[adjustingStock,setAdjustingStock]=useState(null);const[searchTerm,setSearchTerm]=useState('');const[selectedCategory,setSelectedCategory]=useState('');const[stockFilter,setStockFilter]=useState('all');// Filter products based on search, category, and stock status\nconst filteredProducts=products.filter(product=>{const matchesSearch=product.name.toLowerCase().includes(searchTerm.toLowerCase())||product.description.toLowerCase().includes(searchTerm.toLowerCase());const matchesCategory=!selectedCategory||product.category===selectedCategory;let matchesStock=true;switch(stockFilter){case'low':matchesStock=product.stockQuantity<=product.reorderLevel&&product.stockQuantity>0;break;case'out':matchesStock=product.stockQuantity===0;break;case'expiring':const thirtyDaysFromNow=new Date();const newDate=thirtyDaysFromNow.getDate()+30;thirtyDaysFromNow.setDate(newDate);matchesStock=product.hasExpiry&&product.expiryDate&&product.expiryDate<=thirtyDaysFromNow;break;}return matchesSearch&&matchesCategory&&matchesStock;});const categories=getProductCategories();const lowStockProducts=getLowStockProducts();const expiringProducts=getExpiringProducts();const handleEditProduct=product=>{setEditingProduct(product);setShowProductModal(true);};const handleDeleteProduct=async productId=>{if(window.confirm('Are you sure you want to delete this product?')){try{await deleteProduct(productId);}catch(error){console.error('Error deleting product:',error);}}};const handleStockAdjustment=product=>{setAdjustingStock(product);setShowStockModal(true);};const handleToggleActive=async product=>{try{await updateProduct(product.id,{isActive:!product.isActive});}catch(error){console.error('Error toggling product status:',error);}};if(!hasPermission(['admin','attendant'])){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Package,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"Access Denied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"You don't have permission to access inventory management.\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(Package,{className:\"h-6 w-6 text-primary-600 mr-2\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"Inventory Management\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex bg-gray-100 rounded-lg p-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveView('products'),className:\"px-3 py-1 rounded text-sm font-medium transition-colors \".concat(activeView==='products'?'bg-white text-gray-900 shadow-sm':'text-gray-600 hover:text-gray-900'),children:\"Products\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveView('stats'),className:\"px-3 py-1 rounded text-sm font-medium transition-colors \".concat(activeView==='stats'?'bg-white text-gray-900 shadow-sm':'text-gray-600 hover:text-gray-900'),children:\"Statistics\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveView('alerts'),className:\"px-3 py-1 rounded text-sm font-medium transition-colors \".concat(activeView==='alerts'?'bg-white text-gray-900 shadow-sm':'text-gray-600 hover:text-gray-900'),children:[\"Alerts\",(lowStockProducts.length>0||expiringProducts.length>0)&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5\",children:lowStockProducts.length+expiringProducts.length})]})]}),hasPermission('admin')&&/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setEditingProduct(null);setShowProductModal(true);},className:\"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\",children:[/*#__PURE__*/_jsx(Plus,{className:\"h-4 w-4 mr-2\"}),\"Add Product\"]})]})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\",children:error}),activeView==='products'&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row gap-4 mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 relative\",children:[/*#__PURE__*/_jsx(Search,{className:\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search products...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-3\",children:[/*#__PURE__*/_jsxs(\"select\",{value:selectedCategory,onChange:e=>setSelectedCategory(e.target.value),className:\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Categories\"}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category,children:category},category))]}),/*#__PURE__*/_jsxs(\"select\",{value:stockFilter,onChange:e=>setStockFilter(e.target.value),className:\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Stock\"}),/*#__PURE__*/_jsx(\"option\",{value:\"low\",children:\"Low Stock\"}),/*#__PURE__*/_jsx(\"option\",{value:\"out\",children:\"Out of Stock\"}),/*#__PURE__*/_jsx(\"option\",{value:\"expiring\",children:\"Expiring Soon\"})]})]})]})]}),activeView==='stats'?/*#__PURE__*/_jsx(InventoryStats,{products:products}):activeView==='alerts'?/*#__PURE__*/_jsx(LowStockAlert,{lowStockProducts:lowStockProducts,expiringProducts:expiringProducts,onStockAdjust:handleStockAdjustment}):/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-500\",children:\"Loading products...\"})]}):filteredProducts.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Package,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"No products found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:searchTerm||selectedCategory||stockFilter!=='all'?'Try adjusting your search or filter criteria.':'Get started by adding your first product.'})]}):/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:filteredProducts.map(product=>/*#__PURE__*/_jsx(ProductCard,{product:product,onEdit:handleEditProduct,onDelete:handleDeleteProduct,onStockAdjust:handleStockAdjustment,onToggleActive:handleToggleActive,canEdit:hasPermission('admin')},product.id))})})}),showProductModal&&/*#__PURE__*/_jsx(ProductModal,{product:editingProduct,onClose:()=>{setShowProductModal(false);setEditingProduct(null);}}),showStockModal&&adjustingStock&&/*#__PURE__*/_jsx(StockAdjustmentModal,{product:adjustingStock,onClose:()=>{setShowStockModal(false);setAdjustingStock(null);},onAdjust:updateStock})]});};// Product Card Component\nconst ProductCard=_ref=>{let{product,onEdit,onDelete,onStockAdjust,onToggleActive,canEdit}=_ref;const isLowStock=product.stockQuantity<=product.reorderLevel;const isOutOfStock=product.stockQuantity===0;const isExpiringSoon=product.hasExpiry&&product.expiryDate&&product.expiryDate<=new Date(Date.now()+30*24*60*60*1000);const getStockStatusColor=()=>{if(isOutOfStock)return'text-red-600 bg-red-100';if(isLowStock)return'text-orange-600 bg-orange-100';return'text-green-600 bg-green-100';};const getStockStatusText=()=>{if(isOutOfStock)return'Out of Stock';if(isLowStock)return'Low Stock';return'In Stock';};return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white border rounded-lg p-4 \".concat(product.isActive?'border-gray-200':'border-gray-300 bg-gray-50'),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold \".concat(product.isActive?'text-gray-900':'text-gray-500'),children:product.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm \".concat(product.isActive?'text-gray-600':'text-gray-400'),children:product.description})]}),canEdit&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(product),className:\"text-blue-600 hover:text-blue-800\",children:/*#__PURE__*/_jsx(Edit,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(product.id),className:\"text-red-600 hover:text-red-800\",children:/*#__PURE__*/_jsx(Trash2,{className:\"h-4 w-4\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",children:product.category}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-semibold text-green-600\",children:[\"KSh \",product.price.toLocaleString()]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Stock Level:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getStockStatusColor()),children:getStockStatusText()}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onStockAdjust(product),className:\"text-blue-600 hover:text-blue-800\",title:\"Adjust Stock\",children:/*#__PURE__*/_jsx(RefreshCw,{className:\"h-3 w-3\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Current:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-medium \".concat(isOutOfStock?'text-red-600':isLowStock?'text-orange-600':'text-green-600'),children:[product.stockQuantity,\" units\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Reorder Level:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-medium text-gray-900\",children:[product.reorderLevel,\" units\"]})]})]}),product.hasExpiry&&product.expiryDate&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Expires:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium \".concat(isExpiringSoon?'text-orange-600':'text-gray-900'),children:product.expiryDate.toLocaleDateString()}),isExpiringSoon&&/*#__PURE__*/_jsx(Calendar,{className:\"h-3 w-3 text-orange-600\"})]})]}),canEdit&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between pt-2 border-t\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Status:\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onToggleActive(product),className:\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full \".concat(product.isActive?'bg-green-100 text-green-800 hover:bg-green-200':'bg-gray-100 text-gray-800 hover:bg-gray-200'),children:product.isActive?'Active':'Inactive'})]}),(isLowStock||isExpiringSoon)&&/*#__PURE__*/_jsxs(\"div\",{className:\"pt-2 border-t\",children:[isLowStock&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-xs text-orange-600 mb-1\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-3 w-3 mr-1\"}),\"Stock below reorder level\"]}),isExpiringSoon&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-xs text-orange-600\",children:[/*#__PURE__*/_jsx(Calendar,{className:\"h-3 w-3 mr-1\"}),\"Expires within 30 days\"]})]})]})]});};export default Inventory;", "map": {"version": 3, "names": ["React", "useState", "Package", "Plus", "Search", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Calendar", "Edit", "Trash2", "RefreshCw", "useAuth", "useProducts", "ProductModal", "InventoryStats", "StockAdjustmentModal", "LowStockAlert", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Inventory", "hasPermission", "products", "loading", "error", "updateProduct", "deleteProduct", "updateStock", "getLowStockProducts", "getExpiringProducts", "getProductCategories", "activeView", "setActiveView", "showProductModal", "setShowProductModal", "showStockModal", "setShowStockModal", "editingProduct", "setEditingProduct", "adjustingStock", "setAdjustingStock", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "stockFilter", "setStockFilter", "filteredProducts", "filter", "product", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesCategory", "category", "matchesStock", "stockQuantity", "reorderLevel", "thirtyDaysFromNow", "Date", "newDate", "getDate", "setDate", "hasEx<PERSON>ry", "expiryDate", "categories", "lowStockProducts", "expiringProducts", "handleEditProduct", "handleDeleteProduct", "productId", "window", "confirm", "console", "handleStockAdjustment", "handleToggleActive", "id", "isActive", "className", "children", "onClick", "concat", "length", "type", "placeholder", "value", "onChange", "e", "target", "map", "onStockAdjust", "ProductCard", "onEdit", "onDelete", "onToggleActive", "canEdit", "onClose", "onAdjust", "_ref", "isLowStock", "isOutOfStock", "isExpiringSoon", "now", "getStockStatusColor", "getStockStatusText", "price", "toLocaleString", "title", "toLocaleDateString"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/Inventory.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Package,\n  Plus,\n  Search,\n  Filter,\n  AlertTriangle,\n  Calendar,\n  TrendingDown,\n  BarChart3,\n  Edit,\n  Trash2,\n  RefreshCw,\n  Download,\n  Upload\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useProducts } from '../../hooks/useProducts';\nimport { Product } from '../../types';\nimport ProductModal from './ProductModal';\nimport InventoryStats from './InventoryStats';\nimport StockAdjustmentModal from './StockAdjustmentModal';\nimport LowStockAlert from './LowStockAlert';\n\nconst Inventory: React.FC = () => {\n  const { hasPermission } = useAuth();\n  const {\n    products,\n    loading,\n    error,\n    updateProduct,\n    deleteProduct,\n    updateStock,\n    getLowStockProducts,\n    getExpiringProducts,\n    getProductCategories\n  } = useProducts();\n\n  const [activeView, setActiveView] = useState<'products' | 'stats' | 'alerts'>('products');\n  const [showProductModal, setShowProductModal] = useState(false);\n  const [showStockModal, setShowStockModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [adjustingStock, setAdjustingStock] = useState<Product | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [stockFilter, setStockFilter] = useState<'all' | 'low' | 'out' | 'expiring'>('all');\n\n  // Filter products based on search, category, and stock status\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !selectedCategory || product.category === selectedCategory;\n\n    let matchesStock = true;\n    switch (stockFilter) {\n      case 'low':\n        matchesStock = product.stockQuantity <= product.reorderLevel && product.stockQuantity > 0;\n        break;\n      case 'out':\n        matchesStock = product.stockQuantity === 0;\n        break;\n      case 'expiring':\n        const thirtyDaysFromNow = new Date();\n        const newDate = thirtyDaysFromNow.getDate() + 30;\n        thirtyDaysFromNow.setDate(newDate);\n        matchesStock = product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow;\n        break;\n    }\n\n    return matchesSearch && matchesCategory && matchesStock;\n  });\n\n  const categories = getProductCategories();\n  const lowStockProducts = getLowStockProducts();\n  const expiringProducts = getExpiringProducts();\n\n  const handleEditProduct = (product: Product) => {\n    setEditingProduct(product);\n    setShowProductModal(true);\n  };\n\n  const handleDeleteProduct = async (productId: string) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await deleteProduct(productId);\n      } catch (error) {\n        console.error('Error deleting product:', error);\n      }\n    }\n  };\n\n  const handleStockAdjustment = (product: Product) => {\n    setAdjustingStock(product);\n    setShowStockModal(true);\n  };\n\n  const handleToggleActive = async (product: Product) => {\n    try {\n      await updateProduct(product.id, { isActive: !product.isActive });\n    } catch (error) {\n      console.error('Error toggling product status:', error);\n    }\n  };\n\n  if (!hasPermission(['admin', 'attendant'])) {\n    return (\n      <div className=\"text-center py-12\">\n        <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Access Denied</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          You don't have permission to access inventory management.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center\">\n            <Package className=\"h-6 w-6 text-primary-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">Inventory Management</h1>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            {/* View Toggle */}\n            <div className=\"flex bg-gray-100 rounded-lg p-1\">\n              <button\n                onClick={() => setActiveView('products')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'products'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Products\n              </button>\n              <button\n                onClick={() => setActiveView('stats')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'stats'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Statistics\n              </button>\n              <button\n                onClick={() => setActiveView('alerts')}\n                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${\n                  activeView === 'alerts'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                Alerts\n                {(lowStockProducts.length > 0 || expiringProducts.length > 0) && (\n                  <span className=\"ml-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5\">\n                    {lowStockProducts.length + expiringProducts.length}\n                  </span>\n                )}\n              </button>\n            </div>\n\n            {hasPermission('admin') && (\n              <button\n                onClick={() => {\n                  setEditingProduct(null);\n                  setShowProductModal(true);\n                }}\n                className=\"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Product\n              </button>\n            )}\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n            {error}\n          </div>\n        )}\n\n        {/* Search and Filters - Only show for products view */}\n        {activeView === 'products' && (\n          <div className=\"flex flex-col lg:flex-row gap-4 mb-6\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              />\n            </div>\n\n            <div className=\"flex gap-3\">\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>{category}</option>\n                ))}\n              </select>\n\n              <select\n                value={stockFilter}\n                onChange={(e) => setStockFilter(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"all\">All Stock</option>\n                <option value=\"low\">Low Stock</option>\n                <option value=\"out\">Out of Stock</option>\n                <option value=\"expiring\">Expiring Soon</option>\n              </select>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Content based on active view */}\n      {activeView === 'stats' ? (\n        <InventoryStats products={products} />\n      ) : activeView === 'alerts' ? (\n        <LowStockAlert\n          lowStockProducts={lowStockProducts}\n          expiringProducts={expiringProducts}\n          onStockAdjust={handleStockAdjustment}\n        />\n      ) : (\n        <>\n          {/* Products Grid */}\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            {loading ? (\n              <div className=\"text-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n                <p className=\"mt-2 text-gray-500\">Loading products...</p>\n              </div>\n            ) : filteredProducts.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No products found</h3>\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  {searchTerm || selectedCategory || stockFilter !== 'all'\n                    ? 'Try adjusting your search or filter criteria.'\n                    : 'Get started by adding your first product.'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {filteredProducts.map((product) => (\n                  <ProductCard\n                    key={product.id}\n                    product={product}\n                    onEdit={handleEditProduct}\n                    onDelete={handleDeleteProduct}\n                    onStockAdjust={handleStockAdjustment}\n                    onToggleActive={handleToggleActive}\n                    canEdit={hasPermission('admin')}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n        </>\n      )}\n\n      {/* Modals */}\n      {showProductModal && (\n        <ProductModal\n          product={editingProduct}\n          onClose={() => {\n            setShowProductModal(false);\n            setEditingProduct(null);\n          }}\n        />\n      )}\n\n      {showStockModal && adjustingStock && (\n        <StockAdjustmentModal\n          product={adjustingStock}\n          onClose={() => {\n            setShowStockModal(false);\n            setAdjustingStock(null);\n          }}\n          onAdjust={updateStock}\n        />\n      )}\n    </div>\n  );\n};\n\n// Product Card Component\ninterface ProductCardProps {\n  product: Product;\n  onEdit: (product: Product) => void;\n  onDelete: (productId: string) => void;\n  onStockAdjust: (product: Product) => void;\n  onToggleActive: (product: Product) => void;\n  canEdit: boolean;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({\n  product,\n  onEdit,\n  onDelete,\n  onStockAdjust,\n  onToggleActive,\n  canEdit\n}) => {\n  const isLowStock = product.stockQuantity <= product.reorderLevel;\n  const isOutOfStock = product.stockQuantity === 0;\n  const isExpiringSoon = product.hasExpiry && product.expiryDate &&\n    product.expiryDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n\n  const getStockStatusColor = () => {\n    if (isOutOfStock) return 'text-red-600 bg-red-100';\n    if (isLowStock) return 'text-orange-600 bg-orange-100';\n    return 'text-green-600 bg-green-100';\n  };\n\n  const getStockStatusText = () => {\n    if (isOutOfStock) return 'Out of Stock';\n    if (isLowStock) return 'Low Stock';\n    return 'In Stock';\n  };\n\n  return (\n    <div className={`bg-white border rounded-lg p-4 ${product.isActive ? 'border-gray-200' : 'border-gray-300 bg-gray-50'}`}>\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex-1\">\n          <h3 className={`font-semibold ${product.isActive ? 'text-gray-900' : 'text-gray-500'}`}>\n            {product.name}\n          </h3>\n          <p className={`text-sm ${product.isActive ? 'text-gray-600' : 'text-gray-400'}`}>\n            {product.description}\n          </p>\n        </div>\n        {canEdit && (\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => onEdit(product)}\n              className=\"text-blue-600 hover:text-blue-800\"\n            >\n              <Edit className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => onDelete(product.id)}\n              className=\"text-red-600 hover:text-red-800\"\n            >\n              <Trash2 className=\"h-4 w-4\" />\n            </button>\n          </div>\n        )}\n      </div>\n\n      <div className=\"space-y-3\">\n        {/* Category and Price */}\n        <div className=\"flex items-center justify-between\">\n          <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n            {product.category}\n          </span>\n          <span className=\"font-semibold text-green-600\">\n            KSh {product.price.toLocaleString()}\n          </span>\n        </div>\n\n        {/* Stock Information */}\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-gray-600\">Stock Level:</span>\n            <div className=\"flex items-center space-x-2\">\n              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor()}`}>\n                {getStockStatusText()}\n              </span>\n              <button\n                onClick={() => onStockAdjust(product)}\n                className=\"text-blue-600 hover:text-blue-800\"\n                title=\"Adjust Stock\"\n              >\n                <RefreshCw className=\"h-3 w-3\" />\n              </button>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">Current:</span>\n            <span className={`font-medium ${isOutOfStock ? 'text-red-600' : isLowStock ? 'text-orange-600' : 'text-green-600'}`}>\n              {product.stockQuantity} units\n            </span>\n          </div>\n\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">Reorder Level:</span>\n            <span className=\"font-medium text-gray-900\">{product.reorderLevel} units</span>\n          </div>\n        </div>\n\n        {/* Expiry Information */}\n        {product.hasExpiry && product.expiryDate && (\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">Expires:</span>\n            <div className=\"flex items-center space-x-1\">\n              <span className={`font-medium ${isExpiringSoon ? 'text-orange-600' : 'text-gray-900'}`}>\n                {product.expiryDate.toLocaleDateString()}\n              </span>\n              {isExpiringSoon && <Calendar className=\"h-3 w-3 text-orange-600\" />}\n            </div>\n          </div>\n        )}\n\n        {/* Status Toggle */}\n        {canEdit && (\n          <div className=\"flex items-center justify-between pt-2 border-t\">\n            <span className=\"text-sm text-gray-600\">Status:</span>\n            <button\n              onClick={() => onToggleActive(product)}\n              className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${\n                product.isActive\n                  ? 'bg-green-100 text-green-800 hover:bg-green-200'\n                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'\n              }`}\n            >\n              {product.isActive ? 'Active' : 'Inactive'}\n            </button>\n          </div>\n        )}\n\n        {/* Alerts */}\n        {(isLowStock || isExpiringSoon) && (\n          <div className=\"pt-2 border-t\">\n            {isLowStock && (\n              <div className=\"flex items-center text-xs text-orange-600 mb-1\">\n                <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                Stock below reorder level\n              </div>\n            )}\n            {isExpiringSoon && (\n              <div className=\"flex items-center text-xs text-orange-600\">\n                <Calendar className=\"h-3 w-3 mr-1\" />\n                Expires within 30 days\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Inventory;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,OAAO,CACPC,IAAI,CACJC,MAAM,CAENC,aAAa,CACbC,QAAQ,CAGRC,IAAI,CACJC,MAAM,CACNC,SAAS,KAGJ,cAAc,CACrB,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,WAAW,KAAQ,yBAAyB,CAErD,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,oBAAoB,KAAM,wBAAwB,CACzD,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE5C,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAEC,aAAc,CAAC,CAAGb,OAAO,CAAC,CAAC,CACnC,KAAM,CACJc,QAAQ,CACRC,OAAO,CACPC,KAAK,CACLC,aAAa,CACbC,aAAa,CACbC,WAAW,CACXC,mBAAmB,CACnBC,mBAAmB,CACnBC,oBACF,CAAC,CAAGrB,WAAW,CAAC,CAAC,CAEjB,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGjC,QAAQ,CAAkC,UAAU,CAAC,CACzF,KAAM,CAACkC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACoC,cAAc,CAAEC,iBAAiB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACsC,cAAc,CAAEC,iBAAiB,CAAC,CAAGvC,QAAQ,CAAiB,IAAI,CAAC,CAC1E,KAAM,CAACwC,cAAc,CAAEC,iBAAiB,CAAC,CAAGzC,QAAQ,CAAiB,IAAI,CAAC,CAC1E,KAAM,CAAC0C,UAAU,CAAEC,aAAa,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC4C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC8C,WAAW,CAAEC,cAAc,CAAC,CAAG/C,QAAQ,CAAqC,KAAK,CAAC,CAEzF;AACA,KAAM,CAAAgD,gBAAgB,CAAGzB,QAAQ,CAAC0B,MAAM,CAACC,OAAO,EAAI,CAClD,KAAM,CAAAC,aAAa,CAAGD,OAAO,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC,EAC9DH,OAAO,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC,CACzF,KAAM,CAAAG,eAAe,CAAG,CAACZ,gBAAgB,EAAIM,OAAO,CAACO,QAAQ,GAAKb,gBAAgB,CAElF,GAAI,CAAAc,YAAY,CAAG,IAAI,CACvB,OAAQZ,WAAW,EACjB,IAAK,KAAK,CACRY,YAAY,CAAGR,OAAO,CAACS,aAAa,EAAIT,OAAO,CAACU,YAAY,EAAIV,OAAO,CAACS,aAAa,CAAG,CAAC,CACzF,MACF,IAAK,KAAK,CACRD,YAAY,CAAGR,OAAO,CAACS,aAAa,GAAK,CAAC,CAC1C,MACF,IAAK,UAAU,CACb,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACpC,KAAM,CAAAC,OAAO,CAAGF,iBAAiB,CAACG,OAAO,CAAC,CAAC,CAAG,EAAE,CAChDH,iBAAiB,CAACI,OAAO,CAACF,OAAO,CAAC,CAClCL,YAAY,CAAGR,OAAO,CAACgB,SAAS,EAAIhB,OAAO,CAACiB,UAAU,EAAIjB,OAAO,CAACiB,UAAU,EAAIN,iBAAiB,CACjG,MACJ,CAEA,MAAO,CAAAV,aAAa,EAAIK,eAAe,EAAIE,YAAY,CACzD,CAAC,CAAC,CAEF,KAAM,CAAAU,UAAU,CAAGrC,oBAAoB,CAAC,CAAC,CACzC,KAAM,CAAAsC,gBAAgB,CAAGxC,mBAAmB,CAAC,CAAC,CAC9C,KAAM,CAAAyC,gBAAgB,CAAGxC,mBAAmB,CAAC,CAAC,CAE9C,KAAM,CAAAyC,iBAAiB,CAAIrB,OAAgB,EAAK,CAC9CX,iBAAiB,CAACW,OAAO,CAAC,CAC1Bf,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAqC,mBAAmB,CAAG,KAAO,CAAAC,SAAiB,EAAK,CACvD,GAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,CAAE,CACnE,GAAI,CACF,KAAM,CAAAhD,aAAa,CAAC8C,SAAS,CAAC,CAChC,CAAE,MAAOhD,KAAK,CAAE,CACdmD,OAAO,CAACnD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CACF,CAAC,CAED,KAAM,CAAAoD,qBAAqB,CAAI3B,OAAgB,EAAK,CAClDT,iBAAiB,CAACS,OAAO,CAAC,CAC1Bb,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAAyC,kBAAkB,CAAG,KAAO,CAAA5B,OAAgB,EAAK,CACrD,GAAI,CACF,KAAM,CAAAxB,aAAa,CAACwB,OAAO,CAAC6B,EAAE,CAAE,CAAEC,QAAQ,CAAE,CAAC9B,OAAO,CAAC8B,QAAS,CAAC,CAAC,CAClE,CAAE,MAAOvD,KAAK,CAAE,CACdmD,OAAO,CAACnD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACF,CAAC,CAED,GAAI,CAACH,aAAa,CAAC,CAAC,OAAO,CAAE,WAAW,CAAC,CAAC,CAAE,CAC1C,mBACEJ,KAAA,QAAK+D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClE,IAAA,CAACf,OAAO,EAACgF,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACvDjE,IAAA,OAAIiE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACzElE,IAAA,MAAGiE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,2DAE1C,CAAG,CAAC,EACD,CAAC,CAEV,CAEA,mBACEhE,KAAA,QAAK+D,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBhE,KAAA,QAAK+D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7ChE,KAAA,QAAK+D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDhE,KAAA,QAAK+D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClE,IAAA,CAACf,OAAO,EAACgF,SAAS,CAAC,+BAA+B,CAAE,CAAC,cACrDjE,IAAA,OAAIiE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,EACvE,CAAC,cAENhE,KAAA,QAAK+D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAE1ChE,KAAA,QAAK+D,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9ClE,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMlD,aAAa,CAAC,UAAU,CAAE,CACzCgD,SAAS,4DAAAG,MAAA,CACPpD,UAAU,GAAK,UAAU,CACrB,kCAAkC,CAClC,mCAAmC,CACtC,CAAAkD,QAAA,CACJ,UAED,CAAQ,CAAC,cACTlE,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMlD,aAAa,CAAC,OAAO,CAAE,CACtCgD,SAAS,4DAAAG,MAAA,CACPpD,UAAU,GAAK,OAAO,CAClB,kCAAkC,CAClC,mCAAmC,CACtC,CAAAkD,QAAA,CACJ,YAED,CAAQ,CAAC,cACThE,KAAA,WACEiE,OAAO,CAAEA,CAAA,GAAMlD,aAAa,CAAC,QAAQ,CAAE,CACvCgD,SAAS,4DAAAG,MAAA,CACPpD,UAAU,GAAK,QAAQ,CACnB,kCAAkC,CAClC,mCAAmC,CACtC,CAAAkD,QAAA,EACJ,QAEC,CAAC,CAACb,gBAAgB,CAACgB,MAAM,CAAG,CAAC,EAAIf,gBAAgB,CAACe,MAAM,CAAG,CAAC,gBAC1DrE,IAAA,SAAMiE,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC5Eb,gBAAgB,CAACgB,MAAM,CAAGf,gBAAgB,CAACe,MAAM,CAC9C,CACP,EACK,CAAC,EACN,CAAC,CAEL/D,aAAa,CAAC,OAAO,CAAC,eACrBJ,KAAA,WACEiE,OAAO,CAAEA,CAAA,GAAM,CACb5C,iBAAiB,CAAC,IAAI,CAAC,CACvBJ,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CACF8C,SAAS,CAAC,uFAAuF,CAAAC,QAAA,eAEjGlE,IAAA,CAACd,IAAI,EAAC+E,SAAS,CAAC,cAAc,CAAE,CAAC,cAEnC,EAAQ,CACT,EACE,CAAC,EACH,CAAC,CAELxD,KAAK,eACJT,IAAA,QAAKiE,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CACjFzD,KAAK,CACH,CACN,CAGAO,UAAU,GAAK,UAAU,eACxBd,KAAA,QAAK+D,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnDhE,KAAA,QAAK+D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BlE,IAAA,CAACb,MAAM,EAAC8E,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC/FjE,IAAA,UACEsE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAE9C,UAAW,CAClB+C,QAAQ,CAAGC,CAAC,EAAK/C,aAAa,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CP,SAAS,CAAC,0GAA0G,CACrH,CAAC,EACC,CAAC,cAEN/D,KAAA,QAAK+D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBhE,KAAA,WACEsE,KAAK,CAAE5C,gBAAiB,CACxB6C,QAAQ,CAAGC,CAAC,EAAK7C,mBAAmB,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDP,SAAS,CAAC,6FAA6F,CAAAC,QAAA,eAEvGlE,IAAA,WAAQwE,KAAK,CAAC,EAAE,CAAAN,QAAA,CAAC,gBAAc,CAAQ,CAAC,CACvCd,UAAU,CAACwB,GAAG,CAACnC,QAAQ,eACtBzC,IAAA,WAAuBwE,KAAK,CAAE/B,QAAS,CAAAyB,QAAA,CAAEzB,QAAQ,EAApCA,QAA6C,CAC3D,CAAC,EACI,CAAC,cAETvC,KAAA,WACEsE,KAAK,CAAE1C,WAAY,CACnB2C,QAAQ,CAAGC,CAAC,EAAK3C,cAAc,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAY,CAAE,CACvDP,SAAS,CAAC,6FAA6F,CAAAC,QAAA,eAEvGlE,IAAA,WAAQwE,KAAK,CAAC,KAAK,CAAAN,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtClE,IAAA,WAAQwE,KAAK,CAAC,KAAK,CAAAN,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtClE,IAAA,WAAQwE,KAAK,CAAC,KAAK,CAAAN,QAAA,CAAC,cAAY,CAAQ,CAAC,cACzClE,IAAA,WAAQwE,KAAK,CAAC,UAAU,CAAAN,QAAA,CAAC,eAAa,CAAQ,CAAC,EACzC,CAAC,EACN,CAAC,EACH,CACN,EACE,CAAC,CAGLlD,UAAU,GAAK,OAAO,cACrBhB,IAAA,CAACJ,cAAc,EAACW,QAAQ,CAAEA,QAAS,CAAE,CAAC,CACpCS,UAAU,GAAK,QAAQ,cACzBhB,IAAA,CAACF,aAAa,EACZuD,gBAAgB,CAAEA,gBAAiB,CACnCC,gBAAgB,CAAEA,gBAAiB,CACnCuB,aAAa,CAAEhB,qBAAsB,CACtC,CAAC,cAEF7D,IAAA,CAAAI,SAAA,EAAA8D,QAAA,cAEElE,IAAA,QAAKiE,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC5C1D,OAAO,cACNN,KAAA,QAAK+D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BlE,IAAA,QAAKiE,SAAS,CAAC,yEAAyE,CAAM,CAAC,cAC/FjE,IAAA,MAAGiE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,qBAAmB,CAAG,CAAC,EACtD,CAAC,CACJlC,gBAAgB,CAACqC,MAAM,GAAK,CAAC,cAC/BnE,KAAA,QAAK+D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClE,IAAA,CAACf,OAAO,EAACgF,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACvDjE,IAAA,OAAIiE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC7ElE,IAAA,MAAGiE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACtCxC,UAAU,EAAIE,gBAAgB,EAAIE,WAAW,GAAK,KAAK,CACpD,+CAA+C,CAC/C,2CAA2C,CAC9C,CAAC,EACD,CAAC,cAEN9B,IAAA,QAAKiE,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClElC,gBAAgB,CAAC4C,GAAG,CAAE1C,OAAO,eAC5BlC,IAAA,CAAC8E,WAAW,EAEV5C,OAAO,CAAEA,OAAQ,CACjB6C,MAAM,CAAExB,iBAAkB,CAC1ByB,QAAQ,CAAExB,mBAAoB,CAC9BqB,aAAa,CAAEhB,qBAAsB,CACrCoB,cAAc,CAAEnB,kBAAmB,CACnCoB,OAAO,CAAE5E,aAAa,CAAC,OAAO,CAAE,EAN3B4B,OAAO,CAAC6B,EAOd,CACF,CAAC,CACC,CACN,CACE,CAAC,CACN,CACH,CAGA7C,gBAAgB,eACflB,IAAA,CAACL,YAAY,EACXuC,OAAO,CAAEZ,cAAe,CACxB6D,OAAO,CAAEA,CAAA,GAAM,CACbhE,mBAAmB,CAAC,KAAK,CAAC,CAC1BI,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CACH,CACF,CAEAH,cAAc,EAAII,cAAc,eAC/BxB,IAAA,CAACH,oBAAoB,EACnBqC,OAAO,CAAEV,cAAe,CACxB2D,OAAO,CAAEA,CAAA,GAAM,CACb9D,iBAAiB,CAAC,KAAK,CAAC,CACxBI,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CACF2D,QAAQ,CAAExE,WAAY,CACvB,CACF,EACE,CAAC,CAEV,CAAC,CAED;AAUA,KAAM,CAAAkE,WAAuC,CAAGO,IAAA,EAO1C,IAP2C,CAC/CnD,OAAO,CACP6C,MAAM,CACNC,QAAQ,CACRH,aAAa,CACbI,cAAc,CACdC,OACF,CAAC,CAAAG,IAAA,CACC,KAAM,CAAAC,UAAU,CAAGpD,OAAO,CAACS,aAAa,EAAIT,OAAO,CAACU,YAAY,CAChE,KAAM,CAAA2C,YAAY,CAAGrD,OAAO,CAACS,aAAa,GAAK,CAAC,CAChD,KAAM,CAAA6C,cAAc,CAAGtD,OAAO,CAACgB,SAAS,EAAIhB,OAAO,CAACiB,UAAU,EAC5DjB,OAAO,CAACiB,UAAU,EAAI,GAAI,CAAAL,IAAI,CAACA,IAAI,CAAC2C,GAAG,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAEvE,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAIH,YAAY,CAAE,MAAO,yBAAyB,CAClD,GAAID,UAAU,CAAE,MAAO,+BAA+B,CACtD,MAAO,6BAA6B,CACtC,CAAC,CAED,KAAM,CAAAK,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIJ,YAAY,CAAE,MAAO,cAAc,CACvC,GAAID,UAAU,CAAE,MAAO,WAAW,CAClC,MAAO,UAAU,CACnB,CAAC,CAED,mBACEpF,KAAA,QAAK+D,SAAS,mCAAAG,MAAA,CAAoClC,OAAO,CAAC8B,QAAQ,CAAG,iBAAiB,CAAG,4BAA4B,CAAG,CAAAE,QAAA,eACtHhE,KAAA,QAAK+D,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDhE,KAAA,QAAK+D,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBlE,IAAA,OAAIiE,SAAS,kBAAAG,MAAA,CAAmBlC,OAAO,CAAC8B,QAAQ,CAAG,eAAe,CAAG,eAAe,CAAG,CAAAE,QAAA,CACpFhC,OAAO,CAACE,IAAI,CACX,CAAC,cACLpC,IAAA,MAAGiE,SAAS,YAAAG,MAAA,CAAalC,OAAO,CAAC8B,QAAQ,CAAG,eAAe,CAAG,eAAe,CAAG,CAAAE,QAAA,CAC7EhC,OAAO,CAACK,WAAW,CACnB,CAAC,EACD,CAAC,CACL2C,OAAO,eACNhF,KAAA,QAAK+D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClE,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMY,MAAM,CAAC7C,OAAO,CAAE,CAC/B+B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7ClE,IAAA,CAACV,IAAI,EAAC2E,SAAS,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,cACTjE,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMa,QAAQ,CAAC9C,OAAO,CAAC6B,EAAE,CAAE,CACpCE,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAE3ClE,IAAA,CAACT,MAAM,EAAC0E,SAAS,CAAC,SAAS,CAAE,CAAC,CACxB,CAAC,EACN,CACN,EACE,CAAC,cAEN/D,KAAA,QAAK+D,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBhE,KAAA,QAAK+D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDlE,IAAA,SAAMiE,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CACjGhC,OAAO,CAACO,QAAQ,CACb,CAAC,cACPvC,KAAA,SAAM+D,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,MACzC,CAAChC,OAAO,CAAC0D,KAAK,CAACC,cAAc,CAAC,CAAC,EAC/B,CAAC,EACJ,CAAC,cAGN3F,KAAA,QAAK+D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBhE,KAAA,QAAK+D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDlE,IAAA,SAAMiE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,cAC3DhE,KAAA,QAAK+D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClE,IAAA,SAAMiE,SAAS,6DAAAG,MAAA,CAA8DsB,mBAAmB,CAAC,CAAC,CAAG,CAAAxB,QAAA,CAClGyB,kBAAkB,CAAC,CAAC,CACjB,CAAC,cACP3F,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMU,aAAa,CAAC3C,OAAO,CAAE,CACtC+B,SAAS,CAAC,mCAAmC,CAC7C6B,KAAK,CAAC,cAAc,CAAA5B,QAAA,cAEpBlE,IAAA,CAACR,SAAS,EAACyE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,EACH,CAAC,cAEN/D,KAAA,QAAK+D,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACxDlE,IAAA,SAAMiE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cAC/ChE,KAAA,SAAM+D,SAAS,gBAAAG,MAAA,CAAiBmB,YAAY,CAAG,cAAc,CAAGD,UAAU,CAAG,iBAAiB,CAAG,gBAAgB,CAAG,CAAApB,QAAA,EACjHhC,OAAO,CAACS,aAAa,CAAC,QACzB,EAAM,CAAC,EACJ,CAAC,cAENzC,KAAA,QAAK+D,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACxDlE,IAAA,SAAMiE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cACrDhE,KAAA,SAAM+D,SAAS,CAAC,2BAA2B,CAAAC,QAAA,EAAEhC,OAAO,CAACU,YAAY,CAAC,QAAM,EAAM,CAAC,EAC5E,CAAC,EACH,CAAC,CAGLV,OAAO,CAACgB,SAAS,EAAIhB,OAAO,CAACiB,UAAU,eACtCjD,KAAA,QAAK+D,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACxDlE,IAAA,SAAMiE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cAC/ChE,KAAA,QAAK+D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClE,IAAA,SAAMiE,SAAS,gBAAAG,MAAA,CAAiBoB,cAAc,CAAG,iBAAiB,CAAG,eAAe,CAAG,CAAAtB,QAAA,CACpFhC,OAAO,CAACiB,UAAU,CAAC4C,kBAAkB,CAAC,CAAC,CACpC,CAAC,CACNP,cAAc,eAAIxF,IAAA,CAACX,QAAQ,EAAC4E,SAAS,CAAC,yBAAyB,CAAE,CAAC,EAChE,CAAC,EACH,CACN,CAGAiB,OAAO,eACNhF,KAAA,QAAK+D,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DlE,IAAA,SAAMiE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cACtDlE,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAMc,cAAc,CAAC/C,OAAO,CAAE,CACvC+B,SAAS,0EAAAG,MAAA,CACPlC,OAAO,CAAC8B,QAAQ,CACZ,gDAAgD,CAChD,6CAA6C,CAChD,CAAAE,QAAA,CAEFhC,OAAO,CAAC8B,QAAQ,CAAG,QAAQ,CAAG,UAAU,CACnC,CAAC,EACN,CACN,CAGA,CAACsB,UAAU,EAAIE,cAAc,gBAC5BtF,KAAA,QAAK+D,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BoB,UAAU,eACTpF,KAAA,QAAK+D,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC7DlE,IAAA,CAACZ,aAAa,EAAC6E,SAAS,CAAC,cAAc,CAAE,CAAC,4BAE5C,EAAK,CACN,CACAuB,cAAc,eACbtF,KAAA,QAAK+D,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACxDlE,IAAA,CAACX,QAAQ,EAAC4E,SAAS,CAAC,cAAc,CAAE,CAAC,yBAEvC,EAAK,CACN,EACE,CACN,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5D,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}