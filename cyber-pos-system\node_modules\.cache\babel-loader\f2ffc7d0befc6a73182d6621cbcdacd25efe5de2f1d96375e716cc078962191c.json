{"ast": null, "code": "'use strict';\n\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\n// `Object.keys` method\n// https://tc39.es/ecma262/#sec-object.keys\n// eslint-disable-next-line es/no-object-keys -- safe\nmodule.exports = Object.keys || function keys(O) {\n  return internalObjectKeys(O, enumBugKeys);\n};", "map": {"version": 3, "names": ["internalObjectKeys", "require", "enumBugKeys", "module", "exports", "Object", "keys", "O"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/object-keys.js"], "sourcesContent": ["'use strict';\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\n// `Object.keys` method\n// https://tc39.es/ecma262/#sec-object.keys\n// eslint-disable-next-line es/no-object-keys -- safe\nmodule.exports = Object.keys || function keys(O) {\n  return internalObjectKeys(O, enumBugKeys);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,kBAAkB,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AACrE,IAAIC,WAAW,GAAGD,OAAO,CAAC,4BAA4B,CAAC;;AAEvD;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,IAAI,IAAI,SAASA,IAAIA,CAACC,CAAC,EAAE;EAC/C,OAAOP,kBAAkB,CAACO,CAAC,EAAEL,WAAW,CAAC;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}