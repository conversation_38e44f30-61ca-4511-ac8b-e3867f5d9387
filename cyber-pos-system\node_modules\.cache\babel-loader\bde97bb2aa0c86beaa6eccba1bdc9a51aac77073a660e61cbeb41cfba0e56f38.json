{"ast": null, "code": "'use strict';\n\nvar DESCRIPTORS = require('../internals/descriptors');\nvar hasOwn = require('../internals/has-own-property');\nvar FunctionPrototype = Function.prototype;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;\nvar EXISTS = hasOwn(FunctionPrototype, 'name');\n// additional protection from minified / mangled / dropped function names\nvar PROPER = EXISTS && function something() {/* empty */}.name === 'something';\nvar CONFIGURABLE = EXISTS && (!DESCRIPTORS || DESCRIPTORS && getDescriptor(FunctionPrototype, 'name').configurable);\nmodule.exports = {\n  EXISTS: EXISTS,\n  PROPER: PROPER,\n  CONFIGURABLE: CONFIGURABLE\n};", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "hasOwn", "FunctionPrototype", "Function", "prototype", "getDescriptor", "Object", "getOwnPropertyDescriptor", "EXISTS", "PROPER", "something", "name", "CONFIGURABLE", "configurable", "module", "exports"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/function-name.js"], "sourcesContent": ["'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar hasOwn = require('../internals/has-own-property');\n\nvar FunctionPrototype = Function.prototype;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;\n\nvar EXISTS = hasOwn(FunctionPrototype, 'name');\n// additional protection from minified / mangled / dropped function names\nvar PROPER = EXISTS && (function something() { /* empty */ }).name === 'something';\nvar CONFIGURABLE = EXISTS && (!DESCRIPTORS || (DESCRIPTORS && getDescriptor(FunctionPrototype, 'name').configurable));\n\nmodule.exports = {\n  EXISTS: EXISTS,\n  PROPER: PROPER,\n  CONFIGURABLE: CONFIGURABLE\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACrD,IAAIC,MAAM,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAErD,IAAIE,iBAAiB,GAAGC,QAAQ,CAACC,SAAS;AAC1C;AACA,IAAIC,aAAa,GAAGN,WAAW,IAAIO,MAAM,CAACC,wBAAwB;AAElE,IAAIC,MAAM,GAAGP,MAAM,CAACC,iBAAiB,EAAE,MAAM,CAAC;AAC9C;AACA,IAAIO,MAAM,GAAGD,MAAM,IAAK,SAASE,SAASA,CAAA,EAAG,CAAE,YAAa,CAAEC,IAAI,KAAK,WAAW;AAClF,IAAIC,YAAY,GAAGJ,MAAM,KAAK,CAACT,WAAW,IAAKA,WAAW,IAAIM,aAAa,CAACH,iBAAiB,EAAE,MAAM,CAAC,CAACW,YAAa,CAAC;AAErHC,MAAM,CAACC,OAAO,GAAG;EACfP,MAAM,EAAEA,MAAM;EACdC,MAAM,EAAEA,MAAM;EACdG,YAAY,EAAEA;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}