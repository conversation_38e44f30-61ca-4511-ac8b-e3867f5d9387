{"ast": null, "code": "'use strict';\n\nvar ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `Math.trunc` method\n// https://tc39.es/ecma262/#sec-math.trunc\n// eslint-disable-next-line es/no-math-trunc -- safe\nmodule.exports = Math.trunc || function trunc(x) {\n  var n = +x;\n  return (n > 0 ? floor : ceil)(n);\n};", "map": {"version": 3, "names": ["ceil", "Math", "floor", "module", "exports", "trunc", "x", "n"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/math-trunc.js"], "sourcesContent": ["'use strict';\nvar ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `Math.trunc` method\n// https://tc39.es/ecma262/#sec-math.trunc\n// eslint-disable-next-line es/no-math-trunc -- safe\nmodule.exports = Math.trunc || function trunc(x) {\n  var n = +x;\n  return (n > 0 ? floor : ceil)(n);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,IAAI,GAAGC,IAAI,CAACD,IAAI;AACpB,IAAIE,KAAK,GAAGD,IAAI,CAACC,KAAK;;AAEtB;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAGH,IAAI,CAACI,KAAK,IAAI,SAASA,KAAKA,CAACC,CAAC,EAAE;EAC/C,IAAIC,CAAC,GAAG,CAACD,CAAC;EACV,OAAO,CAACC,CAAC,GAAG,CAAC,GAAGL,KAAK,GAAGF,IAAI,EAAEO,CAAC,CAAC;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}