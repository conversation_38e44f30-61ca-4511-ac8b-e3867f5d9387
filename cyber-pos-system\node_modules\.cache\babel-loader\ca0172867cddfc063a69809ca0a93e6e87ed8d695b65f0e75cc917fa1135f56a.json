{"ast": null, "code": "'use strict';\n\nvar classof = require('../internals/classof');\nvar $String = String;\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw new TypeError('Cannot convert a Symbol value to a string');\n  return $String(argument);\n};", "map": {"version": 3, "names": ["classof", "require", "$String", "String", "module", "exports", "argument", "TypeError"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/to-string.js"], "sourcesContent": ["'use strict';\nvar classof = require('../internals/classof');\n\nvar $String = String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw new TypeError('Cannot convert a Symbol value to a string');\n  return $String(argument);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAE7C,IAAIC,OAAO,GAAGC,MAAM;AAEpBC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIN,OAAO,CAACM,QAAQ,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAIC,SAAS,CAAC,2CAA2C,CAAC;EACpG,OAAOL,OAAO,CAACI,QAAQ,CAAC;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}