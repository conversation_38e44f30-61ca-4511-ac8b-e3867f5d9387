{"ast": null, "code": "/*! *****************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\nvar t = function (r, e) {\n  return (t = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (t, r) {\n    t.__proto__ = r;\n  } || function (t, r) {\n    for (var e in r) Object.prototype.hasOwnProperty.call(r, e) && (t[e] = r[e]);\n  })(r, e);\n};\nfunction r(r, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Class extends value \" + String(e) + \" is not a constructor or null\");\n  function i() {\n    this.constructor = r;\n  }\n  t(r, e), r.prototype = null === e ? Object.create(e) : (i.prototype = e.prototype, new i());\n}\nfunction e(t) {\n  var r = \"\";\n  Array.isArray(t) || (t = [t]);\n  for (var e = 0; e < t.length; e++) {\n    var i = t[e];\n    if (i.type === _.CLOSE_PATH) r += \"z\";else if (i.type === _.HORIZ_LINE_TO) r += (i.relative ? \"h\" : \"H\") + i.x;else if (i.type === _.VERT_LINE_TO) r += (i.relative ? \"v\" : \"V\") + i.y;else if (i.type === _.MOVE_TO) r += (i.relative ? \"m\" : \"M\") + i.x + \" \" + i.y;else if (i.type === _.LINE_TO) r += (i.relative ? \"l\" : \"L\") + i.x + \" \" + i.y;else if (i.type === _.CURVE_TO) r += (i.relative ? \"c\" : \"C\") + i.x1 + \" \" + i.y1 + \" \" + i.x2 + \" \" + i.y2 + \" \" + i.x + \" \" + i.y;else if (i.type === _.SMOOTH_CURVE_TO) r += (i.relative ? \"s\" : \"S\") + i.x2 + \" \" + i.y2 + \" \" + i.x + \" \" + i.y;else if (i.type === _.QUAD_TO) r += (i.relative ? \"q\" : \"Q\") + i.x1 + \" \" + i.y1 + \" \" + i.x + \" \" + i.y;else if (i.type === _.SMOOTH_QUAD_TO) r += (i.relative ? \"t\" : \"T\") + i.x + \" \" + i.y;else {\n      if (i.type !== _.ARC) throw new Error('Unexpected command type \"' + i.type + '\" at index ' + e + \".\");\n      r += (i.relative ? \"a\" : \"A\") + i.rX + \" \" + i.rY + \" \" + i.xRot + \" \" + +i.lArcFlag + \" \" + +i.sweepFlag + \" \" + i.x + \" \" + i.y;\n    }\n  }\n  return r;\n}\nfunction i(t, r) {\n  var e = t[0],\n    i = t[1];\n  return [e * Math.cos(r) - i * Math.sin(r), e * Math.sin(r) + i * Math.cos(r)];\n}\nfunction a() {\n  for (var t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];\n  for (var e = 0; e < t.length; e++) if (\"number\" != typeof t[e]) throw new Error(\"assertNumbers arguments[\" + e + \"] is not a number. \" + typeof t[e] + \" == typeof \" + t[e]);\n  return !0;\n}\nvar n = Math.PI;\nfunction o(t, r, e) {\n  t.lArcFlag = 0 === t.lArcFlag ? 0 : 1, t.sweepFlag = 0 === t.sweepFlag ? 0 : 1;\n  var a = t.rX,\n    o = t.rY,\n    s = t.x,\n    u = t.y;\n  a = Math.abs(t.rX), o = Math.abs(t.rY);\n  var h = i([(r - s) / 2, (e - u) / 2], -t.xRot / 180 * n),\n    c = h[0],\n    y = h[1],\n    p = Math.pow(c, 2) / Math.pow(a, 2) + Math.pow(y, 2) / Math.pow(o, 2);\n  1 < p && (a *= Math.sqrt(p), o *= Math.sqrt(p)), t.rX = a, t.rY = o;\n  var m = Math.pow(a, 2) * Math.pow(y, 2) + Math.pow(o, 2) * Math.pow(c, 2),\n    O = (t.lArcFlag !== t.sweepFlag ? 1 : -1) * Math.sqrt(Math.max(0, (Math.pow(a, 2) * Math.pow(o, 2) - m) / m)),\n    l = a * y / o * O,\n    T = -o * c / a * O,\n    v = i([l, T], t.xRot / 180 * n);\n  t.cX = v[0] + (r + s) / 2, t.cY = v[1] + (e + u) / 2, t.phi1 = Math.atan2((y - T) / o, (c - l) / a), t.phi2 = Math.atan2((-y - T) / o, (-c - l) / a), 0 === t.sweepFlag && t.phi2 > t.phi1 && (t.phi2 -= 2 * n), 1 === t.sweepFlag && t.phi2 < t.phi1 && (t.phi2 += 2 * n), t.phi1 *= 180 / n, t.phi2 *= 180 / n;\n}\nfunction s(t, r, e) {\n  a(t, r, e);\n  var i = t * t + r * r - e * e;\n  if (0 > i) return [];\n  if (0 === i) return [[t * e / (t * t + r * r), r * e / (t * t + r * r)]];\n  var n = Math.sqrt(i);\n  return [[(t * e + r * n) / (t * t + r * r), (r * e - t * n) / (t * t + r * r)], [(t * e - r * n) / (t * t + r * r), (r * e + t * n) / (t * t + r * r)]];\n}\nvar u,\n  h = Math.PI / 180;\nfunction c(t, r, e) {\n  return (1 - e) * t + e * r;\n}\nfunction y(t, r, e, i) {\n  return t + Math.cos(i / 180 * n) * r + Math.sin(i / 180 * n) * e;\n}\nfunction p(t, r, e, i) {\n  var a = 1e-6,\n    n = r - t,\n    o = e - r,\n    s = 3 * n + 3 * (i - e) - 6 * o,\n    u = 6 * (o - n),\n    h = 3 * n;\n  return Math.abs(s) < a ? [-h / u] : function (t, r, e) {\n    void 0 === e && (e = 1e-6);\n    var i = t * t / 4 - r;\n    if (i < -e) return [];\n    if (i <= e) return [-t / 2];\n    var a = Math.sqrt(i);\n    return [-t / 2 - a, -t / 2 + a];\n  }(u / s, h / s, a);\n}\nfunction m(t, r, e, i, a) {\n  var n = 1 - a;\n  return t * (n * n * n) + r * (3 * n * n * a) + e * (3 * n * a * a) + i * (a * a * a);\n}\n!function (t) {\n  function r() {\n    return u(function (t, r, e) {\n      return t.relative && (void 0 !== t.x1 && (t.x1 += r), void 0 !== t.y1 && (t.y1 += e), void 0 !== t.x2 && (t.x2 += r), void 0 !== t.y2 && (t.y2 += e), void 0 !== t.x && (t.x += r), void 0 !== t.y && (t.y += e), t.relative = !1), t;\n    });\n  }\n  function e() {\n    var t = NaN,\n      r = NaN,\n      e = NaN,\n      i = NaN;\n    return u(function (a, n, o) {\n      return a.type & _.SMOOTH_CURVE_TO && (a.type = _.CURVE_TO, t = isNaN(t) ? n : t, r = isNaN(r) ? o : r, a.x1 = a.relative ? n - t : 2 * n - t, a.y1 = a.relative ? o - r : 2 * o - r), a.type & _.CURVE_TO ? (t = a.relative ? n + a.x2 : a.x2, r = a.relative ? o + a.y2 : a.y2) : (t = NaN, r = NaN), a.type & _.SMOOTH_QUAD_TO && (a.type = _.QUAD_TO, e = isNaN(e) ? n : e, i = isNaN(i) ? o : i, a.x1 = a.relative ? n - e : 2 * n - e, a.y1 = a.relative ? o - i : 2 * o - i), a.type & _.QUAD_TO ? (e = a.relative ? n + a.x1 : a.x1, i = a.relative ? o + a.y1 : a.y1) : (e = NaN, i = NaN), a;\n    });\n  }\n  function n() {\n    var t = NaN,\n      r = NaN;\n    return u(function (e, i, a) {\n      if (e.type & _.SMOOTH_QUAD_TO && (e.type = _.QUAD_TO, t = isNaN(t) ? i : t, r = isNaN(r) ? a : r, e.x1 = e.relative ? i - t : 2 * i - t, e.y1 = e.relative ? a - r : 2 * a - r), e.type & _.QUAD_TO) {\n        t = e.relative ? i + e.x1 : e.x1, r = e.relative ? a + e.y1 : e.y1;\n        var n = e.x1,\n          o = e.y1;\n        e.type = _.CURVE_TO, e.x1 = ((e.relative ? 0 : i) + 2 * n) / 3, e.y1 = ((e.relative ? 0 : a) + 2 * o) / 3, e.x2 = (e.x + 2 * n) / 3, e.y2 = (e.y + 2 * o) / 3;\n      } else t = NaN, r = NaN;\n      return e;\n    });\n  }\n  function u(t) {\n    var r = 0,\n      e = 0,\n      i = NaN,\n      a = NaN;\n    return function (n) {\n      if (isNaN(i) && !(n.type & _.MOVE_TO)) throw new Error(\"path must start with moveto\");\n      var o = t(n, r, e, i, a);\n      return n.type & _.CLOSE_PATH && (r = i, e = a), void 0 !== n.x && (r = n.relative ? r + n.x : n.x), void 0 !== n.y && (e = n.relative ? e + n.y : n.y), n.type & _.MOVE_TO && (i = r, a = e), o;\n    };\n  }\n  function O(t, r, e, i, n, o) {\n    return a(t, r, e, i, n, o), u(function (a, s, u, h) {\n      var c = a.x1,\n        y = a.x2,\n        p = a.relative && !isNaN(h),\n        m = void 0 !== a.x ? a.x : p ? 0 : s,\n        O = void 0 !== a.y ? a.y : p ? 0 : u;\n      function l(t) {\n        return t * t;\n      }\n      a.type & _.HORIZ_LINE_TO && 0 !== r && (a.type = _.LINE_TO, a.y = a.relative ? 0 : u), a.type & _.VERT_LINE_TO && 0 !== e && (a.type = _.LINE_TO, a.x = a.relative ? 0 : s), void 0 !== a.x && (a.x = a.x * t + O * e + (p ? 0 : n)), void 0 !== a.y && (a.y = m * r + a.y * i + (p ? 0 : o)), void 0 !== a.x1 && (a.x1 = a.x1 * t + a.y1 * e + (p ? 0 : n)), void 0 !== a.y1 && (a.y1 = c * r + a.y1 * i + (p ? 0 : o)), void 0 !== a.x2 && (a.x2 = a.x2 * t + a.y2 * e + (p ? 0 : n)), void 0 !== a.y2 && (a.y2 = y * r + a.y2 * i + (p ? 0 : o));\n      var T = t * i - r * e;\n      if (void 0 !== a.xRot && (1 !== t || 0 !== r || 0 !== e || 1 !== i)) if (0 === T) delete a.rX, delete a.rY, delete a.xRot, delete a.lArcFlag, delete a.sweepFlag, a.type = _.LINE_TO;else {\n        var v = a.xRot * Math.PI / 180,\n          f = Math.sin(v),\n          N = Math.cos(v),\n          x = 1 / l(a.rX),\n          d = 1 / l(a.rY),\n          E = l(N) * x + l(f) * d,\n          A = 2 * f * N * (x - d),\n          C = l(f) * x + l(N) * d,\n          M = E * i * i - A * r * i + C * r * r,\n          R = A * (t * i + r * e) - 2 * (E * e * i + C * t * r),\n          g = E * e * e - A * t * e + C * t * t,\n          I = (Math.atan2(R, M - g) + Math.PI) % Math.PI / 2,\n          S = Math.sin(I),\n          L = Math.cos(I);\n        a.rX = Math.abs(T) / Math.sqrt(M * l(L) + R * S * L + g * l(S)), a.rY = Math.abs(T) / Math.sqrt(M * l(S) - R * S * L + g * l(L)), a.xRot = 180 * I / Math.PI;\n      }\n      return void 0 !== a.sweepFlag && 0 > T && (a.sweepFlag = +!a.sweepFlag), a;\n    });\n  }\n  function l() {\n    return function (t) {\n      var r = {};\n      for (var e in t) r[e] = t[e];\n      return r;\n    };\n  }\n  t.ROUND = function (t) {\n    function r(r) {\n      return Math.round(r * t) / t;\n    }\n    return void 0 === t && (t = 1e13), a(t), function (t) {\n      return void 0 !== t.x1 && (t.x1 = r(t.x1)), void 0 !== t.y1 && (t.y1 = r(t.y1)), void 0 !== t.x2 && (t.x2 = r(t.x2)), void 0 !== t.y2 && (t.y2 = r(t.y2)), void 0 !== t.x && (t.x = r(t.x)), void 0 !== t.y && (t.y = r(t.y)), void 0 !== t.rX && (t.rX = r(t.rX)), void 0 !== t.rY && (t.rY = r(t.rY)), t;\n    };\n  }, t.TO_ABS = r, t.TO_REL = function () {\n    return u(function (t, r, e) {\n      return t.relative || (void 0 !== t.x1 && (t.x1 -= r), void 0 !== t.y1 && (t.y1 -= e), void 0 !== t.x2 && (t.x2 -= r), void 0 !== t.y2 && (t.y2 -= e), void 0 !== t.x && (t.x -= r), void 0 !== t.y && (t.y -= e), t.relative = !0), t;\n    });\n  }, t.NORMALIZE_HVZ = function (t, r, e) {\n    return void 0 === t && (t = !0), void 0 === r && (r = !0), void 0 === e && (e = !0), u(function (i, a, n, o, s) {\n      if (isNaN(o) && !(i.type & _.MOVE_TO)) throw new Error(\"path must start with moveto\");\n      return r && i.type & _.HORIZ_LINE_TO && (i.type = _.LINE_TO, i.y = i.relative ? 0 : n), e && i.type & _.VERT_LINE_TO && (i.type = _.LINE_TO, i.x = i.relative ? 0 : a), t && i.type & _.CLOSE_PATH && (i.type = _.LINE_TO, i.x = i.relative ? o - a : o, i.y = i.relative ? s - n : s), i.type & _.ARC && (0 === i.rX || 0 === i.rY) && (i.type = _.LINE_TO, delete i.rX, delete i.rY, delete i.xRot, delete i.lArcFlag, delete i.sweepFlag), i;\n    });\n  }, t.NORMALIZE_ST = e, t.QT_TO_C = n, t.INFO = u, t.SANITIZE = function (t) {\n    void 0 === t && (t = 0), a(t);\n    var r = NaN,\n      e = NaN,\n      i = NaN,\n      n = NaN;\n    return u(function (a, o, s, u, h) {\n      var c = Math.abs,\n        y = !1,\n        p = 0,\n        m = 0;\n      if (a.type & _.SMOOTH_CURVE_TO && (p = isNaN(r) ? 0 : o - r, m = isNaN(e) ? 0 : s - e), a.type & (_.CURVE_TO | _.SMOOTH_CURVE_TO) ? (r = a.relative ? o + a.x2 : a.x2, e = a.relative ? s + a.y2 : a.y2) : (r = NaN, e = NaN), a.type & _.SMOOTH_QUAD_TO ? (i = isNaN(i) ? o : 2 * o - i, n = isNaN(n) ? s : 2 * s - n) : a.type & _.QUAD_TO ? (i = a.relative ? o + a.x1 : a.x1, n = a.relative ? s + a.y1 : a.y2) : (i = NaN, n = NaN), a.type & _.LINE_COMMANDS || a.type & _.ARC && (0 === a.rX || 0 === a.rY || !a.lArcFlag) || a.type & _.CURVE_TO || a.type & _.SMOOTH_CURVE_TO || a.type & _.QUAD_TO || a.type & _.SMOOTH_QUAD_TO) {\n        var O = void 0 === a.x ? 0 : a.relative ? a.x : a.x - o,\n          l = void 0 === a.y ? 0 : a.relative ? a.y : a.y - s;\n        p = isNaN(i) ? void 0 === a.x1 ? p : a.relative ? a.x : a.x1 - o : i - o, m = isNaN(n) ? void 0 === a.y1 ? m : a.relative ? a.y : a.y1 - s : n - s;\n        var T = void 0 === a.x2 ? 0 : a.relative ? a.x : a.x2 - o,\n          v = void 0 === a.y2 ? 0 : a.relative ? a.y : a.y2 - s;\n        c(O) <= t && c(l) <= t && c(p) <= t && c(m) <= t && c(T) <= t && c(v) <= t && (y = !0);\n      }\n      return a.type & _.CLOSE_PATH && c(o - u) <= t && c(s - h) <= t && (y = !0), y ? [] : a;\n    });\n  }, t.MATRIX = O, t.ROTATE = function (t, r, e) {\n    void 0 === r && (r = 0), void 0 === e && (e = 0), a(t, r, e);\n    var i = Math.sin(t),\n      n = Math.cos(t);\n    return O(n, i, -i, n, r - r * n + e * i, e - r * i - e * n);\n  }, t.TRANSLATE = function (t, r) {\n    return void 0 === r && (r = 0), a(t, r), O(1, 0, 0, 1, t, r);\n  }, t.SCALE = function (t, r) {\n    return void 0 === r && (r = t), a(t, r), O(t, 0, 0, r, 0, 0);\n  }, t.SKEW_X = function (t) {\n    return a(t), O(1, 0, Math.atan(t), 1, 0, 0);\n  }, t.SKEW_Y = function (t) {\n    return a(t), O(1, Math.atan(t), 0, 1, 0, 0);\n  }, t.X_AXIS_SYMMETRY = function (t) {\n    return void 0 === t && (t = 0), a(t), O(-1, 0, 0, 1, t, 0);\n  }, t.Y_AXIS_SYMMETRY = function (t) {\n    return void 0 === t && (t = 0), a(t), O(1, 0, 0, -1, 0, t);\n  }, t.A_TO_C = function () {\n    return u(function (t, r, e) {\n      return _.ARC === t.type ? function (t, r, e) {\n        var a, n, s, u;\n        t.cX || o(t, r, e);\n        for (var y = Math.min(t.phi1, t.phi2), p = Math.max(t.phi1, t.phi2) - y, m = Math.ceil(p / 90), O = new Array(m), l = r, T = e, v = 0; v < m; v++) {\n          var f = c(t.phi1, t.phi2, v / m),\n            N = c(t.phi1, t.phi2, (v + 1) / m),\n            x = N - f,\n            d = 4 / 3 * Math.tan(x * h / 4),\n            E = [Math.cos(f * h) - d * Math.sin(f * h), Math.sin(f * h) + d * Math.cos(f * h)],\n            A = E[0],\n            C = E[1],\n            M = [Math.cos(N * h), Math.sin(N * h)],\n            R = M[0],\n            g = M[1],\n            I = [R + d * Math.sin(N * h), g - d * Math.cos(N * h)],\n            S = I[0],\n            L = I[1];\n          O[v] = {\n            relative: t.relative,\n            type: _.CURVE_TO\n          };\n          var H = function (r, e) {\n            var a = i([r * t.rX, e * t.rY], t.xRot),\n              n = a[0],\n              o = a[1];\n            return [t.cX + n, t.cY + o];\n          };\n          a = H(A, C), O[v].x1 = a[0], O[v].y1 = a[1], n = H(S, L), O[v].x2 = n[0], O[v].y2 = n[1], s = H(R, g), O[v].x = s[0], O[v].y = s[1], t.relative && (O[v].x1 -= l, O[v].y1 -= T, O[v].x2 -= l, O[v].y2 -= T, O[v].x -= l, O[v].y -= T), l = (u = [O[v].x, O[v].y])[0], T = u[1];\n        }\n        return O;\n      }(t, t.relative ? 0 : r, t.relative ? 0 : e) : t;\n    });\n  }, t.ANNOTATE_ARCS = function () {\n    return u(function (t, r, e) {\n      return t.relative && (r = 0, e = 0), _.ARC === t.type && o(t, r, e), t;\n    });\n  }, t.CLONE = l, t.CALCULATE_BOUNDS = function () {\n    var t = function (t) {\n        var r = {};\n        for (var e in t) r[e] = t[e];\n        return r;\n      },\n      i = r(),\n      a = n(),\n      h = e(),\n      c = u(function (r, e, n) {\n        var u = h(a(i(t(r))));\n        function O(t) {\n          t > c.maxX && (c.maxX = t), t < c.minX && (c.minX = t);\n        }\n        function l(t) {\n          t > c.maxY && (c.maxY = t), t < c.minY && (c.minY = t);\n        }\n        if (u.type & _.DRAWING_COMMANDS && (O(e), l(n)), u.type & _.HORIZ_LINE_TO && O(u.x), u.type & _.VERT_LINE_TO && l(u.y), u.type & _.LINE_TO && (O(u.x), l(u.y)), u.type & _.CURVE_TO) {\n          O(u.x), l(u.y);\n          for (var T = 0, v = p(e, u.x1, u.x2, u.x); T < v.length; T++) {\n            0 < (w = v[T]) && 1 > w && O(m(e, u.x1, u.x2, u.x, w));\n          }\n          for (var f = 0, N = p(n, u.y1, u.y2, u.y); f < N.length; f++) {\n            0 < (w = N[f]) && 1 > w && l(m(n, u.y1, u.y2, u.y, w));\n          }\n        }\n        if (u.type & _.ARC) {\n          O(u.x), l(u.y), o(u, e, n);\n          for (var x = u.xRot / 180 * Math.PI, d = Math.cos(x) * u.rX, E = Math.sin(x) * u.rX, A = -Math.sin(x) * u.rY, C = Math.cos(x) * u.rY, M = u.phi1 < u.phi2 ? [u.phi1, u.phi2] : -180 > u.phi2 ? [u.phi2 + 360, u.phi1 + 360] : [u.phi2, u.phi1], R = M[0], g = M[1], I = function (t) {\n              var r = t[0],\n                e = t[1],\n                i = 180 * Math.atan2(e, r) / Math.PI;\n              return i < R ? i + 360 : i;\n            }, S = 0, L = s(A, -d, 0).map(I); S < L.length; S++) {\n            (w = L[S]) > R && w < g && O(y(u.cX, d, A, w));\n          }\n          for (var H = 0, U = s(C, -E, 0).map(I); H < U.length; H++) {\n            var w;\n            (w = U[H]) > R && w < g && l(y(u.cY, E, C, w));\n          }\n        }\n        return r;\n      });\n    return c.minX = 1 / 0, c.maxX = -1 / 0, c.minY = 1 / 0, c.maxY = -1 / 0, c;\n  };\n}(u || (u = {}));\nvar O,\n  l = function () {\n    function t() {}\n    return t.prototype.round = function (t) {\n      return this.transform(u.ROUND(t));\n    }, t.prototype.toAbs = function () {\n      return this.transform(u.TO_ABS());\n    }, t.prototype.toRel = function () {\n      return this.transform(u.TO_REL());\n    }, t.prototype.normalizeHVZ = function (t, r, e) {\n      return this.transform(u.NORMALIZE_HVZ(t, r, e));\n    }, t.prototype.normalizeST = function () {\n      return this.transform(u.NORMALIZE_ST());\n    }, t.prototype.qtToC = function () {\n      return this.transform(u.QT_TO_C());\n    }, t.prototype.aToC = function () {\n      return this.transform(u.A_TO_C());\n    }, t.prototype.sanitize = function (t) {\n      return this.transform(u.SANITIZE(t));\n    }, t.prototype.translate = function (t, r) {\n      return this.transform(u.TRANSLATE(t, r));\n    }, t.prototype.scale = function (t, r) {\n      return this.transform(u.SCALE(t, r));\n    }, t.prototype.rotate = function (t, r, e) {\n      return this.transform(u.ROTATE(t, r, e));\n    }, t.prototype.matrix = function (t, r, e, i, a, n) {\n      return this.transform(u.MATRIX(t, r, e, i, a, n));\n    }, t.prototype.skewX = function (t) {\n      return this.transform(u.SKEW_X(t));\n    }, t.prototype.skewY = function (t) {\n      return this.transform(u.SKEW_Y(t));\n    }, t.prototype.xSymmetry = function (t) {\n      return this.transform(u.X_AXIS_SYMMETRY(t));\n    }, t.prototype.ySymmetry = function (t) {\n      return this.transform(u.Y_AXIS_SYMMETRY(t));\n    }, t.prototype.annotateArcs = function () {\n      return this.transform(u.ANNOTATE_ARCS());\n    }, t;\n  }(),\n  T = function (t) {\n    return \" \" === t || \"\\t\" === t || \"\\r\" === t || \"\\n\" === t;\n  },\n  v = function (t) {\n    return \"0\".charCodeAt(0) <= t.charCodeAt(0) && t.charCodeAt(0) <= \"9\".charCodeAt(0);\n  },\n  f = function (t) {\n    function e() {\n      var r = t.call(this) || this;\n      return r.curNumber = \"\", r.curCommandType = -1, r.curCommandRelative = !1, r.canParseCommandOrComma = !0, r.curNumberHasExp = !1, r.curNumberHasExpDigits = !1, r.curNumberHasDecimal = !1, r.curArgs = [], r;\n    }\n    return r(e, t), e.prototype.finish = function (t) {\n      if (void 0 === t && (t = []), this.parse(\" \", t), 0 !== this.curArgs.length || !this.canParseCommandOrComma) throw new SyntaxError(\"Unterminated command at the path end.\");\n      return t;\n    }, e.prototype.parse = function (t, r) {\n      var e = this;\n      void 0 === r && (r = []);\n      for (var i = function (t) {\n          r.push(t), e.curArgs.length = 0, e.canParseCommandOrComma = !0;\n        }, a = 0; a < t.length; a++) {\n        var n = t[a],\n          o = !(this.curCommandType !== _.ARC || 3 !== this.curArgs.length && 4 !== this.curArgs.length || 1 !== this.curNumber.length || \"0\" !== this.curNumber && \"1\" !== this.curNumber),\n          s = v(n) && (\"0\" === this.curNumber && \"0\" === n || o);\n        if (!v(n) || s) {\n          if (\"e\" !== n && \"E\" !== n) {\n            if (\"-\" !== n && \"+\" !== n || !this.curNumberHasExp || this.curNumberHasExpDigits) {\n              if (\".\" !== n || this.curNumberHasExp || this.curNumberHasDecimal || o) {\n                if (this.curNumber && -1 !== this.curCommandType) {\n                  var u = Number(this.curNumber);\n                  if (isNaN(u)) throw new SyntaxError(\"Invalid number ending at \" + a);\n                  if (this.curCommandType === _.ARC) if (0 === this.curArgs.length || 1 === this.curArgs.length) {\n                    if (0 > u) throw new SyntaxError('Expected positive number, got \"' + u + '\" at index \"' + a + '\"');\n                  } else if ((3 === this.curArgs.length || 4 === this.curArgs.length) && \"0\" !== this.curNumber && \"1\" !== this.curNumber) throw new SyntaxError('Expected a flag, got \"' + this.curNumber + '\" at index \"' + a + '\"');\n                  this.curArgs.push(u), this.curArgs.length === N[this.curCommandType] && (_.HORIZ_LINE_TO === this.curCommandType ? i({\n                    type: _.HORIZ_LINE_TO,\n                    relative: this.curCommandRelative,\n                    x: u\n                  }) : _.VERT_LINE_TO === this.curCommandType ? i({\n                    type: _.VERT_LINE_TO,\n                    relative: this.curCommandRelative,\n                    y: u\n                  }) : this.curCommandType === _.MOVE_TO || this.curCommandType === _.LINE_TO || this.curCommandType === _.SMOOTH_QUAD_TO ? (i({\n                    type: this.curCommandType,\n                    relative: this.curCommandRelative,\n                    x: this.curArgs[0],\n                    y: this.curArgs[1]\n                  }), _.MOVE_TO === this.curCommandType && (this.curCommandType = _.LINE_TO)) : this.curCommandType === _.CURVE_TO ? i({\n                    type: _.CURVE_TO,\n                    relative: this.curCommandRelative,\n                    x1: this.curArgs[0],\n                    y1: this.curArgs[1],\n                    x2: this.curArgs[2],\n                    y2: this.curArgs[3],\n                    x: this.curArgs[4],\n                    y: this.curArgs[5]\n                  }) : this.curCommandType === _.SMOOTH_CURVE_TO ? i({\n                    type: _.SMOOTH_CURVE_TO,\n                    relative: this.curCommandRelative,\n                    x2: this.curArgs[0],\n                    y2: this.curArgs[1],\n                    x: this.curArgs[2],\n                    y: this.curArgs[3]\n                  }) : this.curCommandType === _.QUAD_TO ? i({\n                    type: _.QUAD_TO,\n                    relative: this.curCommandRelative,\n                    x1: this.curArgs[0],\n                    y1: this.curArgs[1],\n                    x: this.curArgs[2],\n                    y: this.curArgs[3]\n                  }) : this.curCommandType === _.ARC && i({\n                    type: _.ARC,\n                    relative: this.curCommandRelative,\n                    rX: this.curArgs[0],\n                    rY: this.curArgs[1],\n                    xRot: this.curArgs[2],\n                    lArcFlag: this.curArgs[3],\n                    sweepFlag: this.curArgs[4],\n                    x: this.curArgs[5],\n                    y: this.curArgs[6]\n                  })), this.curNumber = \"\", this.curNumberHasExpDigits = !1, this.curNumberHasExp = !1, this.curNumberHasDecimal = !1, this.canParseCommandOrComma = !0;\n                }\n                if (!T(n)) if (\",\" === n && this.canParseCommandOrComma) this.canParseCommandOrComma = !1;else if (\"+\" !== n && \"-\" !== n && \".\" !== n) {\n                  if (s) this.curNumber = n, this.curNumberHasDecimal = !1;else {\n                    if (0 !== this.curArgs.length) throw new SyntaxError(\"Unterminated command at index \" + a + \".\");\n                    if (!this.canParseCommandOrComma) throw new SyntaxError('Unexpected character \"' + n + '\" at index ' + a + \". Command cannot follow comma\");\n                    if (this.canParseCommandOrComma = !1, \"z\" !== n && \"Z\" !== n) {\n                      if (\"h\" === n || \"H\" === n) this.curCommandType = _.HORIZ_LINE_TO, this.curCommandRelative = \"h\" === n;else if (\"v\" === n || \"V\" === n) this.curCommandType = _.VERT_LINE_TO, this.curCommandRelative = \"v\" === n;else if (\"m\" === n || \"M\" === n) this.curCommandType = _.MOVE_TO, this.curCommandRelative = \"m\" === n;else if (\"l\" === n || \"L\" === n) this.curCommandType = _.LINE_TO, this.curCommandRelative = \"l\" === n;else if (\"c\" === n || \"C\" === n) this.curCommandType = _.CURVE_TO, this.curCommandRelative = \"c\" === n;else if (\"s\" === n || \"S\" === n) this.curCommandType = _.SMOOTH_CURVE_TO, this.curCommandRelative = \"s\" === n;else if (\"q\" === n || \"Q\" === n) this.curCommandType = _.QUAD_TO, this.curCommandRelative = \"q\" === n;else if (\"t\" === n || \"T\" === n) this.curCommandType = _.SMOOTH_QUAD_TO, this.curCommandRelative = \"t\" === n;else {\n                        if (\"a\" !== n && \"A\" !== n) throw new SyntaxError('Unexpected character \"' + n + '\" at index ' + a + \".\");\n                        this.curCommandType = _.ARC, this.curCommandRelative = \"a\" === n;\n                      }\n                    } else r.push({\n                      type: _.CLOSE_PATH\n                    }), this.canParseCommandOrComma = !0, this.curCommandType = -1;\n                  }\n                } else this.curNumber = n, this.curNumberHasDecimal = \".\" === n;\n              } else this.curNumber += n, this.curNumberHasDecimal = !0;\n            } else this.curNumber += n;\n          } else this.curNumber += n, this.curNumberHasExp = !0;\n        } else this.curNumber += n, this.curNumberHasExpDigits = this.curNumberHasExp;\n      }\n      return r;\n    }, e.prototype.transform = function (t) {\n      return Object.create(this, {\n        parse: {\n          value: function (r, e) {\n            void 0 === e && (e = []);\n            for (var i = 0, a = Object.getPrototypeOf(this).parse.call(this, r); i < a.length; i++) {\n              var n = a[i],\n                o = t(n);\n              Array.isArray(o) ? e.push.apply(e, o) : e.push(o);\n            }\n            return e;\n          }\n        }\n      });\n    }, e;\n  }(l),\n  _ = function (t) {\n    function i(r) {\n      var e = t.call(this) || this;\n      return e.commands = \"string\" == typeof r ? i.parse(r) : r, e;\n    }\n    return r(i, t), i.prototype.encode = function () {\n      return i.encode(this.commands);\n    }, i.prototype.getBounds = function () {\n      var t = u.CALCULATE_BOUNDS();\n      return this.transform(t), t;\n    }, i.prototype.transform = function (t) {\n      for (var r = [], e = 0, i = this.commands; e < i.length; e++) {\n        var a = t(i[e]);\n        Array.isArray(a) ? r.push.apply(r, a) : r.push(a);\n      }\n      return this.commands = r, this;\n    }, i.encode = function (t) {\n      return e(t);\n    }, i.parse = function (t) {\n      var r = new f(),\n        e = [];\n      return r.parse(t, e), r.finish(e), e;\n    }, i.CLOSE_PATH = 1, i.MOVE_TO = 2, i.HORIZ_LINE_TO = 4, i.VERT_LINE_TO = 8, i.LINE_TO = 16, i.CURVE_TO = 32, i.SMOOTH_CURVE_TO = 64, i.QUAD_TO = 128, i.SMOOTH_QUAD_TO = 256, i.ARC = 512, i.LINE_COMMANDS = i.LINE_TO | i.HORIZ_LINE_TO | i.VERT_LINE_TO, i.DRAWING_COMMANDS = i.HORIZ_LINE_TO | i.VERT_LINE_TO | i.LINE_TO | i.CURVE_TO | i.SMOOTH_CURVE_TO | i.QUAD_TO | i.SMOOTH_QUAD_TO | i.ARC, i;\n  }(l),\n  N = ((O = {})[_.MOVE_TO] = 2, O[_.LINE_TO] = 2, O[_.HORIZ_LINE_TO] = 1, O[_.VERT_LINE_TO] = 1, O[_.CLOSE_PATH] = 0, O[_.QUAD_TO] = 4, O[_.SMOOTH_QUAD_TO] = 2, O[_.CURVE_TO] = 6, O[_.SMOOTH_CURVE_TO] = 4, O[_.ARC] = 7, O);\nexport { N as COMMAND_ARG_COUNTS, _ as SVGPathData, f as SVGPathDataParser, u as SVGPathDataTransformer, e as encodeSVGPath };", "map": {"version": 3, "names": ["t", "extendStatics", "r", "e", "Object", "setPrototypeOf", "__proto__", "Array", "prototype", "hasOwnProperty", "call", "TypeError", "String", "i", "constructor", "create", "isArray", "length", "type", "_", "CLOSE_PATH", "HORIZ_LINE_TO", "relative", "x", "VERT_LINE_TO", "y", "MOVE_TO", "LINE_TO", "CURVE_TO", "x1", "y1", "x2", "y2", "SMOOTH_CURVE_TO", "QUAD_TO", "SMOOTH_QUAD_TO", "ARC", "Error", "rX", "rY", "xRot", "lArcFlag", "sweepFlag", "Math", "cos", "sin", "a", "arguments", "n", "PI", "o", "s", "u", "abs", "h", "c", "p", "pow", "sqrt", "m", "O", "max", "l", "T", "v", "cX", "cY", "phi1", "atan2", "phi2", "NaN", "isNaN", "f", "N", "d", "E", "A", "C", "M", "R", "g", "I", "S", "L", "ROUND", "round", "TO_ABS", "TO_REL", "NORMALIZE_HVZ", "NORMALIZE_ST", "QT_TO_C", "INFO", "SANITIZE", "LINE_COMMANDS", "MATRIX", "ROTATE", "TRANSLATE", "SCALE", "SKEW_X", "atan", "SKEW_Y", "X_AXIS_SYMMETRY", "Y_AXIS_SYMMETRY", "A_TO_C", "min", "ceil", "tan", "H", "transform", "ANNOTATE_ARCS", "CLONE", "CALCULATE_BOUNDS", "clone", "maxX", "minX", "maxY", "minY", "DRAWING_COMMANDS", "w", "normalizeXiEta", "map", "U", "toAbs", "toRel", "normalizeHVZ", "normalizeST", "qtToC", "aToC", "sanitize", "translate", "scale", "rotate", "matrix", "skewX", "skewY", "xSymmetry", "ySymmetry", "annotateArcs", "isWhiteSpace", "isDigit", "charCodeAt", "curN<PERSON>ber", "curCommandType", "curCommandRelative", "canParseCommandOrComma", "curNumberHasExp", "curNumberHasExpDigits", "curNumberHasDecimal", "curArgs", "finish", "parse", "SyntaxError", "finishCommand", "push", "Number", "value", "getPrototypeOf", "apply", "commands", "encode", "getBounds", "COMMAND_ARG_COUNTS", "SVGPathData", "SVGPathData<PERSON><PERSON><PERSON>", "SVGPathDataTransformer", "encodeSVGPath"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\svg-pathdata\\node_modules\\tslib\\tslib.es6.js", "E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\svg-pathdata\\src\\SVGPathDataEncoder.ts", "E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\svg-pathdata\\src\\mathUtils.ts", "E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\svg-pathdata\\src\\SVGPathDataTransformer.ts", "E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\svg-pathdata\\src\\TransformableSVG.ts", "E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\svg-pathdata\\src\\SVGPathDataParser.ts", "E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\svg-pathdata\\src\\SVGPathData.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { SVGPathData } from \"./SVGPathData\";\nimport { SVGCommand } from \"./types\";\n\n// Encode SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\n\n// Private consts : Char groups\nconst WSP = \" \";\n\nexport function encodeSVGPath(commands: SVGCommand | SVGCommand[]) {\n  let str = \"\";\n\n  if (!Array.isArray(commands)) {\n    commands = [commands];\n  }\n  for (let i = 0; i < commands.length; i++) {\n    const command = commands[i];\n    if (command.type === SVGPathData.CLOSE_PATH) {\n      str += \"z\";\n    } else if (command.type === SVGPathData.HORIZ_LINE_TO) {\n      str += (command.relative ? \"h\" : \"H\") +\n        command.x;\n    } else if (command.type === SVGPathData.VERT_LINE_TO) {\n      str += (command.relative ? \"v\" : \"V\") +\n        command.y;\n    } else if (command.type === SVGPathData.MOVE_TO) {\n      str += (command.relative ? \"m\" : \"M\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.LINE_TO) {\n      str += (command.relative ? \"l\" : \"L\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.CURVE_TO) {\n      str += (command.relative ? \"c\" : \"C\") +\n        command.x1 + WSP + command.y1 +\n        WSP + command.x2 + WSP + command.y2 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.SMOOTH_CURVE_TO) {\n      str += (command.relative ? \"s\" : \"S\") +\n        command.x2 + WSP + command.y2 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.QUAD_TO) {\n      str += (command.relative ? \"q\" : \"Q\") +\n        command.x1 + WSP + command.y1 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.SMOOTH_QUAD_TO) {\n      str += (command.relative ? \"t\" : \"T\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.ARC) {\n      str += (command.relative ? \"a\" : \"A\") +\n        command.rX + WSP + command.rY +\n        WSP + command.xRot +\n        WSP + (+command.lArcFlag) + WSP + (+command.sweepFlag) +\n        WSP + command.x + WSP + command.y;\n    } else {\n      // Unknown command\n      throw new Error(\n        `Unexpected command type \"${ (command as any).type}\" at index ${i}.`);\n    }\n  }\n\n  return str;\n}\n", "import { SVGPathData } from \"./SVGPathData\";\nimport { CommandA, CommandC } from \"./types\";\n\nexport function rotate([x, y]: [number, number], rad: number) {\n  return [\n    x * Math.cos(rad) - y * Math.sin(rad),\n    x * Math.sin(rad) + y * Math.cos(rad),\n  ];\n}\n\nconst DEBUG_CHECK_NUMBERS = true;\nexport function assertNumbers(...numbers: number[]) {\n  if (DEBUG_CHECK_NUMBERS) {\n    for (let i = 0; i < numbers.length; i++) {\n      if (\"number\" !== typeof numbers[i]) {\n        throw new Error(\n          `assertNumbers arguments[${i}] is not a number. ${typeof numbers[i]} == typeof ${numbers[i]}`);\n      }\n    }\n  }\n  return true;\n}\n\nconst PI = Math.PI;\n\n/**\n * https://www.w3.org/TR/SVG/implnote.html#ArcImplementationNotes\n * Fixes rX and rY.\n * Ensures lArcFlag and sweepFlag are 0 or 1\n * Adds center coordinates: command.cX, command.cY (relative or absolute, depending on command.relative)\n * Adds start and end arc parameters (in degrees): command.phi1, command.phi2; phi1 < phi2 iff. c.sweepFlag == true\n */\nexport function annotateArcCommand(c: CommandA, x1: number, y1: number) {\n  c.lArcFlag = (0 === c.lArcFlag) ? 0 : 1;\n  c.sweepFlag = (0 === c.sweepFlag) ? 0 : 1;\n  // tslint:disable-next-line\n  let {rX, rY, x, y} = c;\n\n  rX = Math.abs(c.rX);\n  rY = Math.abs(c.rY);\n  const [x1_, y1_] = rotate([(x1 - x) / 2, (y1 - y) / 2], -c.xRot / 180 * PI);\n  const testValue = Math.pow(x1_, 2) / Math.pow(rX, 2) + Math.pow(y1_, 2) / Math.pow(rY, 2);\n\n  if (1 < testValue) {\n    rX *= Math.sqrt(testValue);\n    rY *= Math.sqrt(testValue);\n  }\n  c.rX = rX;\n  c.rY = rY;\n  const c_ScaleTemp = (Math.pow(rX, 2) * Math.pow(y1_, 2) + Math.pow(rY, 2) * Math.pow(x1_, 2));\n  const c_Scale = (c.lArcFlag !== c.sweepFlag ? 1 : -1) *\n    Math.sqrt(Math.max(0, (Math.pow(rX, 2) * Math.pow(rY, 2) - c_ScaleTemp) / c_ScaleTemp));\n  const cx_ = rX * y1_ / rY * c_Scale;\n  const cy_ = -rY * x1_ / rX * c_Scale;\n  const cRot = rotate([cx_, cy_], c.xRot / 180 * PI);\n\n  c.cX = cRot[0] + (x1 + x) / 2;\n  c.cY = cRot[1] + (y1 + y) / 2;\n  c.phi1 = Math.atan2((y1_ - cy_) / rY, (x1_ - cx_) / rX);\n  c.phi2 = Math.atan2((-y1_ - cy_) / rY, (-x1_ - cx_) / rX);\n  if (0 === c.sweepFlag && c.phi2 > c.phi1) {\n    c.phi2 -= 2 * PI;\n  }\n  if (1 === c.sweepFlag && c.phi2 < c.phi1) {\n    c.phi2 += 2 * PI;\n  }\n  c.phi1 *= 180 / PI;\n  c.phi2 *= 180 / PI;\n}\n\n/**\n * Solves a quadratic system of equations of the form\n *      a * x + b * y = c\n *      x² + y² = 1\n * This can be understood as the intersection of the unit circle with a line.\n *      => y = (c - a x) / b\n *      => x² + (c - a x)² / b² = 1\n *      => x² b² + c² - 2 c a x + a² x² = b²\n *      => (a² + b²) x² - 2 a c x + (c² - b²) = 0\n */\nexport function intersectionUnitCircleLine(a: number, b: number, c: number): [number, number][] {\n  assertNumbers(a, b, c);\n  // cf. pqFormula\n  const termSqr = a * a + b * b - c * c;\n\n  if (0 > termSqr) {\n    return [];\n  } else if (0 === termSqr) {\n    return [\n      [\n        (a * c) / (a * a + b * b),\n        (b * c) / (a * a + b * b)]];\n  }\n  const term = Math.sqrt(termSqr);\n\n  return [\n    [\n      (a * c + b * term) / (a * a + b * b),\n      (b * c - a * term) / (a * a + b * b)],\n    [\n      (a * c - b * term) / (a * a + b * b),\n      (b * c + a * term) / (a * a + b * b)]];\n\n}\n\nexport const DEG = Math.PI / 180;\n\nexport function lerp(a: number, b: number, t: number) {\n  return (1 - t) * a + t * b;\n}\n\nexport function arcAt(c: number, x1: number, x2: number, phiDeg: number) {\n  return c + Math.cos(phiDeg / 180 * PI) * x1 + Math.sin(phiDeg / 180 * PI) * x2;\n}\n\nexport function bezierRoot(x0: number, x1: number, x2: number, x3: number) {\n  const EPS = 1e-6;\n  const x01 = x1 - x0;\n  const x12 = x2 - x1;\n  const x23 = x3 - x2;\n  const a = 3 * x01 + 3 * x23 - 6 * x12;\n  const b = (x12 - x01) * 6;\n  const c = 3 * x01;\n  // solve a * t² + b * t + c = 0\n\n  if (Math.abs(a) < EPS) {\n    // equivalent to b * t + c =>\n    return [-c / b];\n  }\n  return pqFormula(b / a, c / a, EPS);\n\n}\n\nexport function bezierAt(x0: number, x1: number, x2: number, x3: number, t: number) {\n  // console.log(x0, y0, x1, y1, x2, y2, x3, y3, t)\n  const s = 1 - t;\n  const c0 = s * s * s;\n  const c1 = 3 * s * s * t;\n  const c2 = 3 * s * t * t;\n  const c3 = t * t * t;\n\n  return x0 * c0 + x1 * c1 + x2 * c2 + x3 * c3;\n}\n\nfunction pqFormula(p: number, q: number, PRECISION = 1e-6) {\n  // 4 times the discriminant:in\n  const discriminantX4 = p * p / 4 - q;\n\n  if (discriminantX4 < -PRECISION) {\n    return [];\n  } else if (discriminantX4 <= PRECISION) {\n    return [-p / 2];\n  }\n  const root = Math.sqrt(discriminantX4);\n\n  return [-(p / 2) - root, -(p / 2) + root];\n\n}\n\nexport function a2c(arc: CommandA, x0: number, y0: number): CommandC[] {\n  if (!arc.cX) {\n    annotateArcCommand(arc, x0, y0);\n  }\n\n  const phiMin = Math.min(arc.phi1!, arc.phi2!), phiMax = Math.max(arc.phi1!, arc.phi2!), deltaPhi = phiMax - phiMin;\n  const partCount = Math.ceil(deltaPhi / 90 );\n\n  const result: CommandC[] = new Array(partCount);\n  let prevX = x0, prevY = y0;\n  for (let i = 0; i < partCount; i++) {\n    const phiStart = lerp(arc.phi1!, arc.phi2!, i / partCount);\n    const phiEnd = lerp(arc.phi1!, arc.phi2!, (i + 1) / partCount);\n    const deltaPhi = phiEnd - phiStart;\n    const f = 4 / 3 * Math.tan(deltaPhi * DEG / 4);\n    // x1/y1, x2/y2 and x/y coordinates on the unit circle for phiStart/phiEnd\n    const [x1, y1] = [\n      Math.cos(phiStart * DEG) - f * Math.sin(phiStart * DEG),\n      Math.sin(phiStart * DEG) + f * Math.cos(phiStart * DEG)];\n    const [x, y] = [Math.cos(phiEnd * DEG), Math.sin(phiEnd * DEG)];\n    const [x2, y2] = [x + f * Math.sin(phiEnd * DEG), y - f * Math.cos(phiEnd * DEG)];\n    result[i] = {relative: arc.relative, type: SVGPathData.CURVE_TO } as any;\n    const transform = (x: number, y: number) => {\n      const [xTemp, yTemp] = rotate([x * arc.rX, y * arc.rY], arc.xRot);\n      return [arc.cX! + xTemp, arc.cY! + yTemp];\n    };\n    [result[i].x1, result[i].y1] = transform(x1, y1);\n    [result[i].x2, result[i].y2] = transform(x2, y2);\n    [result[i].x, result[i].y] = transform(x, y);\n    if (arc.relative) {\n      result[i].x1 -= prevX;\n      result[i].y1 -= prevY;\n      result[i].x2 -= prevX;\n      result[i].y2 -= prevY;\n      result[i].x -= prevX;\n      result[i].y -= prevY;\n    }\n    [prevX, prevY] = [result[i].x, result[i].y];\n  }\n  return result;\n}\n", "// Transform SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\n\nimport { a2c, annotateArcCommand, arcAt, assertNumbers, bezierAt, bezierRoot,\n  intersectionUnitCircleLine } from \"./mathUtils\";\nimport { SVGPathData } from \"./SVGPathData\";\nimport { SVGCommand, TransformFunction } from \"./types\";\n\nexport namespace SVGPathDataTransformer {\n  // Predefined transforming functions\n  // Rounds commands values\n  export function ROUND(roundVal = 1e13) {\n    assertNumbers(roundVal);\n    function rf(val: number) { return Math.round(val * roundVal) / roundVal; }\n    return function round(command: any) {\n      if (\"undefined\" !== typeof command.x1) {\n        command.x1 = rf(command.x1);\n      }\n      if (\"undefined\" !== typeof command.y1) {\n        command.y1 = rf(command.y1);\n      }\n\n      if (\"undefined\" !== typeof command.x2) {\n        command.x2 = rf(command.x2);\n      }\n      if (\"undefined\" !== typeof command.y2) {\n        command.y2 = rf(command.y2);\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        command.x = rf(command.x);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        command.y = rf(command.y);\n      }\n\n      if (\"undefined\" !== typeof command.rX) {\n        command.rX = rf(command.rX);\n      }\n      if (\"undefined\" !== typeof command.rY) {\n        command.rY = rf(command.rY);\n      }\n\n      return command;\n    };\n  }\n  // Relative to absolute commands\n  export function TO_ABS() {\n    return INFO((command, prevX, prevY) => {\n      if (command.relative) {\n        // x1/y1 values\n        if (\"undefined\" !== typeof command.x1) {\n          command.x1 += prevX;\n        }\n        if (\"undefined\" !== typeof command.y1) {\n          command.y1 += prevY;\n        }\n        // x2/y2 values\n        if (\"undefined\" !== typeof command.x2) {\n          command.x2 += prevX;\n        }\n        if (\"undefined\" !== typeof command.y2) {\n          command.y2 += prevY;\n        }\n        // Finally x/y values\n        if (\"undefined\" !== typeof command.x) {\n          command.x += prevX;\n        }\n        if (\"undefined\" !== typeof command.y) {\n          command.y += prevY;\n        }\n        command.relative = false;\n      }\n      return command;\n    });\n  }\n  // Absolute to relative commands\n  export function TO_REL() {\n    return INFO((command, prevX, prevY) => {\n      if (!command.relative) {\n        // x1/y1 values\n        if (\"undefined\" !== typeof command.x1) {\n          command.x1 -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y1) {\n          command.y1 -= prevY;\n        }\n        // x2/y2 values\n        if (\"undefined\" !== typeof command.x2) {\n          command.x2 -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y2) {\n          command.y2 -= prevY;\n        }\n        // Finally x/y values\n        if (\"undefined\" !== typeof command.x) {\n          command.x -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y) {\n          command.y -= prevY;\n        }\n        command.relative = true;\n      }\n      return command;\n    });\n  }\n  // Convert H, V, Z and A with rX = 0 to L\n  export function NORMALIZE_HVZ(normalizeZ = true, normalizeH = true, normalizeV = true) {\n    return INFO((command, prevX, prevY, pathStartX, pathStartY) => {\n      if (isNaN(pathStartX) && !(command.type & SVGPathData.MOVE_TO)) {\n        throw new Error(\"path must start with moveto\");\n      }\n      if (normalizeH && command.type & SVGPathData.HORIZ_LINE_TO) {\n        command.type = SVGPathData.LINE_TO;\n        command.y = command.relative ? 0 : prevY;\n      }\n      if (normalizeV && command.type & SVGPathData.VERT_LINE_TO) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? 0 : prevX;\n      }\n      if (normalizeZ && command.type & SVGPathData.CLOSE_PATH) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? pathStartX - prevX : pathStartX;\n        command.y = command.relative ? pathStartY - prevY : pathStartY;\n      }\n      if (command.type & SVGPathData.ARC && (0 === command.rX || 0 === command.rY)) {\n        command.type = SVGPathData.LINE_TO;\n        delete command.rX;\n        delete command.rY;\n        delete command.xRot;\n        delete command.lArcFlag;\n        delete command.sweepFlag;\n      }\n      return command;\n    });\n  }\n  /*\n   * Transforms smooth curves and quads to normal curves and quads (SsTt to CcQq)\n   */\n  export function NORMALIZE_ST() {\n    let prevCurveC2X = NaN;\n    let prevCurveC2Y = NaN;\n    let prevQuadCX = NaN;\n    let prevQuadCY = NaN;\n\n    return INFO((command, prevX, prevY) => {\n      if (command.type & SVGPathData.SMOOTH_CURVE_TO) {\n        command.type = SVGPathData.CURVE_TO;\n        prevCurveC2X = isNaN(prevCurveC2X) ? prevX : prevCurveC2X;\n        prevCurveC2Y = isNaN(prevCurveC2Y) ? prevY : prevCurveC2Y;\n        command.x1 = command.relative ? prevX - prevCurveC2X : 2 * prevX - prevCurveC2X;\n        command.y1 = command.relative ? prevY - prevCurveC2Y : 2 * prevY - prevCurveC2Y;\n      }\n      if (command.type & SVGPathData.CURVE_TO) {\n        prevCurveC2X = command.relative ? prevX + command.x2 : command.x2;\n        prevCurveC2Y = command.relative ? prevY + command.y2 : command.y2;\n      } else {\n        prevCurveC2X = NaN;\n        prevCurveC2Y = NaN;\n      }\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        command.type = SVGPathData.QUAD_TO;\n        prevQuadCX = isNaN(prevQuadCX) ? prevX : prevQuadCX;\n        prevQuadCY = isNaN(prevQuadCY) ? prevY : prevQuadCY;\n        command.x1 = command.relative ? prevX - prevQuadCX : 2 * prevX - prevQuadCX;\n        command.y1 = command.relative ? prevY - prevQuadCY : 2 * prevY - prevQuadCY;\n      }\n      if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadCX = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadCY = command.relative ? prevY + command.y1 : command.y1;\n      } else {\n        prevQuadCX = NaN;\n        prevQuadCY = NaN;\n      }\n\n      return command;\n    });\n  }\n  /*\n   * A quadratic bézier curve can be represented by a cubic bézier curve which has\n   * the same end points as the quadratic and both control points in place of the\n   * quadratic\"s one.\n   *\n   * This transformer replaces QqTt commands with Cc commands respectively.\n   * This is useful for reading path data into a system which only has a\n   * representation for cubic curves.\n   */\n  export function QT_TO_C() {\n    let prevQuadX1 = NaN;\n    let prevQuadY1 = NaN;\n\n    return INFO((command, prevX, prevY) => {\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        command.type = SVGPathData.QUAD_TO;\n        prevQuadX1 = isNaN(prevQuadX1) ? prevX : prevQuadX1;\n        prevQuadY1 = isNaN(prevQuadY1) ? prevY : prevQuadY1;\n        command.x1 = command.relative ? prevX - prevQuadX1 : 2 * prevX - prevQuadX1;\n        command.y1 = command.relative ? prevY - prevQuadY1 : 2 * prevY - prevQuadY1;\n      }\n      if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadX1 = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadY1 = command.relative ? prevY + command.y1 : command.y1;\n        const x1 = command.x1;\n        const y1 = command.y1;\n\n        command.type = SVGPathData.CURVE_TO;\n        command.x1 = ((command.relative ? 0 : prevX) + x1 * 2) / 3;\n        command.y1 = ((command.relative ? 0 : prevY) + y1 * 2) / 3;\n        command.x2 = (command.x + x1 * 2) / 3;\n        command.y2 = (command.y + y1 * 2) / 3;\n      } else {\n        prevQuadX1 = NaN;\n        prevQuadY1 = NaN;\n      }\n\n      return command;\n    });\n  }\n  export function INFO(\n    f: (command: any, prevXAbs: number, prevYAbs: number,\n        pathStartXAbs: number, pathStartYAbs: number) => any | any[]) {\n    let prevXAbs = 0;\n    let prevYAbs = 0;\n    let pathStartXAbs = NaN;\n    let pathStartYAbs = NaN;\n\n    return function transform(command: any) {\n      if (isNaN(pathStartXAbs) && !(command.type & SVGPathData.MOVE_TO)) {\n        throw new Error(\"path must start with moveto\");\n      }\n\n      const result = f(command, prevXAbs, prevYAbs, pathStartXAbs, pathStartYAbs);\n\n      if (command.type & SVGPathData.CLOSE_PATH) {\n        prevXAbs = pathStartXAbs;\n        prevYAbs = pathStartYAbs;\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        prevXAbs = (command.relative ? prevXAbs + command.x : command.x);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        prevYAbs = (command.relative ? prevYAbs + command.y : command.y);\n      }\n\n      if (command.type & SVGPathData.MOVE_TO) {\n        pathStartXAbs = prevXAbs;\n        pathStartYAbs = prevYAbs;\n      }\n\n      return result;\n    };\n  }\n  /*\n   * remove 0-length segments\n   */\n  export function SANITIZE(EPS = 0) {\n    assertNumbers(EPS);\n    let prevCurveC2X = NaN;\n    let prevCurveC2Y = NaN;\n    let prevQuadCX = NaN;\n    let prevQuadCY = NaN;\n\n    return INFO((command, prevX, prevY, pathStartX, pathStartY) => {\n      const abs = Math.abs;\n      let skip = false;\n      let x1Rel = 0;\n      let y1Rel = 0;\n\n      if (command.type & SVGPathData.SMOOTH_CURVE_TO) {\n        x1Rel = isNaN(prevCurveC2X) ? 0 : prevX - prevCurveC2X;\n        y1Rel = isNaN(prevCurveC2Y) ? 0 : prevY - prevCurveC2Y;\n      }\n      if (command.type & (SVGPathData.CURVE_TO | SVGPathData.SMOOTH_CURVE_TO)) {\n        prevCurveC2X = command.relative ? prevX + command.x2 : command.x2;\n        prevCurveC2Y = command.relative ? prevY + command.y2 : command.y2;\n      } else {\n        prevCurveC2X = NaN;\n        prevCurveC2Y = NaN;\n      }\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        prevQuadCX = isNaN(prevQuadCX) ? prevX : 2 * prevX - prevQuadCX;\n        prevQuadCY = isNaN(prevQuadCY) ? prevY : 2 * prevY - prevQuadCY;\n      } else if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadCX = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadCY = command.relative ? prevY + command.y1 : command.y2;\n      } else {\n        prevQuadCX = NaN;\n        prevQuadCY = NaN;\n      }\n\n      if (command.type & SVGPathData.LINE_COMMANDS ||\n        command.type & SVGPathData.ARC && (0 === command.rX || 0 === command.rY || !command.lArcFlag) ||\n        command.type & SVGPathData.CURVE_TO || command.type & SVGPathData.SMOOTH_CURVE_TO ||\n        command.type & SVGPathData.QUAD_TO || command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        const xRel = \"undefined\" === typeof command.x ? 0 :\n          (command.relative ? command.x : command.x - prevX);\n        const yRel = \"undefined\" === typeof command.y ? 0 :\n          (command.relative ? command.y : command.y - prevY);\n\n        x1Rel = !isNaN(prevQuadCX) ? prevQuadCX - prevX :\n          \"undefined\" === typeof command.x1 ? x1Rel :\n            command.relative ? command.x :\n              command.x1 - prevX;\n        y1Rel = !isNaN(prevQuadCY) ? prevQuadCY - prevY :\n          \"undefined\" === typeof command.y1 ? y1Rel :\n            command.relative ? command.y :\n              command.y1 - prevY;\n\n        const x2Rel = \"undefined\" === typeof command.x2 ? 0 :\n          (command.relative ? command.x : command.x2 - prevX);\n        const y2Rel = \"undefined\" === typeof command.y2 ? 0 :\n          (command.relative ? command.y : command.y2 - prevY);\n\n        if (abs(xRel) <= EPS && abs(yRel) <= EPS &&\n          abs(x1Rel) <= EPS && abs(y1Rel) <= EPS &&\n          abs(x2Rel) <= EPS && abs(y2Rel) <= EPS) {\n          skip = true;\n        }\n      }\n\n      if (command.type & SVGPathData.CLOSE_PATH) {\n        if (abs(prevX - pathStartX) <= EPS && abs(prevY - pathStartY) <= EPS) {\n          skip = true;\n        }\n      }\n\n      return skip ? [] : command;\n    });\n  }\n  // SVG Transforms : http://www.w3.org/TR/SVGTiny12/coords.html#TransformList\n  // Matrix : http://apike.ca/prog_svg_transform.html\n  // a c e\n  // b d f\n  export function MATRIX(a: number, b: number, c: number, d: number, e: number, f: number) {\n    assertNumbers(a, b, c, d, e, f);\n\n    return INFO((command, prevX, prevY, pathStartX) => {\n      const origX1 = command.x1;\n      const origX2 = command.x2;\n      // if isNaN(pathStartX), then this is the first command, which is ALWAYS an\n      // absolute MOVE_TO, regardless what the relative flag says\n      const comRel = command.relative && !isNaN(pathStartX);\n      const x = \"undefined\" !== typeof command.x ? command.x : (comRel ? 0 : prevX);\n      const y = \"undefined\" !== typeof command.y ? command.y : (comRel ? 0 : prevY);\n\n      if (command.type & SVGPathData.HORIZ_LINE_TO && 0 !== b) {\n        command.type = SVGPathData.LINE_TO;\n        command.y = command.relative ? 0 : prevY;\n      }\n      if (command.type & SVGPathData.VERT_LINE_TO && 0 !== c) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? 0 : prevX;\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        command.x = (command.x * a) + (y * c) + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        command.y = (x * b) + command.y * d + (comRel ? 0 : f);\n      }\n      if (\"undefined\" !== typeof command.x1) {\n        command.x1 = command.x1 * a + command.y1 * c + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y1) {\n        command.y1 = origX1 * b + command.y1 * d + (comRel ? 0 : f);\n      }\n      if (\"undefined\" !== typeof command.x2) {\n        command.x2 = command.x2 * a + command.y2 * c + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y2) {\n        command.y2 = origX2 * b + command.y2 * d + (comRel ? 0 : f);\n      }\n      function sqr(x: number) { return x * x; }\n      const det = a * d - b * c;\n\n      if (\"undefined\" !== typeof command.xRot) {\n        // Skip if this is a pure translation\n        if (1 !== a || 0 !== b || 0 !== c || 1 !== d) {\n          // Special case for singular matrix\n          if (0 === det) {\n            // In the singular case, the arc is compressed to a line. The actual geometric image of the original\n            // curve under this transform possibly extends beyond the starting and/or ending points of the segment, but\n            // for simplicity we ignore this detail and just replace this command with a single line segment.\n            delete command.rX;\n            delete command.rY;\n            delete command.xRot;\n            delete command.lArcFlag;\n            delete command.sweepFlag;\n            command.type = SVGPathData.LINE_TO;\n          } else {\n            // Convert to radians\n            const xRot = command.xRot * Math.PI / 180;\n\n            // Convert rotated ellipse to general conic form\n            // x0^2/rX^2 + y0^2/rY^2 - 1 = 0\n            // x0 = x*cos(xRot) + y*sin(xRot)\n            // y0 = -x*sin(xRot) + y*cos(xRot)\n            // --> A*x^2 + B*x*y + C*y^2 - 1 = 0, where\n            const sinRot = Math.sin(xRot);\n            const cosRot = Math.cos(xRot);\n            const xCurve = 1 / sqr(command.rX);\n            const yCurve = 1 / sqr(command.rY);\n            const A = sqr(cosRot) * xCurve + sqr(sinRot) * yCurve;\n            const B = 2 * sinRot * cosRot * (xCurve - yCurve);\n            const C = sqr(sinRot) * xCurve + sqr(cosRot) * yCurve;\n\n            // Apply matrix to A*x^2 + B*x*y + C*y^2 - 1 = 0\n            // x1 = a*x + c*y\n            // y1 = b*x + d*y\n            //      (we can ignore e and f, since pure translations don\"t affect the shape of the ellipse)\n            // --> A1*x1^2 + B1*x1*y1 + C1*y1^2 - det^2 = 0, where\n            const A1 = A * d * d - B * b * d + C * b * b;\n            const B1 = B * (a * d + b * c) - 2 * (A * c * d + C * a * b);\n            const C1 = A * c * c - B * a * c + C * a * a;\n\n            // Unapply newXRot to get back to axis-aligned ellipse equation\n            // x1 = x2*cos(newXRot) - y2*sin(newXRot)\n            // y1 = x2*sin(newXRot) + y2*cos(newXRot)\n            // A1*x1^2 + B1*x1*y1 + C1*y1^2 - det^2 =\n            //   x2^2*(A1*cos(newXRot)^2 + B1*sin(newXRot)*cos(newXRot) + C1*sin(newXRot)^2)\n            //   + x2*y2*(2*(C1 - A1)*sin(newXRot)*cos(newXRot) + B1*(cos(newXRot)^2 - sin(newXRot)^2))\n            //   + y2^2*(A1*sin(newXRot)^2 - B1*sin(newXRot)*cos(newXRot) + C1*cos(newXRot)^2)\n            //   (which must have the same zeroes as)\n            // x2^2/newRX^2 + y2^2/newRY^2 - 1\n            //   (so we have)\n            // 2*(C1 - A1)*sin(newXRot)*cos(newXRot) + B1*(cos(newXRot)^2 - sin(newXRot)^2) = 0\n            // (A1 - C1)*sin(2*newXRot) = B1*cos(2*newXRot)\n            // 2*newXRot = atan2(B1, A1 - C1)\n            const newXRot = ((Math.atan2(B1, A1 - C1) + Math.PI) % Math.PI) / 2;\n            // For any integer n, (atan2(B1, A1 - C1) + n*pi)/2 is a solution to the above; incrementing n just swaps\n            // the x and y radii computed below (since that\"s what rotating an ellipse by pi/2 does).  Choosing the\n            // rotation between 0 and pi/2 eliminates the ambiguity and leads to more predictable output.\n\n            // Finally, we get newRX and newRY from the same-zeroes relationship that gave us newXRot\n            const newSinRot = Math.sin(newXRot);\n            const newCosRot = Math.cos(newXRot);\n\n            command.rX = Math.abs(det) /\n              Math.sqrt(A1 * sqr(newCosRot) + B1 * newSinRot * newCosRot + C1 * sqr(newSinRot));\n            command.rY = Math.abs(det) /\n              Math.sqrt(A1 * sqr(newSinRot) - B1 * newSinRot * newCosRot + C1 * sqr(newCosRot));\n            command.xRot = newXRot * 180 / Math.PI;\n          }\n        }\n      }\n      // sweepFlag needs to be inverted when mirroring shapes\n      // see http://www.itk.ilstu.edu/faculty/javila/SVG/SVG_drawing1/elliptical_curve.htm\n      // m 65,10 a 50,25 0 1 0 50,25\n      // M 65,60 A 50,25 0 1 1 115,35\n      if (\"undefined\" !== typeof command.sweepFlag && 0 > det) {\n        command.sweepFlag = +!command.sweepFlag;\n      }\n      return command;\n    });\n  }\n  export function ROTATE(a: number, x = 0, y = 0) {\n    assertNumbers(a, x, y);\n    const sin = Math.sin(a);\n    const cos = Math.cos(a);\n\n    return MATRIX(cos, sin, -sin, cos, x - x * cos + y * sin, y - x * sin - y * cos);\n  }\n  export function TRANSLATE(dX: number, dY = 0) {\n    assertNumbers(dX, dY);\n    return MATRIX(1, 0, 0, 1, dX, dY);\n  }\n  export function SCALE(dX: number, dY = dX) {\n    assertNumbers(dX, dY);\n    return MATRIX(dX, 0, 0, dY, 0, 0);\n  }\n  export function SKEW_X(a: number) {\n    assertNumbers(a);\n    return MATRIX(1, 0, Math.atan(a), 1, 0, 0);\n  }\n  export function SKEW_Y(a: number) {\n    assertNumbers(a);\n    return MATRIX(1, Math.atan(a), 0, 1, 0, 0);\n  }\n  export function X_AXIS_SYMMETRY(xOffset = 0) {\n    assertNumbers(xOffset);\n    return MATRIX(-1, 0, 0, 1, xOffset, 0);\n  }\n  export function Y_AXIS_SYMMETRY(yOffset = 0) {\n    assertNumbers(yOffset);\n    return MATRIX(1, 0, 0, -1, 0, yOffset);\n  }\n  // Convert arc commands to curve commands\n  export function A_TO_C() {\n    return INFO((command, prevX, prevY) => {\n      if (SVGPathData.ARC === command.type) {\n        return a2c(command, command.relative ? 0 : prevX, command.relative ? 0 : prevY);\n      }\n      return command;\n    });\n  }\n  // @see annotateArcCommand\n  export function ANNOTATE_ARCS() {\n    return INFO((c, x1, y1) => {\n      if (c.relative) {\n        x1 = 0;\n        y1 = 0;\n      }\n      if (SVGPathData.ARC === c.type) {\n        annotateArcCommand(c, x1, y1);\n      }\n      return c;\n    });\n  }\n  export function CLONE() {\n    return (c: SVGCommand) => {\n      const result = {} as SVGCommand;\n      // tslint:disable-next-line\n      for (const key in c) {\n        result[key as keyof SVGCommand] = c[key as keyof SVGCommand];\n      }\n      return result;\n    };\n  }\n  // @see annotateArcCommand\n  export function CALCULATE_BOUNDS() {\n    const clone = CLONE();\n    const toAbs = TO_ABS();\n    const qtToC = QT_TO_C();\n    const normST = NORMALIZE_ST();\n    const f: TransformFunction & {minX: number, maxX: number, minY: number, maxY: number} =\n        INFO((command, prevXAbs, prevYAbs) => {\n      const c = normST(qtToC(toAbs(clone(command))));\n      function fixX(absX: number) {\n        if (absX > f.maxX) { f.maxX = absX; }\n        if (absX < f.minX) { f.minX = absX; }\n      }\n      function fixY(absY: number) {\n        if (absY > f.maxY) { f.maxY = absY; }\n        if (absY < f.minY) { f.minY = absY; }\n      }\n      if (c.type & SVGPathData.DRAWING_COMMANDS) {\n        fixX(prevXAbs);\n        fixY(prevYAbs);\n      }\n      if (c.type & SVGPathData.HORIZ_LINE_TO) {\n        fixX(c.x);\n      }\n      if (c.type & SVGPathData.VERT_LINE_TO) {\n        fixY(c.y);\n      }\n      if (c.type & SVGPathData.LINE_TO) {\n        fixX(c.x);\n        fixY(c.y);\n      }\n      if (c.type & SVGPathData.CURVE_TO) {\n        // add start and end points\n        fixX(c.x);\n        fixY(c.y);\n        const xDerivRoots = bezierRoot(prevXAbs, c.x1, c.x2, c.x);\n\n        for (const derivRoot of xDerivRoots) {\n          if (0 < derivRoot && 1 > derivRoot) {\n            fixX(bezierAt(prevXAbs, c.x1, c.x2, c.x, derivRoot));\n          }\n        }\n        const yDerivRoots = bezierRoot(prevYAbs, c.y1, c.y2, c.y);\n\n        for (const derivRoot of yDerivRoots) {\n          if (0 < derivRoot && 1 > derivRoot) {\n            fixY(bezierAt(prevYAbs, c.y1, c.y2, c.y, derivRoot));\n          }\n        }\n      }\n      if (c.type & SVGPathData.ARC) {\n        // add start and end points\n        fixX(c.x);\n        fixY(c.y);\n        annotateArcCommand(c, prevXAbs, prevYAbs);\n        // p = cos(phi) * xv + sin(phi) * yv\n        // dp = -sin(phi) * xv + cos(phi) * yv = 0\n        const xRotRad = c.xRot / 180 * Math.PI;\n        // points on ellipse for phi = 0° and phi = 90°\n        const x0 = Math.cos(xRotRad) * c.rX;\n        const y0 = Math.sin(xRotRad) * c.rX;\n        const x90 = -Math.sin(xRotRad) * c.rY;\n        const y90 = Math.cos(xRotRad) * c.rY;\n\n        // annotateArcCommand returns phi1 and phi2 such that -180° < phi1 < 180° and phi2 is smaller or greater\n        // depending on the sweep flag. Calculate phiMin, phiMax such that -180° < phiMin < 180° and phiMin < phiMax\n        const [phiMin, phiMax] = c.phi1 < c.phi2 ?\n          [c.phi1, c.phi2] :\n          (-180 > c.phi2 ? [c.phi2 + 360, c.phi1 + 360] : [c.phi2, c.phi1]);\n        const normalizeXiEta = ([xi, eta]: [number, number]) => {\n          const phiRad = Math.atan2(eta, xi);\n          const phi = phiRad * 180 / Math.PI;\n\n          return phi < phiMin ? phi + 360 : phi;\n        };\n        // xi = cos(phi), eta = sin(phi)\n\n        const xDerivRoots = intersectionUnitCircleLine(x90, -x0, 0).map(normalizeXiEta);\n        for (const derivRoot of xDerivRoots) {\n          if (derivRoot > phiMin && derivRoot < phiMax) {\n            fixX(arcAt(c.cX, x0, x90, derivRoot));\n          }\n        }\n\n        const yDerivRoots = intersectionUnitCircleLine(y90, -y0, 0).map(normalizeXiEta);\n        for (const derivRoot of yDerivRoots) {\n          if (derivRoot > phiMin && derivRoot < phiMax) {\n            fixY(arcAt(c.cY, y0, y90, derivRoot));\n          }\n        }\n      }\n      return command;\n    }) as any;\n\n    f.minX = Infinity;\n    f.maxX = -Infinity;\n    f.minY = Infinity;\n    f.maxY = -Infinity;\n    return f;\n  }\n}\n", "import { SVGPathDataTransformer } from \"./SVGPathDataTransformer\";\nimport { TransformFunction } from \"./types\";\n\nexport abstract class TransformableSVG {\n  round(x?: number) {\n    return this.transform(SVGPathDataTransformer.ROUND(x));\n  }\n\n  toAbs() {\n    return this.transform(SVGPathDataTransformer.TO_ABS());\n  }\n\n  toRel() {\n    return this.transform(SVGPathDataTransformer.TO_REL());\n  }\n\n  normalizeHVZ(a?: boolean, b?: boolean, c?: boolean) {\n    return this.transform(SVGPathDataTransformer.NORMALIZE_HVZ(a, b, c));\n  }\n\n  normalizeST() {\n    return this.transform(SVGPathDataTransformer.NORMALIZE_ST());\n  }\n\n  qtToC() {\n    return this.transform(SVGPathDataTransformer.QT_TO_C());\n  }\n\n  aToC() {\n    return this.transform(SVGPathDataTransformer.A_TO_C());\n  }\n\n  sanitize(eps?: number) {\n    return this.transform(SVGPathDataTransformer.SANITIZE(eps));\n  }\n\n  translate(x: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.TRANSLATE(x, y));\n  }\n\n  scale(x: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.SCALE(x, y));\n  }\n\n  rotate(a: number, x?: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.ROTATE(a, x, y));\n  }\n\n  matrix(a: number, b: number, c: number, d: number, e: number, f: number) {\n    return this.transform(SVGPathDataTransformer.MATRIX(a, b, c, d, e, f));\n  }\n\n  skewX(a: number) {\n    return this.transform(SVGPathDataTransformer.SKEW_X(a));\n  }\n\n  skewY(a: number) {\n    return this.transform(SVGPathDataTransformer.SKEW_Y(a));\n  }\n\n  xSymmetry(xOffset?: number) {\n    return this.transform(SVGPathDataTransformer.X_AXIS_SYMMETRY(xOffset));\n  }\n\n  ySymmetry(yOffset?: number) {\n    return this.transform(SVGPathDataTransformer.Y_AXIS_SYMMETRY(yOffset));\n  }\n\n  annotateArcs() {\n    return this.transform(SVGPathDataTransformer.ANNOTATE_ARCS());\n  }\n\n  abstract transform(transformFunction: TransformFunction): this;\n}\n", "// Parse SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\nimport { COMMAND_ARG_COUNTS, SVGPathData } from \"./SVGPathData\";\nimport { TransformableSVG } from \"./TransformableSVG\";\nimport { SVGCommand, TransformFunction } from \"./types\";\n// Private consts : Char groups\nconst isWhiteSpace = (c: string) =>\n  \" \" === c || \"\\t\" === c || \"\\r\" === c || \"\\n\" === c;\nconst isDigit = (c: string) =>\n  \"0\".charCodeAt(0) <= c.charCodeAt(0) && c.charCodeAt(0) <= \"9\".charCodeAt(0);\nconst COMMANDS = \"mMzZlLhHvVcCsSqQtTaA\";\n\nexport class SVGPathDataParser extends TransformableSVG {\n  private curNumber: string = \"\";\n  private curCommandType: SVGCommand[\"type\"] | -1 = -1;\n  private curCommandRelative = false;\n  private canParseCommandOrComma = true;\n  private curNumberHasExp = false;\n  private curNumberHasExpDigits = false;\n  private curNumberHasDecimal = false;\n  private curArgs: number[] = [];\n\n  constructor() {\n    super();\n  }\n\n  finish(commands: SVGCommand[] = []) {\n    this.parse(\" \", commands);\n    // Adding residual command\n    if (0 !== this.curArgs.length || !this.canParseCommandOrComma) {\n      throw new SyntaxError(\"Unterminated command at the path end.\");\n    }\n    return commands;\n  }\n\n  parse(str: string, commands: SVGCommand[] = []) {\n    const finishCommand = (command: SVGCommand) => {\n      commands.push(command);\n      this.curArgs.length = 0;\n      this.canParseCommandOrComma = true;\n    };\n\n    for (let i = 0; i < str.length; i++) {\n      const c = str[i];\n      // White spaces parsing\n      const isAArcFlag = this.curCommandType === SVGPathData.ARC &&\n        (this.curArgs.length === 3 || this.curArgs.length === 4) &&\n        this.curNumber.length === 1 &&\n        (this.curNumber === \"0\" || this.curNumber === \"1\");\n      const isEndingDigit = isDigit(c) && (\n        (this.curNumber === \"0\" && c === \"0\") ||\n        isAArcFlag\n      );\n\n      if (\n        isDigit(c) &&\n        !isEndingDigit\n      ) {\n        this.curNumber += c;\n        this.curNumberHasExpDigits = this.curNumberHasExp;\n        continue;\n      }\n      if (\"e\" === c || \"E\" === c) {\n        this.curNumber += c;\n        this.curNumberHasExp = true;\n        continue;\n      }\n      if (\n        (\"-\" === c || \"+\" === c) &&\n        this.curNumberHasExp &&\n        !this.curNumberHasExpDigits\n      ) {\n        this.curNumber += c;\n        continue;\n      }\n      // if we already have a \".\", it means we are starting a new number\n      if (\".\" === c && !this.curNumberHasExp && !this.curNumberHasDecimal && !isAArcFlag) {\n        this.curNumber += c;\n        this.curNumberHasDecimal = true;\n        continue;\n      }\n\n      // New number\n      if (this.curNumber && -1 !== this.curCommandType) {\n        const val = Number(this.curNumber);\n        if (isNaN(val)) {\n          throw new SyntaxError(`Invalid number ending at ${i}`);\n        }\n        if (this.curCommandType === SVGPathData.ARC) {\n          if (0 === this.curArgs.length || 1 === this.curArgs.length) {\n            if (0 > val) {\n              throw new SyntaxError(\n                `Expected positive number, got \"${val}\" at index \"${i}\"`,\n              );\n            }\n          } else if (3 === this.curArgs.length || 4 === this.curArgs.length) {\n            if (\"0\" !== this.curNumber && \"1\" !== this.curNumber) {\n              throw new SyntaxError(\n                `Expected a flag, got \"${this.curNumber}\" at index \"${i}\"`,\n              );\n            }\n          }\n        }\n        this.curArgs.push(val);\n        if (this.curArgs.length === COMMAND_ARG_COUNTS[this.curCommandType]) {\n          if (SVGPathData.HORIZ_LINE_TO === this.curCommandType) {\n            finishCommand({\n              type: SVGPathData.HORIZ_LINE_TO,\n              relative: this.curCommandRelative,\n              x: val,\n            });\n          } else if (SVGPathData.VERT_LINE_TO === this.curCommandType) {\n            finishCommand({\n              type: SVGPathData.VERT_LINE_TO,\n              relative: this.curCommandRelative,\n              y: val,\n            });\n            // Move to / line to / smooth quadratic curve to commands (x, y)\n          } else if (\n            this.curCommandType === SVGPathData.MOVE_TO ||\n            this.curCommandType === SVGPathData.LINE_TO ||\n            this.curCommandType === SVGPathData.SMOOTH_QUAD_TO\n          ) {\n            finishCommand({\n              type: this.curCommandType,\n              relative: this.curCommandRelative,\n              x: this.curArgs[0],\n              y: this.curArgs[1],\n            } as SVGCommand);\n            // Switch to line to state\n            if (SVGPathData.MOVE_TO === this.curCommandType) {\n              this.curCommandType = SVGPathData.LINE_TO;\n            }\n          } else if (this.curCommandType === SVGPathData.CURVE_TO) {\n            finishCommand({\n              type: SVGPathData.CURVE_TO,\n              relative: this.curCommandRelative,\n              x1: this.curArgs[0],\n              y1: this.curArgs[1],\n              x2: this.curArgs[2],\n              y2: this.curArgs[3],\n              x: this.curArgs[4],\n              y: this.curArgs[5],\n            });\n          } else if (this.curCommandType === SVGPathData.SMOOTH_CURVE_TO) {\n            finishCommand({\n              type: SVGPathData.SMOOTH_CURVE_TO,\n              relative: this.curCommandRelative,\n              x2: this.curArgs[0],\n              y2: this.curArgs[1],\n              x: this.curArgs[2],\n              y: this.curArgs[3],\n            });\n          } else if (this.curCommandType === SVGPathData.QUAD_TO) {\n            finishCommand({\n              type: SVGPathData.QUAD_TO,\n              relative: this.curCommandRelative,\n              x1: this.curArgs[0],\n              y1: this.curArgs[1],\n              x: this.curArgs[2],\n              y: this.curArgs[3],\n            });\n          } else if (this.curCommandType === SVGPathData.ARC) {\n            finishCommand({\n              type: SVGPathData.ARC,\n              relative: this.curCommandRelative,\n              rX: this.curArgs[0],\n              rY: this.curArgs[1],\n              xRot: this.curArgs[2],\n              lArcFlag: this.curArgs[3] as 0 | 1,\n              sweepFlag: this.curArgs[4] as 0 | 1,\n              x: this.curArgs[5],\n              y: this.curArgs[6],\n            });\n          }\n        }\n        this.curNumber = \"\";\n        this.curNumberHasExpDigits = false;\n        this.curNumberHasExp = false;\n        this.curNumberHasDecimal = false;\n        this.canParseCommandOrComma = true;\n      }\n      // Continue if a white space or a comma was detected\n      if (isWhiteSpace(c)) {\n        continue;\n      }\n      if (\",\" === c && this.canParseCommandOrComma) {\n        // L 0,0, H is not valid:\n        this.canParseCommandOrComma = false;\n        continue;\n      }\n      // if a sign is detected, then parse the new number\n      if (\"+\" === c || \"-\" === c || \".\" === c) {\n        this.curNumber = c;\n        this.curNumberHasDecimal = \".\" === c;\n        continue;\n      }\n      // if a 0 is detected, then parse the new number\n      if (isEndingDigit) {\n        this.curNumber = c;\n        this.curNumberHasDecimal = false;\n        continue;\n      }\n\n      // Adding residual command\n      if (0 !== this.curArgs.length) {\n        throw new SyntaxError(`Unterminated command at index ${i}.`);\n      }\n      if (!this.canParseCommandOrComma) {\n        throw new SyntaxError(\n          `Unexpected character \"${c}\" at index ${i}. Command cannot follow comma`,\n        );\n      }\n      this.canParseCommandOrComma = false;\n      // Detecting the next command\n      if (\"z\" === c || \"Z\" === c) {\n        commands.push({\n          type: SVGPathData.CLOSE_PATH,\n        });\n        this.canParseCommandOrComma = true;\n        this.curCommandType = -1;\n        continue;\n        // Horizontal move to command\n      } else if (\"h\" === c || \"H\" === c) {\n        this.curCommandType = SVGPathData.HORIZ_LINE_TO;\n        this.curCommandRelative = \"h\" === c;\n        // Vertical move to command\n      } else if (\"v\" === c || \"V\" === c) {\n        this.curCommandType = SVGPathData.VERT_LINE_TO;\n        this.curCommandRelative = \"v\" === c;\n        // Move to command\n      } else if (\"m\" === c || \"M\" === c) {\n        this.curCommandType = SVGPathData.MOVE_TO;\n        this.curCommandRelative = \"m\" === c;\n        // Line to command\n      } else if (\"l\" === c || \"L\" === c) {\n        this.curCommandType = SVGPathData.LINE_TO;\n        this.curCommandRelative = \"l\" === c;\n        // Curve to command\n      } else if (\"c\" === c || \"C\" === c) {\n        this.curCommandType = SVGPathData.CURVE_TO;\n        this.curCommandRelative = \"c\" === c;\n        // Smooth curve to command\n      } else if (\"s\" === c || \"S\" === c) {\n        this.curCommandType = SVGPathData.SMOOTH_CURVE_TO;\n        this.curCommandRelative = \"s\" === c;\n        // Quadratic bezier curve to command\n      } else if (\"q\" === c || \"Q\" === c) {\n        this.curCommandType = SVGPathData.QUAD_TO;\n        this.curCommandRelative = \"q\" === c;\n        // Smooth quadratic bezier curve to command\n      } else if (\"t\" === c || \"T\" === c) {\n        this.curCommandType = SVGPathData.SMOOTH_QUAD_TO;\n        this.curCommandRelative = \"t\" === c;\n        // Elliptic arc command\n      } else if (\"a\" === c || \"A\" === c) {\n        this.curCommandType = SVGPathData.ARC;\n        this.curCommandRelative = \"a\" === c;\n      } else {\n        throw new SyntaxError(`Unexpected character \"${c}\" at index ${i}.`);\n      }\n    }\n    return commands;\n  }\n  /**\n   * Return a wrapper around this parser which applies the transformation on parsed commands.\n   */\n  transform(transform: TransformFunction) {\n    const result = Object.create(this, {\n      parse: {\n        value(chunk: string, commands: SVGCommand[] = []) {\n          const parsedCommands = Object.getPrototypeOf(this).parse.call(\n            this,\n            chunk,\n          );\n          for (const c of parsedCommands) {\n            const cT = transform(c);\n            if (Array.isArray(cT)) {\n              commands.push(...cT);\n            } else {\n              commands.push(cT);\n            }\n          }\n          return commands;\n        },\n      },\n    });\n    return result as this;\n  }\n}\n", "import { encodeSV<PERSON>ath } from \"./SVGPathDataEncoder\";\nimport { SVGPathDataParser } from \"./SVGPathDataParser\";\nimport { SVGPathDataTransformer } from \"./SVGPathDataTransformer\";\nimport { TransformableSVG } from \"./TransformableSVG\";\nimport { SVGCommand } from \"./types\";\n\nexport class SVGPathData extends TransformableSVG {\n  commands: SVGCommand[];\n  constructor(content: string | SVGCommand[]) {\n    super();\n    if (\"string\" === typeof content) {\n      this.commands = SVGPathData.parse(content);\n    } else {\n      this.commands = content;\n    }\n  }\n\n  encode() {\n    return SVGPathData.encode(this.commands);\n  }\n\n  getBounds() {\n    const boundsTransform = SVGPathDataTransformer.CALCULATE_BOUNDS();\n\n    this.transform(boundsTransform);\n    return boundsTransform;\n  }\n\n  transform(\n    transformFunction: (input: SVGCommand) => SVGCommand | SVGCommand[],\n  ) {\n    const newCommands = [];\n\n    for (const command of this.commands) {\n      const transformedCommand = transformFunction(command);\n\n      if (Array.isArray(transformedCommand)) {\n        newCommands.push(...transformedCommand);\n      } else {\n        newCommands.push(transformedCommand);\n      }\n    }\n    this.commands = newCommands;\n    return this;\n  }\n\n  static encode(commands: SVGCommand[]) {\n    return encodeSVGPath(commands);\n      }\n\n  static parse(path: string) {\n    const parser = new SVGPathDataParser();\n    const commands: SVGCommand[] = [];\n    parser.parse(path, commands);\n    parser.finish(commands);\n    return commands;\n  }\n\n  static readonly CLOSE_PATH: 1 = 1;\n  static readonly MOVE_TO: 2 = 2;\n  static readonly HORIZ_LINE_TO: 4 = 4;\n  static readonly VERT_LINE_TO: 8 = 8;\n  static readonly LINE_TO: 16 = 16;\n  static readonly CURVE_TO: 32 = 32;\n  static readonly SMOOTH_CURVE_TO: 64 = 64;\n  static readonly QUAD_TO: 128 = 128;\n  static readonly SMOOTH_QUAD_TO: 256 = 256;\n  static readonly ARC: 512 = 512;\n  static readonly LINE_COMMANDS = SVGPathData.LINE_TO | SVGPathData.HORIZ_LINE_TO | SVGPathData.VERT_LINE_TO;\n  static readonly DRAWING_COMMANDS = SVGPathData.HORIZ_LINE_TO | SVGPathData.VERT_LINE_TO | SVGPathData.LINE_TO |\n  SVGPathData.CURVE_TO | SVGPathData.SMOOTH_CURVE_TO | SVGPathData.QUAD_TO |\n  SVGPathData.SMOOTH_QUAD_TO | SVGPathData.ARC;\n}\n\nexport const COMMAND_ARG_COUNTS = {\n    [SVGPathData.MOVE_TO]: 2,\n    [SVGPathData.LINE_TO]: 2,\n    [SVGPathData.HORIZ_LINE_TO]: 1,\n    [SVGPathData.VERT_LINE_TO]: 1,\n    [SVGPathData.CLOSE_PATH]: 0,\n    [SVGPathData.QUAD_TO]: 4,\n    [SVGPathData.SMOOTH_QUAD_TO]: 2,\n    [SVGPathData.CURVE_TO]: 6,\n    [SVGPathData.SMOOTH_CURVE_TO]: 4,\n    [SVGPathData.ARC]: 7,\n};\n\nexport {encodeSVGPath} from \"./SVGPathDataEncoder\";\nexport {SVGPathDataParser} from \"./SVGPathDataParser\";\nexport {SVGPathDataTransformer} from \"./SVGPathDataTransformer\";\n"], "mappings": ";;;;;;;;;;;;;;AAgBA,IAAIA,CAAA,GAAgB,SAAAC,CAASC,CAAA,EAAGC,CAAA;EAI5B,QAHAH,CAAA,GAAgBI,MAAA,CAAOC,cAAA,IAClB;IAAEC,SAAA,EAAW;EAAA,aAAgBC,KAAA,IAAS,UAAUP,CAAA,EAAGE,CAAA;IAAKF,CAAA,CAAEM,SAAA,GAAYJ,CAAA;EAAA,KACvE,UAAUF,CAAA,EAAGE,CAAA;IAAK,KAAK,IAAIC,CAAA,IAAKD,CAAA,EAAOE,MAAA,CAAOI,SAAA,CAAUC,cAAA,CAAeC,IAAA,CAAKR,CAAA,EAAGC,CAAA,MAAIH,CAAA,CAAEG,CAAA,IAAKD,CAAA,CAAEC,CAAA;EAAA,GAC3ED,CAAA,EAAGC,CAAA;AAAA;AAGrB,SAASD,EAAUA,CAAA,EAAGC,CAAA;EACzB,IAAiB,qBAANA,CAAA,IAA0B,SAANA,CAAA,EAC3B,MAAM,IAAIQ,SAAA,CAAU,yBAAyBC,MAAA,CAAOT,CAAA,IAAK;EAE7D,SAASU,EAAA;IAAO,KAAKC,WAAA,GAAcZ,CAAA;EAAA;EADnCF,CAAA,CAAcE,CAAA,EAAGC,CAAA,GAEjBD,CAAA,CAAEM,SAAA,GAAkB,SAANL,CAAA,GAAaC,MAAA,CAAOW,MAAA,CAAOZ,CAAA,KAAMU,CAAA,CAAGL,SAAA,GAAYL,CAAA,CAAEK,SAAA,EAAW,IAAIK,CAAA;AAAA;AAAA,SCnBnEV,EAAcH,CAAA;EAC5B,IAAIE,CAAA,GAAM;EAELK,KAAA,CAAMS,OAAA,CAAQhB,CAAA,MACjBA,CAAA,GAAW,CAACA,CAAA;EAEd,KAAK,IAAIG,CAAA,GAAI,GAAGA,CAAA,GAAIH,CAAA,CAASiB,MAAA,EAAQd,CAAA,IAAK;IACxC,IAAMU,CAAA,GAAUb,CAAA,CAASG,CAAA;IACzB,IAAIU,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYC,UAAA,EAC/BlB,CAAA,IAAO,SACF,IAAIW,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYE,aAAA,EACtCnB,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQU,CAAA,MACL,IAAIV,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYK,YAAA,EACtCtB,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQY,CAAA,MACL,IAAIZ,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYO,OAAA,EACtCxB,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQU,CAAA,GApBJ,MAoBcV,CAAA,CAAQY,CAAA,MACvB,IAAIZ,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYQ,OAAA,EACtCzB,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQU,CAAA,GAvBJ,MAuBcV,CAAA,CAAQY,CAAA,MACvB,IAAIZ,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYS,QAAA,EACtC1B,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQgB,EAAA,GA1BJ,MA0BehB,CAAA,CAAQiB,EAAA,GA1BvB,MA2BEjB,CAAA,CAAQkB,EAAA,GA3BV,MA2BqBlB,CAAA,CAAQmB,EAAA,GA3B7B,MA4BEnB,CAAA,CAAQU,CAAA,GA5BV,MA4BoBV,CAAA,CAAQY,CAAA,MAC7B,IAAIZ,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYc,eAAA,EACtC/B,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQkB,EAAA,GA/BJ,MA+BelB,CAAA,CAAQmB,EAAA,GA/BvB,MAgCEnB,CAAA,CAAQU,CAAA,GAhCV,MAgCoBV,CAAA,CAAQY,CAAA,MAC7B,IAAIZ,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYe,OAAA,EACtChC,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQgB,EAAA,GAnCJ,MAmCehB,CAAA,CAAQiB,EAAA,GAnCvB,MAoCEjB,CAAA,CAAQU,CAAA,GApCV,MAoCoBV,CAAA,CAAQY,CAAA,MAC7B,IAAIZ,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYgB,cAAA,EACtCjC,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQU,CAAA,GAvCJ,MAuCcV,CAAA,CAAQY,CAAA,MACvB;MAAA,IAAIZ,CAAA,CAAQK,IAAA,KAASC,CAAA,CAAYiB,GAAA,EAQtC,MAAM,IAAIC,KAAA,CACR,8BAA8BxB,CAAA,CAAgBK,IAAA,mBAAkBf,CAAA;MARlED,CAAA,KAAQW,CAAA,CAAQS,QAAA,GAAW,MAAM,OAC/BT,CAAA,CAAQyB,EAAA,GA1CJ,MA0CezB,CAAA,CAAQ0B,EAAA,GA1CvB,MA2CE1B,CAAA,CAAQ2B,IAAA,GA3CV,OA4CI3B,CAAA,CAAQ4B,QAAA,GA5CZ,OA4CgC5B,CAAA,CAAQ6B,SAAA,GA5CxC,MA6CE7B,CAAA,CAAQU,CAAA,GA7CV,MA6CoBV,CAAA,CAAQY,CAAA;IAAA;EAAA;EAQtC,OAAOvB,CAAA;AAAA;AAAA,SCzDOW,EAAOb,CAAA,EAA0BE,CAAA;EAAA,IAAzBC,CAAA,GAAAH,CAAA;IAAGa,CAAA,GAAAb,CAAA;EACzB,OAAO,CACLG,CAAA,GAAIwC,IAAA,CAAKC,GAAA,CAAI1C,CAAA,IAAOW,CAAA,GAAI8B,IAAA,CAAKE,GAAA,CAAI3C,CAAA,GACjCC,CAAA,GAAIwC,IAAA,CAAKE,GAAA,CAAI3C,CAAA,IAAOW,CAAA,GAAI8B,IAAA,CAAKC,GAAA,CAAI1C,CAAA;AAAA;AAAA,SAKrB4C,EAAA;EAAA,KAAc,IAAA9C,CAAA,OAAAE,CAAA,MAAAA,CAAA,GAAA6C,SAAA,CAAA9B,MAAA,EAAAf,CAAA,IAAAF,CAAA,CAAAE,CAAA,IAAA6C,SAAA,CAAA7C,CAAA;EAE1B,KAAK,IAAIC,CAAA,GAAI,GAAGA,CAAA,GAAIH,CAAA,CAAQiB,MAAA,EAAQd,CAAA,IAClC,IAAI,mBAAoBH,CAAA,CAAQG,CAAA,GAC9B,MAAM,IAAIkC,KAAA,CACR,6BAA2BlC,CAAA,kCAA8BH,CAAA,CAAQG,CAAA,oBAAgBH,CAAA,CAAQG,CAAA;EAIjG,QAAO;AAAA;AAGT,IAAM6C,CAAA,GAAKL,IAAA,CAAKM,EAAA;AAAA,SASAC,EAAmBlD,CAAA,EAAaE,CAAA,EAAYC,CAAA;EAC1DH,CAAA,CAAEyC,QAAA,GAAY,MAAMzC,CAAA,CAAEyC,QAAA,GAAY,IAAI,GACtCzC,CAAA,CAAE0C,SAAA,GAAa,MAAM1C,CAAA,CAAE0C,SAAA,GAAa,IAAI;EAEnC,IAAAI,CAAA,GAAgB9C,CAAA,CAAAsC,EAAA;IAAZY,CAAA,GAAYlD,CAAA,CAAAuC,EAAA;IAARY,CAAA,GAAQnD,CAAA,CAAAuB,CAAA;IAAL6B,CAAA,GAAKpD,CAAA,CAAAyB,CAAA;EAErBqB,CAAA,GAAKH,IAAA,CAAKU,GAAA,CAAIrD,CAAA,CAAEsC,EAAA,GAChBY,CAAA,GAAKP,IAAA,CAAKU,GAAA,CAAIrD,CAAA,CAAEuC,EAAA;EACV,IAAAe,CAAA,GAAazC,CAAA,CAAO,EAAEX,CAAA,GAAKiD,CAAA,IAAK,IAAIhD,CAAA,GAAKiD,CAAA,IAAK,KAAKpD,CAAA,CAAEwC,IAAA,GAAO,MAAMQ,CAAA;IAAjEO,CAAA,GAAAD,CAAA;IAAK7B,CAAA,GAAA6B,CAAA;IACNE,CAAA,GAAYb,IAAA,CAAKc,GAAA,CAAIF,CAAA,EAAK,KAAKZ,IAAA,CAAKc,GAAA,CAAIX,CAAA,EAAI,KAAKH,IAAA,CAAKc,GAAA,CAAIhC,CAAA,EAAK,KAAKkB,IAAA,CAAKc,GAAA,CAAIP,CAAA,EAAI;EAEnF,IAAIM,CAAA,KACNV,CAAA,IAAMH,IAAA,CAAKe,IAAA,CAAKF,CAAA,GAChBN,CAAA,IAAMP,IAAA,CAAKe,IAAA,CAAKF,CAAA,IAElBxD,CAAA,CAAEsC,EAAA,GAAKQ,CAAA,EACP9C,CAAA,CAAEuC,EAAA,GAAKW,CAAA;EACP,IAAMS,CAAA,GAAehB,IAAA,CAAKc,GAAA,CAAIX,CAAA,EAAI,KAAKH,IAAA,CAAKc,GAAA,CAAIhC,CAAA,EAAK,KAAKkB,IAAA,CAAKc,GAAA,CAAIP,CAAA,EAAI,KAAKP,IAAA,CAAKc,GAAA,CAAIF,CAAA,EAAK;IACpFK,CAAA,IAAW5D,CAAA,CAAEyC,QAAA,KAAazC,CAAA,CAAE0C,SAAA,GAAY,KAAK,KACjDC,IAAA,CAAKe,IAAA,CAAKf,IAAA,CAAKkB,GAAA,CAAI,IAAIlB,IAAA,CAAKc,GAAA,CAAIX,CAAA,EAAI,KAAKH,IAAA,CAAKc,GAAA,CAAIP,CAAA,EAAI,KAAKS,CAAA,IAAeA,CAAA;IACtEG,CAAA,GAAMhB,CAAA,GAAKrB,CAAA,GAAMyB,CAAA,GAAKU,CAAA;IACtBG,CAAA,IAAOb,CAAA,GAAKK,CAAA,GAAMT,CAAA,GAAKc,CAAA;IACvBI,CAAA,GAAOnD,CAAA,CAAO,CAACiD,CAAA,EAAKC,CAAA,GAAM/D,CAAA,CAAEwC,IAAA,GAAO,MAAMQ,CAAA;EAE/ChD,CAAA,CAAEiE,EAAA,GAAKD,CAAA,CAAK,MAAM9D,CAAA,GAAKiD,CAAA,IAAK,GAC5BnD,CAAA,CAAEkE,EAAA,GAAKF,CAAA,CAAK,MAAM7D,CAAA,GAAKiD,CAAA,IAAK,GAC5BpD,CAAA,CAAEmE,IAAA,GAAOxB,IAAA,CAAKyB,KAAA,EAAO3C,CAAA,GAAMsC,CAAA,IAAOb,CAAA,GAAKK,CAAA,GAAMO,CAAA,IAAOhB,CAAA,GACpD9C,CAAA,CAAEqE,IAAA,GAAO1B,IAAA,CAAKyB,KAAA,GAAQ3C,CAAA,GAAMsC,CAAA,IAAOb,CAAA,IAAMK,CAAA,GAAMO,CAAA,IAAOhB,CAAA,GAClD,MAAM9C,CAAA,CAAE0C,SAAA,IAAa1C,CAAA,CAAEqE,IAAA,GAAOrE,CAAA,CAAEmE,IAAA,KAClCnE,CAAA,CAAEqE,IAAA,IAAQ,IAAIrB,CAAA,GAEZ,MAAMhD,CAAA,CAAE0C,SAAA,IAAa1C,CAAA,CAAEqE,IAAA,GAAOrE,CAAA,CAAEmE,IAAA,KAClCnE,CAAA,CAAEqE,IAAA,IAAQ,IAAIrB,CAAA,GAEhBhD,CAAA,CAAEmE,IAAA,IAAQ,MAAMnB,CAAA,EAChBhD,CAAA,CAAEqE,IAAA,IAAQ,MAAMrB,CAAA;AAAA;AAAA,SAaFG,EAA2BnD,CAAA,EAAWE,CAAA,EAAWC,CAAA;EAC/D2C,CAAA,CAAc9C,CAAA,EAAGE,CAAA,EAAGC,CAAA;EAEpB,IAAMU,CAAA,GAAUb,CAAA,GAAIA,CAAA,GAAIE,CAAA,GAAIA,CAAA,GAAIC,CAAA,GAAIA,CAAA;EAEpC,IAAI,IAAIU,CAAA,EACN,OAAO;EACF,IAAI,MAAMA,CAAA,EACf,OAAO,CACL,CACGb,CAAA,GAAIG,CAAA,IAAMH,CAAA,GAAIA,CAAA,GAAIE,CAAA,GAAIA,CAAA,GACtBA,CAAA,GAAIC,CAAA,IAAMH,CAAA,GAAIA,CAAA,GAAIE,CAAA,GAAIA,CAAA;EAE7B,IAAM8C,CAAA,GAAOL,IAAA,CAAKe,IAAA,CAAK7C,CAAA;EAEvB,OAAO,CACL,EACGb,CAAA,GAAIG,CAAA,GAAID,CAAA,GAAI8C,CAAA,KAAShD,CAAA,GAAIA,CAAA,GAAIE,CAAA,GAAIA,CAAA,IACjCA,CAAA,GAAIC,CAAA,GAAIH,CAAA,GAAIgD,CAAA,KAAShD,CAAA,GAAIA,CAAA,GAAIE,CAAA,GAAIA,CAAA,IACpC,EACGF,CAAA,GAAIG,CAAA,GAAID,CAAA,GAAI8C,CAAA,KAAShD,CAAA,GAAIA,CAAA,GAAIE,CAAA,GAAIA,CAAA,IACjCA,CAAA,GAAIC,CAAA,GAAIH,CAAA,GAAIgD,CAAA,KAAShD,CAAA,GAAIA,CAAA,GAAIE,CAAA,GAAIA,CAAA;AAAA;AAIjC,ICjGUkD,CAAA;EDiGJE,CAAA,GAAMX,IAAA,CAAKM,EAAA,GAAK;AAAA,SAEbM,EAAKvD,CAAA,EAAWE,CAAA,EAAWC,CAAA;EACzC,QAAQ,IAAIA,CAAA,IAAKH,CAAA,GAAIG,CAAA,GAAID,CAAA;AAAA;AAAA,SAGXuB,EAAMzB,CAAA,EAAWE,CAAA,EAAYC,CAAA,EAAYU,CAAA;EACvD,OAAOb,CAAA,GAAI2C,IAAA,CAAKC,GAAA,CAAI/B,CAAA,GAAS,MAAMmC,CAAA,IAAM9C,CAAA,GAAKyC,IAAA,CAAKE,GAAA,CAAIhC,CAAA,GAAS,MAAMmC,CAAA,IAAM7C,CAAA;AAAA;AAAA,SAG9DqD,EAAWxD,CAAA,EAAYE,CAAA,EAAYC,CAAA,EAAYU,CAAA;EAC7D,IAAMiC,CAAA,GAAM;IACNE,CAAA,GAAM9C,CAAA,GAAKF,CAAA;IACXkD,CAAA,GAAM/C,CAAA,GAAKD,CAAA;IAEXiD,CAAA,GAAI,IAAIH,CAAA,GAAM,KADRnC,CAAA,GAAKV,CAAA,IACa,IAAI+C,CAAA;IAC5BE,CAAA,GAAkB,KAAbF,CAAA,GAAMF,CAAA;IACXM,CAAA,GAAI,IAAIN,CAAA;EAGd,OAAIL,IAAA,CAAKU,GAAA,CAAIF,CAAA,IAAKL,CAAA,GAET,EAAEQ,CAAA,GAAIF,CAAA,IAiBjB,UAAmBpD,CAAA,EAAWE,CAAA,EAAWC,CAAA;IAAA,WAAAA,CAAA,KAAAA,CAAA;IAEvC,IAAMU,CAAA,GAAiBb,CAAA,GAAIA,CAAA,GAAI,IAAIE,CAAA;IAEnC,IAAIW,CAAA,IAAkBV,CAAA,EACpB,OAAO;IACF,IAAIU,CAAA,IAAkBV,CAAA,EAC3B,OAAO,EAAEH,CAAA,GAAI;IAEf,IAAM8C,CAAA,GAAOH,IAAA,CAAKe,IAAA,CAAK7C,CAAA;IAEvB,OAAO,EAAGb,CAAA,GAAI,IAAK8C,CAAA,GAAQ9C,CAAA,GAAI,IAAK8C,CAAA;EAAA,CA1B7B,CAAUM,CAAA,GAAID,CAAA,EAAGG,CAAA,GAAIH,CAAA,EAAGL,CAAA;AAAA;AAAA,SAIjBa,EAAS3D,CAAA,EAAYE,CAAA,EAAYC,CAAA,EAAYU,CAAA,EAAYiC,CAAA;EAEvE,IAAME,CAAA,GAAI,IAAIF,CAAA;EAMd,OAAO9C,CAAA,IALIgD,CAAA,GAAIA,CAAA,GAAIA,CAAA,IAKF9C,CAAA,IAJN,IAAI8C,CAAA,GAAIA,CAAA,GAAIF,CAAA,IAII3C,CAAA,IAHhB,IAAI6C,CAAA,GAAIF,CAAA,GAAIA,CAAA,IAGcjC,CAAA,IAF1BiC,CAAA,GAAIA,CAAA,GAAIA,CAAA;AAAA;AAAA,CCnIrB,UAAiB9C,CAAA;EAuCf,SAAgBE,EAAA;IACd,OAAOkD,CAAA,CAAK,UAACpD,CAAA,EAASE,CAAA,EAAOC,CAAA;MAyB3B,OAxBIH,CAAA,CAAQsB,QAAA,UAEN,MAAuBtB,CAAA,CAAQ6B,EAAA,KACjC7B,CAAA,CAAQ6B,EAAA,IAAM3B,CAAA,QAEZ,MAAuBF,CAAA,CAAQ8B,EAAA,KACjC9B,CAAA,CAAQ8B,EAAA,IAAM3B,CAAA,QAGZ,MAAuBH,CAAA,CAAQ+B,EAAA,KACjC/B,CAAA,CAAQ+B,EAAA,IAAM7B,CAAA,QAEZ,MAAuBF,CAAA,CAAQgC,EAAA,KACjChC,CAAA,CAAQgC,EAAA,IAAM7B,CAAA,QAGZ,MAAuBH,CAAA,CAAQuB,CAAA,KACjCvB,CAAA,CAAQuB,CAAA,IAAKrB,CAAA,QAEX,MAAuBF,CAAA,CAAQyB,CAAA,KACjCzB,CAAA,CAAQyB,CAAA,IAAKtB,CAAA,GAEfH,CAAA,CAAQsB,QAAA,IAAW,IAEdtB,CAAA;IAAA;EAAA;EAkEX,SAAgBG,EAAA;IACd,IAAIH,CAAA,GAAesE,GAAA;MACfpE,CAAA,GAAeoE,GAAA;MACfnE,CAAA,GAAamE,GAAA;MACbzD,CAAA,GAAayD,GAAA;IAEjB,OAAOlB,CAAA,CAAK,UAACN,CAAA,EAASE,CAAA,EAAOE,CAAA;MA8B3B,OA7BIJ,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYc,eAAA,KAC7Ba,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYS,QAAA,EAC3B5B,CAAA,GAAeuE,KAAA,CAAMvE,CAAA,IAAgBgD,CAAA,GAAQhD,CAAA,EAC7CE,CAAA,GAAeqE,KAAA,CAAMrE,CAAA,IAAgBgD,CAAA,GAAQhD,CAAA,EAC7C4C,CAAA,CAAQjB,EAAA,GAAKiB,CAAA,CAAQxB,QAAA,GAAW0B,CAAA,GAAQhD,CAAA,GAAe,IAAIgD,CAAA,GAAQhD,CAAA,EACnE8C,CAAA,CAAQhB,EAAA,GAAKgB,CAAA,CAAQxB,QAAA,GAAW4B,CAAA,GAAQhD,CAAA,GAAe,IAAIgD,CAAA,GAAQhD,CAAA,GAEjE4C,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYS,QAAA,IAC7B5B,CAAA,GAAe8C,CAAA,CAAQxB,QAAA,GAAW0B,CAAA,GAAQF,CAAA,CAAQf,EAAA,GAAKe,CAAA,CAAQf,EAAA,EAC/D7B,CAAA,GAAe4C,CAAA,CAAQxB,QAAA,GAAW4B,CAAA,GAAQJ,CAAA,CAAQd,EAAA,GAAKc,CAAA,CAAQd,EAAA,KAE/DhC,CAAA,GAAesE,GAAA,EACfpE,CAAA,GAAeoE,GAAA,GAEbxB,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYgB,cAAA,KAC7BW,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYe,OAAA,EAC3B/B,CAAA,GAAaoE,KAAA,CAAMpE,CAAA,IAAc6C,CAAA,GAAQ7C,CAAA,EACzCU,CAAA,GAAa0D,KAAA,CAAM1D,CAAA,IAAcqC,CAAA,GAAQrC,CAAA,EACzCiC,CAAA,CAAQjB,EAAA,GAAKiB,CAAA,CAAQxB,QAAA,GAAW0B,CAAA,GAAQ7C,CAAA,GAAa,IAAI6C,CAAA,GAAQ7C,CAAA,EACjE2C,CAAA,CAAQhB,EAAA,GAAKgB,CAAA,CAAQxB,QAAA,GAAW4B,CAAA,GAAQrC,CAAA,GAAa,IAAIqC,CAAA,GAAQrC,CAAA,GAE/DiC,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYe,OAAA,IAC7B/B,CAAA,GAAa2C,CAAA,CAAQxB,QAAA,GAAW0B,CAAA,GAAQF,CAAA,CAAQjB,EAAA,GAAKiB,CAAA,CAAQjB,EAAA,EAC7DhB,CAAA,GAAaiC,CAAA,CAAQxB,QAAA,GAAW4B,CAAA,GAAQJ,CAAA,CAAQhB,EAAA,GAAKgB,CAAA,CAAQhB,EAAA,KAE7D3B,CAAA,GAAamE,GAAA,EACbzD,CAAA,GAAayD,GAAA,GAGRxB,CAAA;IAAA;EAAA;EAYX,SAAgBE,EAAA;IACd,IAAIhD,CAAA,GAAasE,GAAA;MACbpE,CAAA,GAAaoE,GAAA;IAEjB,OAAOlB,CAAA,CAAK,UAACjD,CAAA,EAASU,CAAA,EAAOiC,CAAA;MAQ3B,IAPI3C,CAAA,CAAQe,IAAA,GAAOC,CAAA,CAAYgB,cAAA,KAC7BhC,CAAA,CAAQe,IAAA,GAAOC,CAAA,CAAYe,OAAA,EAC3BlC,CAAA,GAAauE,KAAA,CAAMvE,CAAA,IAAca,CAAA,GAAQb,CAAA,EACzCE,CAAA,GAAaqE,KAAA,CAAMrE,CAAA,IAAc4C,CAAA,GAAQ5C,CAAA,EACzCC,CAAA,CAAQ0B,EAAA,GAAK1B,CAAA,CAAQmB,QAAA,GAAWT,CAAA,GAAQb,CAAA,GAAa,IAAIa,CAAA,GAAQb,CAAA,EACjEG,CAAA,CAAQ2B,EAAA,GAAK3B,CAAA,CAAQmB,QAAA,GAAWwB,CAAA,GAAQ5C,CAAA,GAAa,IAAI4C,CAAA,GAAQ5C,CAAA,GAE/DC,CAAA,CAAQe,IAAA,GAAOC,CAAA,CAAYe,OAAA,EAAS;QACtClC,CAAA,GAAaG,CAAA,CAAQmB,QAAA,GAAWT,CAAA,GAAQV,CAAA,CAAQ0B,EAAA,GAAK1B,CAAA,CAAQ0B,EAAA,EAC7D3B,CAAA,GAAaC,CAAA,CAAQmB,QAAA,GAAWwB,CAAA,GAAQ3C,CAAA,CAAQ2B,EAAA,GAAK3B,CAAA,CAAQ2B,EAAA;QAC7D,IAAMkB,CAAA,GAAK7C,CAAA,CAAQ0B,EAAA;UACbqB,CAAA,GAAK/C,CAAA,CAAQ2B,EAAA;QAEnB3B,CAAA,CAAQe,IAAA,GAAOC,CAAA,CAAYS,QAAA,EAC3BzB,CAAA,CAAQ0B,EAAA,KAAO1B,CAAA,CAAQmB,QAAA,GAAW,IAAIT,CAAA,IAAc,IAALmC,CAAA,IAAU,GACzD7C,CAAA,CAAQ2B,EAAA,KAAO3B,CAAA,CAAQmB,QAAA,GAAW,IAAIwB,CAAA,IAAc,IAALI,CAAA,IAAU,GACzD/C,CAAA,CAAQ4B,EAAA,IAAM5B,CAAA,CAAQoB,CAAA,GAAS,IAALyB,CAAA,IAAU,GACpC7C,CAAA,CAAQ6B,EAAA,IAAM7B,CAAA,CAAQsB,CAAA,GAAS,IAALyB,CAAA,IAAU;MAAA,OAEpClD,CAAA,GAAasE,GAAA,EACbpE,CAAA,GAAaoE,GAAA;MAGf,OAAOnE,CAAA;IAAA;EAAA;EAGX,SAAgBiD,EACdpD,CAAA;IAEA,IAAIE,CAAA,GAAW;MACXC,CAAA,GAAW;MACXU,CAAA,GAAgByD,GAAA;MAChBxB,CAAA,GAAgBwB,GAAA;IAEpB,OAAO,UAAmBtB,CAAA;MACxB,IAAIuB,KAAA,CAAM1D,CAAA,OAAoBmC,CAAA,CAAQ9B,IAAA,GAAOC,CAAA,CAAYO,OAAA,GACvD,MAAM,IAAIW,KAAA,CAAM;MAGlB,IAAMa,CAAA,GAASlD,CAAA,CAAEgD,CAAA,EAAS9C,CAAA,EAAUC,CAAA,EAAUU,CAAA,EAAeiC,CAAA;MAmB7D,OAjBIE,CAAA,CAAQ9B,IAAA,GAAOC,CAAA,CAAYC,UAAA,KAC7BlB,CAAA,GAAWW,CAAA,EACXV,CAAA,GAAW2C,CAAA,QAGT,MAAuBE,CAAA,CAAQzB,CAAA,KACjCrB,CAAA,GAAY8C,CAAA,CAAQ1B,QAAA,GAAWpB,CAAA,GAAW8C,CAAA,CAAQzB,CAAA,GAAIyB,CAAA,CAAQzB,CAAA,QAE5D,MAAuByB,CAAA,CAAQvB,CAAA,KACjCtB,CAAA,GAAY6C,CAAA,CAAQ1B,QAAA,GAAWnB,CAAA,GAAW6C,CAAA,CAAQvB,CAAA,GAAIuB,CAAA,CAAQvB,CAAA,GAG5DuB,CAAA,CAAQ9B,IAAA,GAAOC,CAAA,CAAYO,OAAA,KAC7Bb,CAAA,GAAgBX,CAAA,EAChB4C,CAAA,GAAgB3C,CAAA,GAGX+C,CAAA;IAAA;EAAA;EAoFX,SAAgBU,EAAO5D,CAAA,EAAWE,CAAA,EAAWC,CAAA,EAAWU,CAAA,EAAWmC,CAAA,EAAWE,CAAA;IAG5E,OAFAJ,CAAA,CAAc9C,CAAA,EAAGE,CAAA,EAAGC,CAAA,EAAGU,CAAA,EAAGmC,CAAA,EAAGE,CAAA,GAEtBE,CAAA,CAAK,UAACN,CAAA,EAASK,CAAA,EAAOC,CAAA,EAAOE,CAAA;MAClC,IAAMC,CAAA,GAAST,CAAA,CAAQjB,EAAA;QACjBJ,CAAA,GAASqB,CAAA,CAAQf,EAAA;QAGjByB,CAAA,GAASV,CAAA,CAAQxB,QAAA,KAAaiD,KAAA,CAAMjB,CAAA;QACpCK,CAAA,QAAI,MAAuBb,CAAA,CAAQvB,CAAA,GAAIuB,CAAA,CAAQvB,CAAA,GAAKiC,CAAA,GAAS,IAAIL,CAAA;QACjES,CAAA,QAAI,MAAuBd,CAAA,CAAQrB,CAAA,GAAIqB,CAAA,CAAQrB,CAAA,GAAK+B,CAAA,GAAS,IAAIJ,CAAA;MA6BvE,SAASU,EAAI9D,CAAA;QAAa,OAAOA,CAAA,GAAIA,CAAA;MAAA;MA3BjC8C,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYE,aAAA,IAAiB,MAAMnB,CAAA,KACpD4C,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYQ,OAAA,EAC3BmB,CAAA,CAAQrB,CAAA,GAAIqB,CAAA,CAAQxB,QAAA,GAAW,IAAI8B,CAAA,GAEjCN,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYK,YAAA,IAAgB,MAAMrB,CAAA,KACnD2C,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYQ,OAAA,EAC3BmB,CAAA,CAAQvB,CAAA,GAAIuB,CAAA,CAAQxB,QAAA,GAAW,IAAI6B,CAAA,QAGjC,MAAuBL,CAAA,CAAQvB,CAAA,KACjCuB,CAAA,CAAQvB,CAAA,GAAKuB,CAAA,CAAQvB,CAAA,GAAIvB,CAAA,GAAM4D,CAAA,GAAIzD,CAAA,IAAMqD,CAAA,GAAS,IAAIR,CAAA,SAEpD,MAAuBF,CAAA,CAAQrB,CAAA,KACjCqB,CAAA,CAAQrB,CAAA,GAAKkC,CAAA,GAAIzD,CAAA,GAAK4C,CAAA,CAAQrB,CAAA,GAAIZ,CAAA,IAAK2C,CAAA,GAAS,IAAIN,CAAA,SAElD,MAAuBJ,CAAA,CAAQjB,EAAA,KACjCiB,CAAA,CAAQjB,EAAA,GAAKiB,CAAA,CAAQjB,EAAA,GAAK7B,CAAA,GAAI8C,CAAA,CAAQhB,EAAA,GAAK3B,CAAA,IAAKqD,CAAA,GAAS,IAAIR,CAAA,SAE3D,MAAuBF,CAAA,CAAQhB,EAAA,KACjCgB,CAAA,CAAQhB,EAAA,GAAKyB,CAAA,GAASrD,CAAA,GAAI4C,CAAA,CAAQhB,EAAA,GAAKjB,CAAA,IAAK2C,CAAA,GAAS,IAAIN,CAAA,SAEvD,MAAuBJ,CAAA,CAAQf,EAAA,KACjCe,CAAA,CAAQf,EAAA,GAAKe,CAAA,CAAQf,EAAA,GAAK/B,CAAA,GAAI8C,CAAA,CAAQd,EAAA,GAAK7B,CAAA,IAAKqD,CAAA,GAAS,IAAIR,CAAA,SAE3D,MAAuBF,CAAA,CAAQd,EAAA,KACjCc,CAAA,CAAQd,EAAA,GAAKP,CAAA,GAASvB,CAAA,GAAI4C,CAAA,CAAQd,EAAA,GAAKnB,CAAA,IAAK2C,CAAA,GAAS,IAAIN,CAAA;MAG3D,IAAMa,CAAA,GAAM/D,CAAA,GAAIa,CAAA,GAAIX,CAAA,GAAIC,CAAA;MAExB,SAAI,MAAuB2C,CAAA,CAAQN,IAAA,KAE7B,MAAMxC,CAAA,IAAK,MAAME,CAAA,IAAK,MAAMC,CAAA,IAAK,MAAMU,CAAA,GAEzC,IAAI,MAAMkD,CAAA,SAIDjB,CAAA,CAAQR,EAAA,SACRQ,CAAA,CAAQP,EAAA,SACRO,CAAA,CAAQN,IAAA,SACRM,CAAA,CAAQL,QAAA,SACRK,CAAA,CAAQJ,SAAA,EACfI,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYQ,OAAA,MACtB;QAEL,IAAMqC,CAAA,GAAOlB,CAAA,CAAQN,IAAA,GAAOG,IAAA,CAAKM,EAAA,GAAK;UAOhCuB,CAAA,GAAS7B,IAAA,CAAKE,GAAA,CAAImB,CAAA;UAClBS,CAAA,GAAS9B,IAAA,CAAKC,GAAA,CAAIoB,CAAA;UAClBzC,CAAA,GAAS,IAAIuC,CAAA,CAAIhB,CAAA,CAAQR,EAAA;UACzBoC,CAAA,GAAS,IAAIZ,CAAA,CAAIhB,CAAA,CAAQP,EAAA;UACzBoC,CAAA,GAAIb,CAAA,CAAIW,CAAA,IAAUlD,CAAA,GAASuC,CAAA,CAAIU,CAAA,IAAUE,CAAA;UACzCE,CAAA,GAAI,IAAIJ,CAAA,GAASC,CAAA,IAAUlD,CAAA,GAASmD,CAAA;UACpCG,CAAA,GAAIf,CAAA,CAAIU,CAAA,IAAUjD,CAAA,GAASuC,CAAA,CAAIW,CAAA,IAAUC,CAAA;UAOzCI,CAAA,GAAKH,CAAA,GAAI9D,CAAA,GAAIA,CAAA,GAAI+D,CAAA,GAAI1E,CAAA,GAAIW,CAAA,GAAIgE,CAAA,GAAI3E,CAAA,GAAIA,CAAA;UACrC6E,CAAA,GAAKH,CAAA,IAAK5E,CAAA,GAAIa,CAAA,GAAIX,CAAA,GAAIC,CAAA,IAAK,KAAKwE,CAAA,GAAIxE,CAAA,GAAIU,CAAA,GAAIgE,CAAA,GAAI7E,CAAA,GAAIE,CAAA;UACpD8E,CAAA,GAAKL,CAAA,GAAIxE,CAAA,GAAIA,CAAA,GAAIyE,CAAA,GAAI5E,CAAA,GAAIG,CAAA,GAAI0E,CAAA,GAAI7E,CAAA,GAAIA,CAAA;UAerCiF,CAAA,IAAYtC,IAAA,CAAKyB,KAAA,CAAMW,CAAA,EAAID,CAAA,GAAKE,CAAA,IAAMrC,IAAA,CAAKM,EAAA,IAAMN,IAAA,CAAKM,EAAA,GAAM;UAM5DiC,CAAA,GAAYvC,IAAA,CAAKE,GAAA,CAAIoC,CAAA;UACrBE,CAAA,GAAYxC,IAAA,CAAKC,GAAA,CAAIqC,CAAA;QAE3BnC,CAAA,CAAQR,EAAA,GAAKK,IAAA,CAAKU,GAAA,CAAIU,CAAA,IACpBpB,IAAA,CAAKe,IAAA,CAAKoB,CAAA,GAAKhB,CAAA,CAAIqB,CAAA,IAAaJ,CAAA,GAAKG,CAAA,GAAYC,CAAA,GAAYH,CAAA,GAAKlB,CAAA,CAAIoB,CAAA,IACxEpC,CAAA,CAAQP,EAAA,GAAKI,IAAA,CAAKU,GAAA,CAAIU,CAAA,IACpBpB,IAAA,CAAKe,IAAA,CAAKoB,CAAA,GAAKhB,CAAA,CAAIoB,CAAA,IAAaH,CAAA,GAAKG,CAAA,GAAYC,CAAA,GAAYH,CAAA,GAAKlB,CAAA,CAAIqB,CAAA,IACxErC,CAAA,CAAQN,IAAA,GAAiB,MAAVyC,CAAA,GAAgBtC,IAAA,CAAKM,EAAA;MAAA;MAW1C,YAHI,MAAuBH,CAAA,CAAQJ,SAAA,IAAa,IAAIqB,CAAA,KAClDjB,CAAA,CAAQJ,SAAA,KAAcI,CAAA,CAAQJ,SAAA,GAEzBI,CAAA;IAAA;EAAA;EAwDX,SAAgBgB,EAAA;IACd,OAAO,UAAC9D,CAAA;MACN,IAAME,CAAA,GAAS;MAEf,KAAK,IAAMC,CAAA,IAAOH,CAAA,EAChBE,CAAA,CAAOC,CAAA,IAA2BH,CAAA,CAAEG,CAAA;MAEtC,OAAOD,CAAA;IAAA;EAAA;EAzfKF,CAAA,CAAAoF,KAAA,GAAhB,UAAsBpF,CAAA;IAEpB,SAASE,EAAGA,CAAA;MAAe,OAAOyC,IAAA,CAAK0C,KAAA,CAAMnF,CAAA,GAAMF,CAAA,IAAYA,CAAA;IAAA;IAC/D,kBAAAA,CAAA,KAHoBA,CAAA,UACpB8C,CAAA,CAAc9C,CAAA,GAEP,UAAeA,CAAA;MA6BpB,YA5BI,MAAuBA,CAAA,CAAQ6B,EAAA,KACjC7B,CAAA,CAAQ6B,EAAA,GAAK3B,CAAA,CAAGF,CAAA,CAAQ6B,EAAA,SAEtB,MAAuB7B,CAAA,CAAQ8B,EAAA,KACjC9B,CAAA,CAAQ8B,EAAA,GAAK5B,CAAA,CAAGF,CAAA,CAAQ8B,EAAA,SAGtB,MAAuB9B,CAAA,CAAQ+B,EAAA,KACjC/B,CAAA,CAAQ+B,EAAA,GAAK7B,CAAA,CAAGF,CAAA,CAAQ+B,EAAA,SAEtB,MAAuB/B,CAAA,CAAQgC,EAAA,KACjChC,CAAA,CAAQgC,EAAA,GAAK9B,CAAA,CAAGF,CAAA,CAAQgC,EAAA,SAGtB,MAAuBhC,CAAA,CAAQuB,CAAA,KACjCvB,CAAA,CAAQuB,CAAA,GAAIrB,CAAA,CAAGF,CAAA,CAAQuB,CAAA,SAErB,MAAuBvB,CAAA,CAAQyB,CAAA,KACjCzB,CAAA,CAAQyB,CAAA,GAAIvB,CAAA,CAAGF,CAAA,CAAQyB,CAAA,SAGrB,MAAuBzB,CAAA,CAAQsC,EAAA,KACjCtC,CAAA,CAAQsC,EAAA,GAAKpC,CAAA,CAAGF,CAAA,CAAQsC,EAAA,SAEtB,MAAuBtC,CAAA,CAAQuC,EAAA,KACjCvC,CAAA,CAAQuC,EAAA,GAAKrC,CAAA,CAAGF,CAAA,CAAQuC,EAAA,IAGnBvC,CAAA;IAAA;EAAA,GAIKA,CAAA,CAAAsF,MAAA,GAAApF,CAAA,EA8BAF,CAAA,CAAAuF,MAAA,GAAhB;IACE,OAAOnC,CAAA,CAAK,UAACpD,CAAA,EAASE,CAAA,EAAOC,CAAA;MAyB3B,OAxBKH,CAAA,CAAQsB,QAAA,UAEP,MAAuBtB,CAAA,CAAQ6B,EAAA,KACjC7B,CAAA,CAAQ6B,EAAA,IAAM3B,CAAA,QAEZ,MAAuBF,CAAA,CAAQ8B,EAAA,KACjC9B,CAAA,CAAQ8B,EAAA,IAAM3B,CAAA,QAGZ,MAAuBH,CAAA,CAAQ+B,EAAA,KACjC/B,CAAA,CAAQ+B,EAAA,IAAM7B,CAAA,QAEZ,MAAuBF,CAAA,CAAQgC,EAAA,KACjChC,CAAA,CAAQgC,EAAA,IAAM7B,CAAA,QAGZ,MAAuBH,CAAA,CAAQuB,CAAA,KACjCvB,CAAA,CAAQuB,CAAA,IAAKrB,CAAA,QAEX,MAAuBF,CAAA,CAAQyB,CAAA,KACjCzB,CAAA,CAAQyB,CAAA,IAAKtB,CAAA,GAEfH,CAAA,CAAQsB,QAAA,IAAW,IAEdtB,CAAA;IAAA;EAAA,GAIKA,CAAA,CAAAwF,aAAA,GAAhB,UAA8BxF,CAAA,EAAmBE,CAAA,EAAmBC,CAAA;IAClE,kBAAAH,CAAA,KAD4BA,CAAA,mBAAAE,CAAA,KAAmBA,CAAA,mBAAAC,CAAA,KAAmBA,CAAA,QAC3DiD,CAAA,CAAK,UAACvC,CAAA,EAASiC,CAAA,EAAOE,CAAA,EAAOE,CAAA,EAAYC,CAAA;MAC9C,IAAIoB,KAAA,CAAMrB,CAAA,OAAiBrC,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYO,OAAA,GACpD,MAAM,IAAIW,KAAA,CAAM;MAuBlB,OArBInC,CAAA,IAAcW,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYE,aAAA,KAC3CR,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYQ,OAAA,EAC3Bd,CAAA,CAAQY,CAAA,GAAIZ,CAAA,CAAQS,QAAA,GAAW,IAAI0B,CAAA,GAEjC7C,CAAA,IAAcU,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYK,YAAA,KAC3CX,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYQ,OAAA,EAC3Bd,CAAA,CAAQU,CAAA,GAAIV,CAAA,CAAQS,QAAA,GAAW,IAAIwB,CAAA,GAEjC9C,CAAA,IAAca,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYC,UAAA,KAC3CP,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYQ,OAAA,EAC3Bd,CAAA,CAAQU,CAAA,GAAIV,CAAA,CAAQS,QAAA,GAAW4B,CAAA,GAAaJ,CAAA,GAAQI,CAAA,EACpDrC,CAAA,CAAQY,CAAA,GAAIZ,CAAA,CAAQS,QAAA,GAAW6B,CAAA,GAAaH,CAAA,GAAQG,CAAA,GAElDtC,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYiB,GAAA,KAAQ,MAAMvB,CAAA,CAAQyB,EAAA,IAAM,MAAMzB,CAAA,CAAQ0B,EAAA,MACvE1B,CAAA,CAAQK,IAAA,GAAOC,CAAA,CAAYQ,OAAA,SACpBd,CAAA,CAAQyB,EAAA,SACRzB,CAAA,CAAQ0B,EAAA,SACR1B,CAAA,CAAQ2B,IAAA,SACR3B,CAAA,CAAQ4B,QAAA,SACR5B,CAAA,CAAQ6B,SAAA,GAEV7B,CAAA;IAAA;EAAA,GAMKb,CAAA,CAAAyF,YAAA,GAAAtF,CAAA,EAgDAH,CAAA,CAAA0F,OAAA,GAAA1C,CAAA,EA+BAhD,CAAA,CAAA2F,IAAA,GAAAvC,CAAA,EAsCApD,CAAA,CAAA4F,QAAA,GAAhB,UAAyB5F,CAAA;IAAA,WAAAA,CAAA,KAAAA,CAAA,OACvB8C,CAAA,CAAc9C,CAAA;IACd,IAAIE,CAAA,GAAeoE,GAAA;MACfnE,CAAA,GAAemE,GAAA;MACfzD,CAAA,GAAayD,GAAA;MACbtB,CAAA,GAAasB,GAAA;IAEjB,OAAOlB,CAAA,CAAK,UAACN,CAAA,EAASI,CAAA,EAAOC,CAAA,EAAOC,CAAA,EAAYE,CAAA;MAC9C,IAAMC,CAAA,GAAMZ,IAAA,CAAKU,GAAA;QACb5B,CAAA,IAAO;QACP+B,CAAA,GAAQ;QACRG,CAAA,GAAQ;MAwBZ,IAtBIb,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYc,eAAA,KAC7BuB,CAAA,GAAQe,KAAA,CAAMrE,CAAA,IAAgB,IAAIgD,CAAA,GAAQhD,CAAA,EAC1CyD,CAAA,GAAQY,KAAA,CAAMpE,CAAA,IAAgB,IAAIgD,CAAA,GAAQhD,CAAA,GAExC2C,CAAA,CAAQ5B,IAAA,IAAQC,CAAA,CAAYS,QAAA,GAAWT,CAAA,CAAYc,eAAA,KACrD/B,CAAA,GAAe4C,CAAA,CAAQxB,QAAA,GAAW4B,CAAA,GAAQJ,CAAA,CAAQf,EAAA,GAAKe,CAAA,CAAQf,EAAA,EAC/D5B,CAAA,GAAe2C,CAAA,CAAQxB,QAAA,GAAW6B,CAAA,GAAQL,CAAA,CAAQd,EAAA,GAAKc,CAAA,CAAQd,EAAA,KAE/D9B,CAAA,GAAeoE,GAAA,EACfnE,CAAA,GAAemE,GAAA,GAEbxB,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYgB,cAAA,IAC7BtB,CAAA,GAAa0D,KAAA,CAAM1D,CAAA,IAAcqC,CAAA,GAAQ,IAAIA,CAAA,GAAQrC,CAAA,EACrDmC,CAAA,GAAauB,KAAA,CAAMvB,CAAA,IAAcG,CAAA,GAAQ,IAAIA,CAAA,GAAQH,CAAA,IAC5CF,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYe,OAAA,IACpCrB,CAAA,GAAaiC,CAAA,CAAQxB,QAAA,GAAW4B,CAAA,GAAQJ,CAAA,CAAQjB,EAAA,GAAKiB,CAAA,CAAQjB,EAAA,EAC7DmB,CAAA,GAAaF,CAAA,CAAQxB,QAAA,GAAW6B,CAAA,GAAQL,CAAA,CAAQhB,EAAA,GAAKgB,CAAA,CAAQd,EAAA,KAE7DnB,CAAA,GAAayD,GAAA,EACbtB,CAAA,GAAasB,GAAA,GAGXxB,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAY0E,aAAA,IAC7B/C,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYiB,GAAA,KAAQ,MAAMU,CAAA,CAAQR,EAAA,IAAM,MAAMQ,CAAA,CAAQP,EAAA,KAAOO,CAAA,CAAQL,QAAA,KACpFK,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYS,QAAA,IAAYkB,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYc,eAAA,IAClEa,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYe,OAAA,IAAWY,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYgB,cAAA,EAAgB;QACjF,IAAMyB,CAAA,QAAO,MAAuBd,CAAA,CAAQvB,CAAA,GAAI,IAC7CuB,CAAA,CAAQxB,QAAA,GAAWwB,CAAA,CAAQvB,CAAA,GAAIuB,CAAA,CAAQvB,CAAA,GAAI2B,CAAA;UACxCY,CAAA,QAAO,MAAuBhB,CAAA,CAAQrB,CAAA,GAAI,IAC7CqB,CAAA,CAAQxB,QAAA,GAAWwB,CAAA,CAAQrB,CAAA,GAAIqB,CAAA,CAAQrB,CAAA,GAAI0B,CAAA;QAE9CK,CAAA,GAASe,KAAA,CAAM1D,CAAA,SACb,MAAuBiC,CAAA,CAAQjB,EAAA,GAAK2B,CAAA,GAClCV,CAAA,CAAQxB,QAAA,GAAWwB,CAAA,CAAQvB,CAAA,GACzBuB,CAAA,CAAQjB,EAAA,GAAKqB,CAAA,GAHUrC,CAAA,GAAaqC,CAAA,EAI1CS,CAAA,GAASY,KAAA,CAAMvB,CAAA,SACb,MAAuBF,CAAA,CAAQhB,EAAA,GAAK6B,CAAA,GAClCb,CAAA,CAAQxB,QAAA,GAAWwB,CAAA,CAAQrB,CAAA,GACzBqB,CAAA,CAAQhB,EAAA,GAAKqB,CAAA,GAHUH,CAAA,GAAaG,CAAA;QAK1C,IAAMY,CAAA,QAAQ,MAAuBjB,CAAA,CAAQf,EAAA,GAAK,IAC/Ce,CAAA,CAAQxB,QAAA,GAAWwB,CAAA,CAAQvB,CAAA,GAAIuB,CAAA,CAAQf,EAAA,GAAKmB,CAAA;UACzCc,CAAA,QAAQ,MAAuBlB,CAAA,CAAQd,EAAA,GAAK,IAC/Cc,CAAA,CAAQxB,QAAA,GAAWwB,CAAA,CAAQrB,CAAA,GAAIqB,CAAA,CAAQd,EAAA,GAAKmB,CAAA;QAE3CI,CAAA,CAAIK,CAAA,KAAS5D,CAAA,IAAOuD,CAAA,CAAIO,CAAA,KAAS9D,CAAA,IACnCuD,CAAA,CAAIC,CAAA,KAAUxD,CAAA,IAAOuD,CAAA,CAAII,CAAA,KAAU3D,CAAA,IACnCuD,CAAA,CAAIQ,CAAA,KAAU/D,CAAA,IAAOuD,CAAA,CAAIS,CAAA,KAAUhE,CAAA,KACnCyB,CAAA,IAAO;MAAA;MAUX,OANIqB,CAAA,CAAQ5B,IAAA,GAAOC,CAAA,CAAYC,UAAA,IACzBmC,CAAA,CAAIL,CAAA,GAAQE,CAAA,KAAepD,CAAA,IAAOuD,CAAA,CAAIJ,CAAA,GAAQG,CAAA,KAAetD,CAAA,KAC/DyB,CAAA,IAAO,IAIJA,CAAA,GAAO,KAAKqB,CAAA;IAAA;EAAA,GAOP9C,CAAA,CAAA8F,MAAA,GAAAlC,CAAA,EA0HA5D,CAAA,CAAA+F,MAAA,GAAhB,UAAuB/F,CAAA,EAAWE,CAAA,EAAOC,CAAA;IAAA,WAAAD,CAAA,KAAPA,CAAA,kBAAAC,CAAA,KAAOA,CAAA,OACvC2C,CAAA,CAAc9C,CAAA,EAAGE,CAAA,EAAGC,CAAA;IACpB,IAAMU,CAAA,GAAM8B,IAAA,CAAKE,GAAA,CAAI7C,CAAA;MACfgD,CAAA,GAAML,IAAA,CAAKC,GAAA,CAAI5C,CAAA;IAErB,OAAO4D,CAAA,CAAOZ,CAAA,EAAKnC,CAAA,GAAMA,CAAA,EAAKmC,CAAA,EAAK9C,CAAA,GAAIA,CAAA,GAAI8C,CAAA,GAAM7C,CAAA,GAAIU,CAAA,EAAKV,CAAA,GAAID,CAAA,GAAIW,CAAA,GAAMV,CAAA,GAAI6C,CAAA;EAAA,GAE9DhD,CAAA,CAAAgG,SAAA,GAAhB,UAA0BhG,CAAA,EAAYE,CAAA;IAEpC,kBAAAA,CAAA,KAFoCA,CAAA,OACpC4C,CAAA,CAAc9C,CAAA,EAAIE,CAAA,GACX0D,CAAA,CAAO,GAAG,GAAG,GAAG,GAAG5D,CAAA,EAAIE,CAAA;EAAA,GAEhBF,CAAA,CAAAiG,KAAA,GAAhB,UAAsBjG,CAAA,EAAYE,CAAA;IAEhC,kBAAAA,CAAA,KAFgCA,CAAA,GAAAF,CAAA,GAChC8C,CAAA,CAAc9C,CAAA,EAAIE,CAAA,GACX0D,CAAA,CAAO5D,CAAA,EAAI,GAAG,GAAGE,CAAA,EAAI,GAAG;EAAA,GAEjBF,CAAA,CAAAkG,MAAA,GAAhB,UAAuBlG,CAAA;IAErB,OADA8C,CAAA,CAAc9C,CAAA,GACP4D,CAAA,CAAO,GAAG,GAAGjB,IAAA,CAAKwD,IAAA,CAAKnG,CAAA,GAAI,GAAG,GAAG;EAAA,GAE1BA,CAAA,CAAAoG,MAAA,GAAhB,UAAuBpG,CAAA;IAErB,OADA8C,CAAA,CAAc9C,CAAA,GACP4D,CAAA,CAAO,GAAGjB,IAAA,CAAKwD,IAAA,CAAKnG,CAAA,GAAI,GAAG,GAAG,GAAG;EAAA,GAE1BA,CAAA,CAAAqG,eAAA,GAAhB,UAAgCrG,CAAA;IAE9B,kBAAAA,CAAA,KAF8BA,CAAA,OAC9B8C,CAAA,CAAc9C,CAAA,GACP4D,CAAA,EAAQ,GAAG,GAAG,GAAG,GAAG5D,CAAA,EAAS;EAAA,GAEtBA,CAAA,CAAAsG,eAAA,GAAhB,UAAgCtG,CAAA;IAE9B,kBAAAA,CAAA,KAF8BA,CAAA,OAC9B8C,CAAA,CAAc9C,CAAA,GACP4D,CAAA,CAAO,GAAG,GAAG,IAAI,GAAG,GAAG5D,CAAA;EAAA,GAGhBA,CAAA,CAAAuG,MAAA,GAAhB;IACE,OAAOnD,CAAA,CAAK,UAACpD,CAAA,EAASE,CAAA,EAAOC,CAAA;MAC3B,OAAIgB,CAAA,CAAYiB,GAAA,KAAQpC,CAAA,CAAQkB,IAAA,aD3UlBlB,CAAA,EAAeE,CAAA,EAAYC,CAAA;QAAA,IAAA2C,CAAA,EAAAE,CAAA,EAAAG,CAAA,EAAAC,CAAA;QACxCpD,CAAA,CAAIiE,EAAA,IACPf,CAAA,CAAmBlD,CAAA,EAAKE,CAAA,EAAIC,CAAA;QAQ9B,KALA,IAAMsB,CAAA,GAASkB,IAAA,CAAK6D,GAAA,CAAIxG,CAAA,CAAImE,IAAA,EAAOnE,CAAA,CAAIqE,IAAA,GAAiDb,CAAA,GAAhCb,IAAA,CAAKkB,GAAA,CAAI7D,CAAA,CAAImE,IAAA,EAAOnE,CAAA,CAAIqE,IAAA,IAA4B5C,CAAA,EACtGkC,CAAA,GAAYhB,IAAA,CAAK8D,IAAA,CAAKjD,CAAA,GAAW,KAEjCI,CAAA,GAAqB,IAAIrD,KAAA,CAAMoD,CAAA,GACjCG,CAAA,GAAQ5D,CAAA,EAAI6D,CAAA,GAAQ5D,CAAA,EACf6D,CAAA,GAAI,GAAGA,CAAA,GAAIL,CAAA,EAAWK,CAAA,IAAK;UAClC,IAAMQ,CAAA,GAAWjB,CAAA,CAAKvD,CAAA,CAAImE,IAAA,EAAOnE,CAAA,CAAIqE,IAAA,EAAOL,CAAA,GAAIL,CAAA;YAC1Cc,CAAA,GAASlB,CAAA,CAAKvD,CAAA,CAAImE,IAAA,EAAOnE,CAAA,CAAIqE,IAAA,GAAQL,CAAA,GAAI,KAAKL,CAAA;YAC9CpC,CAAA,GAAWkD,CAAA,GAASD,CAAA;YACpBE,CAAA,GAAI,IAAI,IAAI/B,IAAA,CAAK+D,GAAA,CAAInF,CAAA,GAAW+B,CAAA,GAAM;YAEtCqB,CAAA,GAAW,CACfhC,IAAA,CAAKC,GAAA,CAAI4B,CAAA,GAAWlB,CAAA,IAAOoB,CAAA,GAAI/B,IAAA,CAAKE,GAAA,CAAI2B,CAAA,GAAWlB,CAAA,GACnDX,IAAA,CAAKE,GAAA,CAAI2B,CAAA,GAAWlB,CAAA,IAAOoB,CAAA,GAAI/B,IAAA,CAAKC,GAAA,CAAI4B,CAAA,GAAWlB,CAAA;YAF9CsB,CAAA,GAAAD,CAAA;YAAIE,CAAA,GAAAF,CAAA;YAGLG,CAAA,GAAS,CAACnC,IAAA,CAAKC,GAAA,CAAI6B,CAAA,GAASnB,CAAA,GAAMX,IAAA,CAAKE,GAAA,CAAI4B,CAAA,GAASnB,CAAA;YAAnDyB,CAAA,GAAAD,CAAA;YAAGE,CAAA,GAAAF,CAAA;YACJG,CAAA,GAAW,CAACF,CAAA,GAAIL,CAAA,GAAI/B,IAAA,CAAKE,GAAA,CAAI4B,CAAA,GAASnB,CAAA,GAAM0B,CAAA,GAAIN,CAAA,GAAI/B,IAAA,CAAKC,GAAA,CAAI6B,CAAA,GAASnB,CAAA;YAArE4B,CAAA,GAAAD,CAAA;YAAIE,CAAA,GAAAF,CAAA;UACXrB,CAAA,CAAOI,CAAA,IAAK;YAAC1C,QAAA,EAAUtB,CAAA,CAAIsB,QAAA;YAAUJ,IAAA,EAAMC,CAAA,CAAYS;UAAA;UACvD,IAAM+E,CAAA,GAAY,SAAAC,CAAC1G,CAAA,EAAWC,CAAA;YACtB,IAAA2C,CAAA,GAAiBjC,CAAA,CAAO,CAACX,CAAA,GAAIF,CAAA,CAAIsC,EAAA,EAAInC,CAAA,GAAIH,CAAA,CAAIuC,EAAA,GAAKvC,CAAA,CAAIwC,IAAA;cAArDQ,CAAA,GAAAF,CAAA;cAAOI,CAAA,GAAAJ,CAAA;YACd,OAAO,CAAC9C,CAAA,CAAIiE,EAAA,GAAMjB,CAAA,EAAOhD,CAAA,CAAIkE,EAAA,GAAMhB,CAAA;UAAA;UAErCJ,CAAA,GAA+B6D,CAAA,CAAU/B,CAAA,EAAIC,CAAA,GAA5CjB,CAAA,CAAOI,CAAA,EAAGnC,EAAA,GAAAiB,CAAA,KAAIc,CAAA,CAAOI,CAAA,EAAGlC,EAAA,GAAAgB,CAAA,KACzBE,CAAA,GAA+B2D,CAAA,CAAUzB,CAAA,EAAIC,CAAA,GAA5CvB,CAAA,CAAOI,CAAA,EAAGjC,EAAA,GAAAiB,CAAA,KAAIY,CAAA,CAAOI,CAAA,EAAGhC,EAAA,GAAAgB,CAAA,KACzBG,CAAA,GAA6BwD,CAAA,CAAU5B,CAAA,EAAGC,CAAA,GAAzCpB,CAAA,CAAOI,CAAA,EAAGzC,CAAA,GAAA4B,CAAA,KAAGS,CAAA,CAAOI,CAAA,EAAGvC,CAAA,GAAA0B,CAAA,KACpBnD,CAAA,CAAIsB,QAAA,KACNsC,CAAA,CAAOI,CAAA,EAAGnC,EAAA,IAAMiC,CAAA,EAChBF,CAAA,CAAOI,CAAA,EAAGlC,EAAA,IAAMiC,CAAA,EAChBH,CAAA,CAAOI,CAAA,EAAGjC,EAAA,IAAM+B,CAAA,EAChBF,CAAA,CAAOI,CAAA,EAAGhC,EAAA,IAAM+B,CAAA,EAChBH,CAAA,CAAOI,CAAA,EAAGzC,CAAA,IAAKuC,CAAA,EACfF,CAAA,CAAOI,CAAA,EAAGvC,CAAA,IAAKsC,CAAA,GAEhBD,CAAA,IAADV,CAAA,GAAiB,CAACQ,CAAA,CAAOI,CAAA,EAAGzC,CAAA,EAAGqC,CAAA,CAAOI,CAAA,EAAGvC,CAAA,OAAjCsC,CAAA,GAAAX,CAAA;QAAA;QAEV,OAAOQ,CAAA;MAAA,CCqSM,CAAI5D,CAAA,EAASA,CAAA,CAAQsB,QAAA,GAAW,IAAIpB,CAAA,EAAOF,CAAA,CAAQsB,QAAA,GAAW,IAAInB,CAAA,IAEpEH,CAAA;IAAA;EAAA,GAIKA,CAAA,CAAA6G,aAAA,GAAhB;IACE,OAAOzD,CAAA,CAAK,UAACpD,CAAA,EAAGE,CAAA,EAAIC,CAAA;MAQlB,OAPIH,CAAA,CAAEsB,QAAA,KACJpB,CAAA,GAAK,GACLC,CAAA,GAAK,IAEHgB,CAAA,CAAYiB,GAAA,KAAQpC,CAAA,CAAEkB,IAAA,IACxBgC,CAAA,CAAmBlD,CAAA,EAAGE,CAAA,EAAIC,CAAA,GAErBH,CAAA;IAAA;EAAA,GAGKA,CAAA,CAAA8G,KAAA,GAAAhD,CAAA,EAWA9D,CAAA,CAAA+G,gBAAA,GAAhB;IACE,IAAM/G,CAAA,GAXC,SAAAgH,CAAChH,CAAA;QACN,IAAME,CAAA,GAAS;QAEf,KAAK,IAAMC,CAAA,IAAOH,CAAA,EAChBE,CAAA,CAAOC,CAAA,IAA2BH,CAAA,CAAEG,CAAA;QAEtC,OAAOD,CAAA;MAAA;MAMHW,CAAA,GAAQX,CAAA;MACR4C,CAAA,GAAQE,CAAA;MACRM,CAAA,GAASnD,CAAA;MACToD,CAAA,GACFH,CAAA,CAAK,UAAClD,CAAA,EAASC,CAAA,EAAU6C,CAAA;QAC3B,IAAMI,CAAA,GAAIE,CAAA,CAAOR,CAAA,CAAMjC,CAAA,CAAMb,CAAA,CAAME,CAAA;QACnC,SAAS0D,EAAK5D,CAAA;UACRA,CAAA,GAAOuD,CAAA,CAAE0D,IAAA,KAAQ1D,CAAA,CAAE0D,IAAA,GAAOjH,CAAA,GAC1BA,CAAA,GAAOuD,CAAA,CAAE2D,IAAA,KAAQ3D,CAAA,CAAE2D,IAAA,GAAOlH,CAAA;QAAA;QAEhC,SAAS8D,EAAK9D,CAAA;UACRA,CAAA,GAAOuD,CAAA,CAAE4D,IAAA,KAAQ5D,CAAA,CAAE4D,IAAA,GAAOnH,CAAA,GAC1BA,CAAA,GAAOuD,CAAA,CAAE6D,IAAA,KAAQ7D,CAAA,CAAE6D,IAAA,GAAOpH,CAAA;QAAA;QAgBhC,IAdIoD,CAAA,CAAElC,IAAA,GAAOC,CAAA,CAAYkG,gBAAA,KACvBzD,CAAA,CAAKzD,CAAA,GACL2D,CAAA,CAAKd,CAAA,IAEHI,CAAA,CAAElC,IAAA,GAAOC,CAAA,CAAYE,aAAA,IACvBuC,CAAA,CAAKR,CAAA,CAAE7B,CAAA,GAEL6B,CAAA,CAAElC,IAAA,GAAOC,CAAA,CAAYK,YAAA,IACvBsC,CAAA,CAAKV,CAAA,CAAE3B,CAAA,GAEL2B,CAAA,CAAElC,IAAA,GAAOC,CAAA,CAAYQ,OAAA,KACvBiC,CAAA,CAAKR,CAAA,CAAE7B,CAAA,GACPuC,CAAA,CAAKV,CAAA,CAAE3B,CAAA,IAEL2B,CAAA,CAAElC,IAAA,GAAOC,CAAA,CAAYS,QAAA,EAAU;UAEjCgC,CAAA,CAAKR,CAAA,CAAE7B,CAAA,GACPuC,CAAA,CAAKV,CAAA,CAAE3B,CAAA;UAGP,KAFA,IAAAsC,CAAA,MAEwBC,CAAA,GAFJR,CAAA,CAAWrD,CAAA,EAAUiD,CAAA,CAAEvB,EAAA,EAAIuB,CAAA,CAAErB,EAAA,EAAIqB,CAAA,CAAE7B,CAAA,GAE/BwC,CAAA,GAAAC,CAAA,CAAA/C,MAAA,EAAA8C,CAAA,IAAa;YAC/B,KADKuD,CAAA,GAAAtD,CAAA,CAAAD,CAAA,MACY,IAAIuD,CAAA,IACvB1D,CAAA,CAAKD,CAAA,CAASxD,CAAA,EAAUiD,CAAA,CAAEvB,EAAA,EAAIuB,CAAA,CAAErB,EAAA,EAAIqB,CAAA,CAAE7B,CAAA,EAAG+F,CAAA;UAAA;UAK7C,KAFA,IAAA9C,CAAA,MAEwBC,CAAA,GAFJjB,CAAA,CAAWR,CAAA,EAAUI,CAAA,CAAEtB,EAAA,EAAIsB,CAAA,CAAEpB,EAAA,EAAIoB,CAAA,CAAE3B,CAAA,GAE/B+C,CAAA,GAAAC,CAAA,CAAAxD,MAAA,EAAAuD,CAAA,IAAa;YAC/B,KADK8C,CAAA,GAAA7C,CAAA,CAAAD,CAAA,MACY,IAAI8C,CAAA,IACvBxD,CAAA,CAAKH,CAAA,CAASX,CAAA,EAAUI,CAAA,CAAEtB,EAAA,EAAIsB,CAAA,CAAEpB,EAAA,EAAIoB,CAAA,CAAE3B,CAAA,EAAG6F,CAAA;UAAA;QAAA;QAI/C,IAAIlE,CAAA,CAAElC,IAAA,GAAOC,CAAA,CAAYiB,GAAA,EAAK;UAE5BwB,CAAA,CAAKR,CAAA,CAAE7B,CAAA,GACPuC,CAAA,CAAKV,CAAA,CAAE3B,CAAA,GACPyB,CAAA,CAAmBE,CAAA,EAAGjD,CAAA,EAAU6C,CAAA;UAwBhC,KArBA,IAAMzB,CAAA,GAAU6B,CAAA,CAAEZ,IAAA,GAAO,MAAMG,IAAA,CAAKM,EAAA,EAE9ByB,CAAA,GAAK/B,IAAA,CAAKC,GAAA,CAAIrB,CAAA,IAAW6B,CAAA,CAAEd,EAAA,EAC3BqC,CAAA,GAAKhC,IAAA,CAAKE,GAAA,CAAItB,CAAA,IAAW6B,CAAA,CAAEd,EAAA,EAC3BsC,CAAA,IAAOjC,IAAA,CAAKE,GAAA,CAAItB,CAAA,IAAW6B,CAAA,CAAEb,EAAA,EAC7BsC,CAAA,GAAMlC,IAAA,CAAKC,GAAA,CAAIrB,CAAA,IAAW6B,CAAA,CAAEb,EAAA,EAI5BuC,CAAA,GAAmB1B,CAAA,CAAEe,IAAA,GAAOf,CAAA,CAAEiB,IAAA,GAClC,CAACjB,CAAA,CAAEe,IAAA,EAAMf,CAAA,CAAEiB,IAAA,KACT,MAAMjB,CAAA,CAAEiB,IAAA,GAAO,CAACjB,CAAA,CAAEiB,IAAA,GAAO,KAAKjB,CAAA,CAAEe,IAAA,GAAO,OAAO,CAACf,CAAA,CAAEiB,IAAA,EAAMjB,CAAA,CAAEe,IAAA,GAFtDY,CAAA,GAAAD,CAAA,KAAQE,CAAA,GAAAF,CAAA,KAGTG,CAAA,GAAiB,SAAAsC,CAACvH,CAAA;cAAA,IAACE,CAAA,GAAAF,CAAA;gBAAIG,CAAA,GAAAH,CAAA;gBAErBa,CAAA,GAAe,MADN8B,IAAA,CAAKyB,KAAA,CAAMjE,CAAA,EAAKD,CAAA,IACJyC,IAAA,CAAKM,EAAA;cAEhC,OAAOpC,CAAA,GAAMkE,CAAA,GAASlE,CAAA,GAAM,MAAMA,CAAA;YAAA,GAAAqE,CAAA,MAKZC,CAAA,GADJhC,CAAA,CAA2ByB,CAAA,GAAMF,CAAA,EAAI,GAAG8C,GAAA,CAAIvC,CAAA,GACxCC,CAAA,GAAAC,CAAA,CAAAlE,MAAA,EAAAiE,CAAA,IAAa;YAAA,CAA1BoC,CAAA,GAAAnC,CAAA,CAAAD,CAAA,KACOH,CAAA,IAAUuC,CAAA,GAAYtC,CAAA,IACpCpB,CAAA,CAAKnC,CAAA,CAAM2B,CAAA,CAAEa,EAAA,EAAIS,CAAA,EAAIE,CAAA,EAAK0C,CAAA;UAAA;UAK9B,KADA,IAAAX,CAAA,MACwBc,CAAA,GADJtE,CAAA,CAA2B0B,CAAA,GAAMF,CAAA,EAAI,GAAG6C,GAAA,CAAIvC,CAAA,GACxC0B,CAAA,GAAAc,CAAA,CAAAxG,MAAA,EAAA0F,CAAA,IAAa;YAAhC,IAAMW,CAAA;YAAA,CAAAA,CAAA,GAAAG,CAAA,CAAAd,CAAA,KACO5B,CAAA,IAAUuC,CAAA,GAAYtC,CAAA,IACpClB,CAAA,CAAKrC,CAAA,CAAM2B,CAAA,CAAEc,EAAA,EAAIS,CAAA,EAAIE,CAAA,EAAKyC,CAAA;UAAA;QAAA;QAIhC,OAAOpH,CAAA;MAAA;IAOT,OAJAqD,CAAA,CAAE2D,IAAA,GAAO,OACT3D,CAAA,CAAE0D,IAAA,IAAQ,OACV1D,CAAA,CAAE6D,IAAA,GAAO,OACT7D,CAAA,CAAE4D,IAAA,IAAQ,OACH5D,CAAA;EAAA;AAAA,CAjmBX,CAAiBH,CAAA,KAAAA,CAAA;ACLjB,IAAAQ,CAAA;EAAAE,CAAA;IAAA,SAAA9D,EAAA;IAsEA,OArEEA,CAAA,CAAAQ,SAAA,CAAA6E,KAAA,aAAMrF,CAAA;MACJ,OAAO,KAAK4G,SAAA,CAAUxD,CAAA,CAAuBgC,KAAA,CAAMpF,CAAA;IAAA,GAGrDA,CAAA,CAAAQ,SAAA,CAAAkH,KAAA;MACE,OAAO,KAAKd,SAAA,CAAUxD,CAAA,CAAuBkC,MAAA;IAAA,GAG/CtF,CAAA,CAAAQ,SAAA,CAAAmH,KAAA;MACE,OAAO,KAAKf,SAAA,CAAUxD,CAAA,CAAuBmC,MAAA;IAAA,GAG/CvF,CAAA,CAAAQ,SAAA,CAAAoH,YAAA,aAAa5H,CAAA,EAAaE,CAAA,EAAaC,CAAA;MACrC,OAAO,KAAKyG,SAAA,CAAUxD,CAAA,CAAuBoC,aAAA,CAAcxF,CAAA,EAAGE,CAAA,EAAGC,CAAA;IAAA,GAGnEH,CAAA,CAAAQ,SAAA,CAAAqH,WAAA;MACE,OAAO,KAAKjB,SAAA,CAAUxD,CAAA,CAAuBqC,YAAA;IAAA,GAG/CzF,CAAA,CAAAQ,SAAA,CAAAsH,KAAA;MACE,OAAO,KAAKlB,SAAA,CAAUxD,CAAA,CAAuBsC,OAAA;IAAA,GAG/C1F,CAAA,CAAAQ,SAAA,CAAAuH,IAAA;MACE,OAAO,KAAKnB,SAAA,CAAUxD,CAAA,CAAuBmD,MAAA;IAAA,GAG/CvG,CAAA,CAAAQ,SAAA,CAAAwH,QAAA,aAAShI,CAAA;MACP,OAAO,KAAK4G,SAAA,CAAUxD,CAAA,CAAuBwC,QAAA,CAAS5F,CAAA;IAAA,GAGxDA,CAAA,CAAAQ,SAAA,CAAAyH,SAAA,aAAUjI,CAAA,EAAWE,CAAA;MACnB,OAAO,KAAK0G,SAAA,CAAUxD,CAAA,CAAuB4C,SAAA,CAAUhG,CAAA,EAAGE,CAAA;IAAA,GAG5DF,CAAA,CAAAQ,SAAA,CAAA0H,KAAA,aAAMlI,CAAA,EAAWE,CAAA;MACf,OAAO,KAAK0G,SAAA,CAAUxD,CAAA,CAAuB6C,KAAA,CAAMjG,CAAA,EAAGE,CAAA;IAAA,GAGxDF,CAAA,CAAAQ,SAAA,CAAA2H,MAAA,aAAOnI,CAAA,EAAWE,CAAA,EAAYC,CAAA;MAC5B,OAAO,KAAKyG,SAAA,CAAUxD,CAAA,CAAuB2C,MAAA,CAAO/F,CAAA,EAAGE,CAAA,EAAGC,CAAA;IAAA,GAG5DH,CAAA,CAAAQ,SAAA,CAAA4H,MAAA,aAAOpI,CAAA,EAAWE,CAAA,EAAWC,CAAA,EAAWU,CAAA,EAAWiC,CAAA,EAAWE,CAAA;MAC5D,OAAO,KAAK4D,SAAA,CAAUxD,CAAA,CAAuB0C,MAAA,CAAO9F,CAAA,EAAGE,CAAA,EAAGC,CAAA,EAAGU,CAAA,EAAGiC,CAAA,EAAGE,CAAA;IAAA,GAGrEhD,CAAA,CAAAQ,SAAA,CAAA6H,KAAA,aAAMrI,CAAA;MACJ,OAAO,KAAK4G,SAAA,CAAUxD,CAAA,CAAuB8C,MAAA,CAAOlG,CAAA;IAAA,GAGtDA,CAAA,CAAAQ,SAAA,CAAA8H,KAAA,aAAMtI,CAAA;MACJ,OAAO,KAAK4G,SAAA,CAAUxD,CAAA,CAAuBgD,MAAA,CAAOpG,CAAA;IAAA,GAGtDA,CAAA,CAAAQ,SAAA,CAAA+H,SAAA,aAAUvI,CAAA;MACR,OAAO,KAAK4G,SAAA,CAAUxD,CAAA,CAAuBiD,eAAA,CAAgBrG,CAAA;IAAA,GAG/DA,CAAA,CAAAQ,SAAA,CAAAgI,SAAA,aAAUxI,CAAA;MACR,OAAO,KAAK4G,SAAA,CAAUxD,CAAA,CAAuBkD,eAAA,CAAgBtG,CAAA;IAAA,GAG/DA,CAAA,CAAAQ,SAAA,CAAAiI,YAAA;MACE,OAAO,KAAK7B,SAAA,CAAUxD,CAAA,CAAuByD,aAAA;IAAA,GAAA7G,CAAA;EAAA;EC/D3C+D,CAAA,GAAe,SAAA2E,CAAC1I,CAAA;IACpB,eAAQA,CAAA,IAAK,SAASA,CAAA,IAAK,SAASA,CAAA,IAAK,SAASA,CAAA;EAAA;EAC9CgE,CAAA,GAAU,SAAA2E,CAAC3I,CAAA;IACf,WAAI4I,UAAA,CAAW,MAAM5I,CAAA,CAAE4I,UAAA,CAAW,MAAM5I,CAAA,CAAE4I,UAAA,CAAW,MAAM,IAAIA,UAAA,CAAW;EAAA;EAAApE,CAAA,aAAAxE,CAAA;IAa1E,SAAAG,EAAA;MAAA,IAAAD,CAAA,GACEF,CAAA,CAAAU,IAAA;MAAA,OAVMR,CAAA,CAAA2I,SAAA,GAAoB,IACpB3I,CAAA,CAAA4I,cAAA,IAA2C,GAC3C5I,CAAA,CAAA6I,kBAAA,IAAqB,GACrB7I,CAAA,CAAA8I,sBAAA,IAAyB,GACzB9I,CAAA,CAAA+I,eAAA,IAAkB,GAClB/I,CAAA,CAAAgJ,qBAAA,IAAwB,GACxBhJ,CAAA,CAAAiJ,mBAAA,IAAsB,GACtBjJ,CAAA,CAAAkJ,OAAA,GAAoB,IAAAlJ,CAAA;IAAA;IA6Q9B,OArRuCA,CAAA,CAAAC,CAAA,EAAAH,CAAA,GAcrCG,CAAA,CAAAK,SAAA,CAAA6I,MAAA,aAAOrJ,CAAA;MAGL,eAAAA,CAAA,KAHKA,CAAA,QACL,KAAKsJ,KAAA,CAAM,KAAKtJ,CAAA,GAEZ,MAAM,KAAKoJ,OAAA,CAAQnI,MAAA,KAAW,KAAK+H,sBAAA,EACrC,MAAM,IAAIO,WAAA,CAAY;MAExB,OAAOvJ,CAAA;IAAA,GAGTG,CAAA,CAAAK,SAAA,CAAA8I,KAAA,aAAMtJ,CAAA,EAAaE,CAAA;MAAnB,IAAAC,CAAA;MAAA,WAAAD,CAAA,KAAmBA,CAAA;MAOjB,KANA,IAAMW,CAAA,GAAgB,SAAA2I,CAACxJ,CAAA;UACrBE,CAAA,CAASuJ,IAAA,CAAKzJ,CAAA,GACdG,CAAA,CAAKiJ,OAAA,CAAQnI,MAAA,GAAS,GACtBd,CAAA,CAAK6I,sBAAA,IAAyB;QAAA,GAGvBlG,CAAA,GAAI,GAAGA,CAAA,GAAI9C,CAAA,CAAIiB,MAAA,EAAQ6B,CAAA,IAAK;QACnC,IAAME,CAAA,GAAIhD,CAAA,CAAI8C,CAAA;UAERI,CAAA,KAAa,KAAK4F,cAAA,KAAmB3H,CAAA,CAAYiB,GAAA,IAC5B,MAAxB,KAAKgH,OAAA,CAAQnI,MAAA,IAAwC,MAAxB,KAAKmI,OAAA,CAAQnI,MAAA,IACjB,MAA1B,KAAK4H,SAAA,CAAU5H,MAAA,IACK,QAAnB,KAAK4H,SAAA,IAAwC,QAAnB,KAAKA,SAAA;UAC5B1F,CAAA,GAAgBa,CAAA,CAAQhB,CAAA,MACR,QAAnB,KAAK6F,SAAA,IAA2B,QAAN7F,CAAA,IAC3BE,CAAA;QAGF,KACEc,CAAA,CAAQhB,CAAA,KACPG,CAAA;UAMH,IAAI,QAAQH,CAAA,IAAK,QAAQA,CAAA;YAKzB,IACG,QAAQA,CAAA,IAAK,QAAQA,CAAA,KACtB,KAAKiG,eAAA,IACJ,KAAKC,qBAAA;cAMR,IAAI,QAAQlG,CAAA,IAAM,KAAKiG,eAAA,IAAoB,KAAKE,mBAAA,IAAwBjG,CAAA,EAAxE;gBAOA,IAAI,KAAK2F,SAAA,KAAc,MAAM,KAAKC,cAAA,EAAgB;kBAChD,IAAM1F,CAAA,GAAMsG,MAAA,CAAO,KAAKb,SAAA;kBACxB,IAAItE,KAAA,CAAMnB,CAAA,GACR,MAAM,IAAImG,WAAA,CAAY,8BAA4BzG,CAAA;kBAEpD,IAAI,KAAKgG,cAAA,KAAmB3H,CAAA,CAAYiB,GAAA,EACtC,IAAI,MAAM,KAAKgH,OAAA,CAAQnI,MAAA,IAAU,MAAM,KAAKmI,OAAA,CAAQnI,MAAA;oBAClD,IAAI,IAAImC,CAAA,EACN,MAAM,IAAImG,WAAA,CACR,oCAAkCnG,CAAA,oBAAkBN,CAAA;kBAAA,OAGnD,KAAI,MAAM,KAAKsG,OAAA,CAAQnI,MAAA,IAAU,MAAM,KAAKmI,OAAA,CAAQnI,MAAA,KACrD,QAAQ,KAAK4H,SAAA,IAAa,QAAQ,KAAKA,SAAA,EACzC,MAAM,IAAIU,WAAA,CACR,2BAAyB,KAAKV,SAAA,oBAAwB/F,CAAA;kBAK9D,KAAKsG,OAAA,CAAQK,IAAA,CAAKrG,CAAA,GACd,KAAKgG,OAAA,CAAQnI,MAAA,KAAWwD,CAAA,CAAmB,KAAKqE,cAAA,MAC9C3H,CAAA,CAAYE,aAAA,KAAkB,KAAKyH,cAAA,GACrCjI,CAAA,CAAc;oBACZK,IAAA,EAAMC,CAAA,CAAYE,aAAA;oBAClBC,QAAA,EAAU,KAAKyH,kBAAA;oBACfxH,CAAA,EAAG6B;kBAAA,KAEIjC,CAAA,CAAYK,YAAA,KAAiB,KAAKsH,cAAA,GAC3CjI,CAAA,CAAc;oBACZK,IAAA,EAAMC,CAAA,CAAYK,YAAA;oBAClBF,QAAA,EAAU,KAAKyH,kBAAA;oBACftH,CAAA,EAAG2B;kBAAA,KAIL,KAAK0F,cAAA,KAAmB3H,CAAA,CAAYO,OAAA,IACpC,KAAKoH,cAAA,KAAmB3H,CAAA,CAAYQ,OAAA,IACpC,KAAKmH,cAAA,KAAmB3H,CAAA,CAAYgB,cAAA,IAEpCtB,CAAA,CAAc;oBACZK,IAAA,EAAM,KAAK4H,cAAA;oBACXxH,QAAA,EAAU,KAAKyH,kBAAA;oBACfxH,CAAA,EAAG,KAAK6H,OAAA,CAAQ;oBAChB3H,CAAA,EAAG,KAAK2H,OAAA,CAAQ;kBAAA,IAGdjI,CAAA,CAAYO,OAAA,KAAY,KAAKoH,cAAA,KAC/B,KAAKA,cAAA,GAAiB3H,CAAA,CAAYQ,OAAA,KAE3B,KAAKmH,cAAA,KAAmB3H,CAAA,CAAYS,QAAA,GAC7Cf,CAAA,CAAc;oBACZK,IAAA,EAAMC,CAAA,CAAYS,QAAA;oBAClBN,QAAA,EAAU,KAAKyH,kBAAA;oBACflH,EAAA,EAAI,KAAKuH,OAAA,CAAQ;oBACjBtH,EAAA,EAAI,KAAKsH,OAAA,CAAQ;oBACjBrH,EAAA,EAAI,KAAKqH,OAAA,CAAQ;oBACjBpH,EAAA,EAAI,KAAKoH,OAAA,CAAQ;oBACjB7H,CAAA,EAAG,KAAK6H,OAAA,CAAQ;oBAChB3H,CAAA,EAAG,KAAK2H,OAAA,CAAQ;kBAAA,KAET,KAAKN,cAAA,KAAmB3H,CAAA,CAAYc,eAAA,GAC7CpB,CAAA,CAAc;oBACZK,IAAA,EAAMC,CAAA,CAAYc,eAAA;oBAClBX,QAAA,EAAU,KAAKyH,kBAAA;oBACfhH,EAAA,EAAI,KAAKqH,OAAA,CAAQ;oBACjBpH,EAAA,EAAI,KAAKoH,OAAA,CAAQ;oBACjB7H,CAAA,EAAG,KAAK6H,OAAA,CAAQ;oBAChB3H,CAAA,EAAG,KAAK2H,OAAA,CAAQ;kBAAA,KAET,KAAKN,cAAA,KAAmB3H,CAAA,CAAYe,OAAA,GAC7CrB,CAAA,CAAc;oBACZK,IAAA,EAAMC,CAAA,CAAYe,OAAA;oBAClBZ,QAAA,EAAU,KAAKyH,kBAAA;oBACflH,EAAA,EAAI,KAAKuH,OAAA,CAAQ;oBACjBtH,EAAA,EAAI,KAAKsH,OAAA,CAAQ;oBACjB7H,CAAA,EAAG,KAAK6H,OAAA,CAAQ;oBAChB3H,CAAA,EAAG,KAAK2H,OAAA,CAAQ;kBAAA,KAET,KAAKN,cAAA,KAAmB3H,CAAA,CAAYiB,GAAA,IAC7CvB,CAAA,CAAc;oBACZK,IAAA,EAAMC,CAAA,CAAYiB,GAAA;oBAClBd,QAAA,EAAU,KAAKyH,kBAAA;oBACfzG,EAAA,EAAI,KAAK8G,OAAA,CAAQ;oBACjB7G,EAAA,EAAI,KAAK6G,OAAA,CAAQ;oBACjB5G,IAAA,EAAM,KAAK4G,OAAA,CAAQ;oBACnB3G,QAAA,EAAU,KAAK2G,OAAA,CAAQ;oBACvB1G,SAAA,EAAW,KAAK0G,OAAA,CAAQ;oBACxB7H,CAAA,EAAG,KAAK6H,OAAA,CAAQ;oBAChB3H,CAAA,EAAG,KAAK2H,OAAA,CAAQ;kBAAA,KAItB,KAAKP,SAAA,GAAY,IACjB,KAAKK,qBAAA,IAAwB,GAC7B,KAAKD,eAAA,IAAkB,GACvB,KAAKE,mBAAA,IAAsB,GAC3B,KAAKH,sBAAA,IAAyB;gBAAA;gBAGhC,KAAIjF,CAAA,CAAaf,CAAA,GAGjB,IAAI,QAAQA,CAAA,IAAK,KAAKgG,sBAAA,EAEpB,KAAKA,sBAAA,IAAyB,OAIhC,IAAI,QAAQhG,CAAA,IAAK,QAAQA,CAAA,IAAK,QAAQA,CAAA;kBAMtC,IAAIG,CAAA,EACF,KAAK0F,SAAA,GAAY7F,CAAA,EACjB,KAAKmG,mBAAA,IAAsB,OAF7B;oBAOA,IAAI,MAAM,KAAKC,OAAA,CAAQnI,MAAA,EACrB,MAAM,IAAIsI,WAAA,CAAY,mCAAiCzG,CAAA;oBAEzD,KAAK,KAAKkG,sBAAA,EACR,MAAM,IAAIO,WAAA,CACR,2BAAyBvG,CAAA,mBAAeF,CAAA;oBAK5C,IAFA,KAAKkG,sBAAA,IAAyB,GAE1B,QAAQhG,CAAA,IAAK,QAAQA,CAAA;sBAQlB,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAC9B,KAAK8F,cAAA,GAAiB3H,CAAA,CAAYE,aAAA,EAClC,KAAK0H,kBAAA,GAAqB,QAAQ/F,CAAA,MAE7B,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAC9B,KAAK8F,cAAA,GAAiB3H,CAAA,CAAYK,YAAA,EAClC,KAAKuH,kBAAA,GAAqB,QAAQ/F,CAAA,MAE7B,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAC9B,KAAK8F,cAAA,GAAiB3H,CAAA,CAAYO,OAAA,EAClC,KAAKqH,kBAAA,GAAqB,QAAQ/F,CAAA,MAE7B,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAC9B,KAAK8F,cAAA,GAAiB3H,CAAA,CAAYQ,OAAA,EAClC,KAAKoH,kBAAA,GAAqB,QAAQ/F,CAAA,MAE7B,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAC9B,KAAK8F,cAAA,GAAiB3H,CAAA,CAAYS,QAAA,EAClC,KAAKmH,kBAAA,GAAqB,QAAQ/F,CAAA,MAE7B,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAC9B,KAAK8F,cAAA,GAAiB3H,CAAA,CAAYc,eAAA,EAClC,KAAK8G,kBAAA,GAAqB,QAAQ/F,CAAA,MAE7B,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAC9B,KAAK8F,cAAA,GAAiB3H,CAAA,CAAYe,OAAA,EAClC,KAAK6G,kBAAA,GAAqB,QAAQ/F,CAAA,MAE7B,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAC9B,KAAK8F,cAAA,GAAiB3H,CAAA,CAAYgB,cAAA,EAClC,KAAK4G,kBAAA,GAAqB,QAAQ/F,CAAA,MAE7B;wBAAA,IAAI,QAAQA,CAAA,IAAK,QAAQA,CAAA,EAI9B,MAAM,IAAIuG,WAAA,CAAY,2BAAyBvG,CAAA,mBAAeF,CAAA;wBAH9D,KAAKgG,cAAA,GAAiB3H,CAAA,CAAYiB,GAAA,EAClC,KAAK2G,kBAAA,GAAqB,QAAQ/F,CAAA;sBAAA;oBAAA,OAzClC9C,CAAA,CAASuJ,IAAA,CAAK;sBACZvI,IAAA,EAAMC,CAAA,CAAYC;oBAAA,IAEpB,KAAK4H,sBAAA,IAAyB,GAC9B,KAAKF,cAAA,IAAkB;kBAAA;gBAAA,OA3BvB,KAAKD,SAAA,GAAY7F,CAAA,EACjB,KAAKmG,mBAAA,GAAsB,QAAQnG,CAAA;cAAA,OArHnC,KAAK6F,SAAA,IAAa7F,CAAA,EAClB,KAAKmG,mBAAA,IAAsB;YAAA,OAN3B,KAAKN,SAAA,IAAa7F,CAAA;UAAA,OATlB,KAAK6F,SAAA,IAAa7F,CAAA,EAClB,KAAKiG,eAAA,IAAkB;QAAA,OANvB,KAAKJ,SAAA,IAAa7F,CAAA,EAClB,KAAKkG,qBAAA,GAAwB,KAAKD,eAAA;MAAA;MA2MtC,OAAO/I,CAAA;IAAA,GAKTC,CAAA,CAAAK,SAAA,CAAAoG,SAAA,aAAU5G,CAAA;MAoBR,OAnBeI,MAAA,CAAOW,MAAA,CAAO,MAAM;QACjCuI,KAAA,EAAO;UACLK,KAAA,WAAAA,CAAMzJ,CAAA,EAAeC,CAAA;YAAA,WAAAA,CAAA,KAAAA,CAAA;YAKnB,KAJA,IAAAU,CAAA,MAIgBiC,CAAA,GAJO1C,MAAA,CAAOwJ,cAAA,CAAe,MAAMN,KAAA,CAAM5I,IAAA,CACvD,MACAR,CAAA,GAEcW,CAAA,GAAAiC,CAAA,CAAA7B,MAAA,EAAAJ,CAAA,IAAgB;cAA3B,IAAMmC,CAAA,GAAAF,CAAA,CAAAjC,CAAA;gBACHqC,CAAA,GAAKlD,CAAA,CAAUgD,CAAA;cACjBzC,KAAA,CAAMS,OAAA,CAAQkC,CAAA,IAChB/C,CAAA,CAASsJ,IAAA,CAAAI,KAAA,CAAT1J,CAAA,EAAiB+C,CAAA,IAEjB/C,CAAA,CAASsJ,IAAA,CAAKvG,CAAA;YAAA;YAGlB,OAAO/C,CAAA;UAAA;QAAA;MAAA;IAAA,GAAAA,CAAA;EAAA,EA/QsB2D,CAAA;EAAA3C,CAAA,aAAAnB,CAAA;ICJrC,SAAAa,EAAYX,CAAA;MAAZ,IAAAC,CAAA,GACEH,CAAA,CAAAU,IAAA;MAAA,OAEEP,CAAA,CAAK2J,QAAA,GADH,mBAAoB5J,CAAA,GACNW,CAAA,CAAYyI,KAAA,CAAMpJ,CAAA,IAElBA,CAAA,EAAAC,CAAA;IAAA;IA2DtB,OAlEiCD,CAAA,CAAAW,CAAA,EAAAb,CAAA,GAW/Ba,CAAA,CAAAL,SAAA,CAAAuJ,MAAA;MACE,OAAOlJ,CAAA,CAAYkJ,MAAA,CAAO,KAAKD,QAAA;IAAA,GAGjCjJ,CAAA,CAAAL,SAAA,CAAAwJ,SAAA;MACE,IAAMhK,CAAA,GAAkBoD,CAAA,CAAuB2D,gBAAA;MAG/C,OADA,KAAKH,SAAA,CAAU5G,CAAA,GACRA,CAAA;IAAA,GAGTa,CAAA,CAAAL,SAAA,CAAAoG,SAAA,aACE5G,CAAA;MAIA,KAFA,IAAME,CAAA,GAAc,IAAAC,CAAA,MAEEU,CAAA,QAAKiJ,QAAA,EAAL3J,CAAA,GAAAU,CAAA,CAAAI,MAAA,EAAAd,CAAA,IAAe;QAAhC,IACG2C,CAAA,GAAqB9C,CAAA,CAAAa,CAAA,CAAAV,CAAA;QAEvBI,KAAA,CAAMS,OAAA,CAAQ8B,CAAA,IAChB5C,CAAA,CAAYuJ,IAAA,CAAAI,KAAA,CAAZ3J,CAAA,EAAoB4C,CAAA,IAEpB5C,CAAA,CAAYuJ,IAAA,CAAK3G,CAAA;MAAA;MAIrB,OADA,KAAKgH,QAAA,GAAW5J,CAAA,EACT;IAAA,GAGFW,CAAA,CAAAkJ,MAAA,GAAP,UAAc/J,CAAA;MACZ,OAAOG,CAAA,CAAcH,CAAA;IAAA,GAGhBa,CAAA,CAAAyI,KAAA,GAAP,UAAatJ,CAAA;MACX,IAAME,CAAA,GAAS,IAAIsE,CAAA;QACbrE,CAAA,GAAyB;MAG/B,OAFAD,CAAA,CAAOoJ,KAAA,CAAMtJ,CAAA,EAAMG,CAAA,GACnBD,CAAA,CAAOmJ,MAAA,CAAOlJ,CAAA,GACPA,CAAA;IAAA,GAGOU,CAAA,CAAAO,UAAA,GAAgB,GAChBP,CAAA,CAAAa,OAAA,GAAa,GACbb,CAAA,CAAAQ,aAAA,GAAmB,GACnBR,CAAA,CAAAW,YAAA,GAAkB,GAClBX,CAAA,CAAAc,OAAA,GAAc,IACdd,CAAA,CAAAe,QAAA,GAAe,IACff,CAAA,CAAAoB,eAAA,GAAsB,IACtBpB,CAAA,CAAAqB,OAAA,GAAe,KACfrB,CAAA,CAAAsB,cAAA,GAAsB,KACtBtB,CAAA,CAAAuB,GAAA,GAAW,KACXvB,CAAA,CAAAgF,aAAA,GAAgBhF,CAAA,CAAYc,OAAA,GAAUd,CAAA,CAAYQ,aAAA,GAAgBR,CAAA,CAAYW,YAAA,EAC9EX,CAAA,CAAAwG,gBAAA,GAAmBxG,CAAA,CAAYQ,aAAA,GAAgBR,CAAA,CAAYW,YAAA,GAAeX,CAAA,CAAYc,OAAA,GACtGd,CAAA,CAAYe,QAAA,GAAWf,CAAA,CAAYoB,eAAA,GAAkBpB,CAAA,CAAYqB,OAAA,GACjErB,CAAA,CAAYsB,cAAA,GAAiBtB,CAAA,CAAYuB,GAAA,EAAAvB,CAAA;EAAA,EAjEViD,CAAA;EAoEpBW,CAAA,KAAAb,CAAA,OACRzC,CAAA,CAAYO,OAAA,IAAU,GACvBkC,CAAA,CAACzC,CAAA,CAAYQ,OAAA,IAAU,GACvBiC,CAAA,CAACzC,CAAA,CAAYE,aAAA,IAAgB,GAC7BuC,CAAA,CAACzC,CAAA,CAAYK,YAAA,IAAe,GAC5BoC,CAAA,CAACzC,CAAA,CAAYC,UAAA,IAAa,GAC1BwC,CAAA,CAACzC,CAAA,CAAYe,OAAA,IAAU,GACvB0B,CAAA,CAACzC,CAAA,CAAYgB,cAAA,IAAiB,GAC9ByB,CAAA,CAACzC,CAAA,CAAYS,QAAA,IAAW,GACxBgC,CAAA,CAACzC,CAAA,CAAYc,eAAA,IAAkB,GAC/B2B,CAAA,CAACzC,CAAA,CAAYiB,GAAA,IAAM,GAAAwB,CAAA;AAAA,SAAAa,CAAA,IAAAwF,kBAAA,EAAA9I,CAAA,IAAA+I,WAAA,EAAA1F,CAAA,IAAA2F,iBAAA,EAAA/G,CAAA,IAAAgH,sBAAA,EAAAjK,CAAA,IAAAkK,aAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}