{"ast": null, "code": "'use strict';\n\nvar isObject = require('../internals/is-object');\nmodule.exports = function (argument) {\n  return isObject(argument) || argument === null;\n};", "map": {"version": 3, "names": ["isObject", "require", "module", "exports", "argument"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/is-possible-prototype.js"], "sourcesContent": ["'use strict';\nvar isObject = require('../internals/is-object');\n\nmodule.exports = function (argument) {\n  return isObject(argument) || argument === null;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAEhDC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,OAAOJ,QAAQ,CAACI,QAAQ,CAAC,IAAIA,QAAQ,KAAK,IAAI;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}