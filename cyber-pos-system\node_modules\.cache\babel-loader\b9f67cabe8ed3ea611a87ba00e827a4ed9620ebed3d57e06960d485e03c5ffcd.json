{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar fails = require('../internals/fails');\n\n// babel-minify and Closure Compiler transpiles RegExp('.', 'd') -> /./d and it causes SyntaxError\nvar RegExp = globalThis.RegExp;\nvar FLAGS_GETTER_IS_CORRECT = !fails(function () {\n  var INDICES_SUPPORT = true;\n  try {\n    RegExp('.', 'd');\n  } catch (error) {\n    INDICES_SUPPORT = false;\n  }\n  var O = {};\n  // modern V8 bug\n  var calls = '';\n  var expected = INDICES_SUPPORT ? 'dgimsy' : 'gimsy';\n  var addGetter = function (key, chr) {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty(O, key, {\n      get: function () {\n        calls += chr;\n        return true;\n      }\n    });\n  };\n  var pairs = {\n    dotAll: 's',\n    global: 'g',\n    ignoreCase: 'i',\n    multiline: 'm',\n    sticky: 'y'\n  };\n  if (INDICES_SUPPORT) pairs.hasIndices = 'd';\n  for (var key in pairs) addGetter(key, pairs[key]);\n\n  // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n  var result = Object.getOwnPropertyDescriptor(RegExp.prototype, 'flags').get.call(O);\n  return result !== expected || calls !== expected;\n});\nmodule.exports = {\n  correct: FLAGS_GETTER_IS_CORRECT\n};", "map": {"version": 3, "names": ["globalThis", "require", "fails", "RegExp", "FLAGS_GETTER_IS_CORRECT", "INDICES_SUPPORT", "error", "O", "calls", "expected", "addGetter", "key", "chr", "Object", "defineProperty", "get", "pairs", "dotAll", "global", "ignoreCase", "multiline", "sticky", "hasIndices", "result", "getOwnPropertyDescriptor", "prototype", "call", "module", "exports", "correct"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/regexp-flags-detection.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar fails = require('../internals/fails');\n\n// babel-minify and Closure Compiler transpiles RegExp('.', 'd') -> /./d and it causes SyntaxError\nvar RegExp = globalThis.RegExp;\n\nvar FLAGS_GETTER_IS_CORRECT = !fails(function () {\n  var INDICES_SUPPORT = true;\n  try {\n    RegExp('.', 'd');\n  } catch (error) {\n    INDICES_SUPPORT = false;\n  }\n\n  var O = {};\n  // modern V8 bug\n  var calls = '';\n  var expected = INDICES_SUPPORT ? 'dgimsy' : 'gimsy';\n\n  var addGetter = function (key, chr) {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty(O, key, { get: function () {\n      calls += chr;\n      return true;\n    } });\n  };\n\n  var pairs = {\n    dotAll: 's',\n    global: 'g',\n    ignoreCase: 'i',\n    multiline: 'm',\n    sticky: 'y'\n  };\n\n  if (INDICES_SUPPORT) pairs.hasIndices = 'd';\n\n  for (var key in pairs) addGetter(key, pairs[key]);\n\n  // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n  var result = Object.getOwnPropertyDescriptor(RegExp.prototype, 'flags').get.call(O);\n\n  return result !== expected || calls !== expected;\n});\n\nmodule.exports = { correct: FLAGS_GETTER_IS_CORRECT };\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAoB,CAAC;;AAEzC;AACA,IAAIE,MAAM,GAAGH,UAAU,CAACG,MAAM;AAE9B,IAAIC,uBAAuB,GAAG,CAACF,KAAK,CAAC,YAAY;EAC/C,IAAIG,eAAe,GAAG,IAAI;EAC1B,IAAI;IACFF,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;EAClB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdD,eAAe,GAAG,KAAK;EACzB;EAEA,IAAIE,CAAC,GAAG,CAAC,CAAC;EACV;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,QAAQ,GAAGJ,eAAe,GAAG,QAAQ,GAAG,OAAO;EAEnD,IAAIK,SAAS,GAAG,SAAAA,CAAUC,GAAG,EAAEC,GAAG,EAAE;IAClC;IACAC,MAAM,CAACC,cAAc,CAACP,CAAC,EAAEI,GAAG,EAAE;MAAEI,GAAG,EAAE,SAAAA,CAAA,EAAY;QAC/CP,KAAK,IAAII,GAAG;QACZ,OAAO,IAAI;MACb;IAAE,CAAC,CAAC;EACN,CAAC;EAED,IAAII,KAAK,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,GAAG;IACdC,MAAM,EAAE;EACV,CAAC;EAED,IAAIhB,eAAe,EAAEW,KAAK,CAACM,UAAU,GAAG,GAAG;EAE3C,KAAK,IAAIX,GAAG,IAAIK,KAAK,EAAEN,SAAS,CAACC,GAAG,EAAEK,KAAK,CAACL,GAAG,CAAC,CAAC;;EAEjD;EACA,IAAIY,MAAM,GAAGV,MAAM,CAACW,wBAAwB,CAACrB,MAAM,CAACsB,SAAS,EAAE,OAAO,CAAC,CAACV,GAAG,CAACW,IAAI,CAACnB,CAAC,CAAC;EAEnF,OAAOgB,MAAM,KAAKd,QAAQ,IAAID,KAAK,KAAKC,QAAQ;AAClD,CAAC,CAAC;AAEFkB,MAAM,CAACC,OAAO,GAAG;EAAEC,OAAO,EAAEzB;AAAwB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}