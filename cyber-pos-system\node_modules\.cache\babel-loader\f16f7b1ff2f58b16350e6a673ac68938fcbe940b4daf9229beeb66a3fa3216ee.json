{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\nvar floor = Math.floor;\nvar charAt = uncurryThis(''.charAt);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\n// eslint-disable-next-line redos/no-vulnerable -- safe\nvar SUBSTITUTION_SYMBOLS = /\\$([$&'`]|\\d{1,2}|<[^>]*>)/g;\nvar SUBSTITUTION_SYMBOLS_NO_NAMED = /\\$([$&'`]|\\d{1,2})/g;\n\n// `GetSubstitution` abstract operation\n// https://tc39.es/ecma262/#sec-getsubstitution\nmodule.exports = function (matched, str, position, captures, namedCaptures, replacement) {\n  var tailPos = position + matched.length;\n  var m = captures.length;\n  var symbols = SUBSTITUTION_SYMBOLS_NO_NAMED;\n  if (namedCaptures !== undefined) {\n    namedCaptures = toObject(namedCaptures);\n    symbols = SUBSTITUTION_SYMBOLS;\n  }\n  return replace(replacement, symbols, function (match, ch) {\n    var capture;\n    switch (charAt(ch, 0)) {\n      case '$':\n        return '$';\n      case '&':\n        return matched;\n      case '`':\n        return stringSlice(str, 0, position);\n      case \"'\":\n        return stringSlice(str, tailPos);\n      case '<':\n        capture = namedCaptures[stringSlice(ch, 1, -1)];\n        break;\n      default:\n        // \\d\\d?\n        var n = +ch;\n        if (n === 0) return match;\n        if (n > m) {\n          var f = floor(n / 10);\n          if (f === 0) return match;\n          if (f <= m) return captures[f - 1] === undefined ? charAt(ch, 1) : captures[f - 1] + charAt(ch, 1);\n          return match;\n        }\n        capture = captures[n - 1];\n    }\n    return capture === undefined ? '' : capture;\n  });\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "toObject", "floor", "Math", "char<PERSON>t", "replace", "stringSlice", "slice", "SUBSTITUTION_SYMBOLS", "SUBSTITUTION_SYMBOLS_NO_NAMED", "module", "exports", "matched", "str", "position", "captures", "namedCaptures", "replacement", "tailPos", "length", "m", "symbols", "undefined", "match", "ch", "capture", "n", "f"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/get-substitution.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar floor = Math.floor;\nvar charAt = uncurryThis(''.charAt);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\n// eslint-disable-next-line redos/no-vulnerable -- safe\nvar SUBSTITUTION_SYMBOLS = /\\$([$&'`]|\\d{1,2}|<[^>]*>)/g;\nvar SUBSTITUTION_SYMBOLS_NO_NAMED = /\\$([$&'`]|\\d{1,2})/g;\n\n// `GetSubstitution` abstract operation\n// https://tc39.es/ecma262/#sec-getsubstitution\nmodule.exports = function (matched, str, position, captures, namedCaptures, replacement) {\n  var tailPos = position + matched.length;\n  var m = captures.length;\n  var symbols = SUBSTITUTION_SYMBOLS_NO_NAMED;\n  if (namedCaptures !== undefined) {\n    namedCaptures = toObject(namedCaptures);\n    symbols = SUBSTITUTION_SYMBOLS;\n  }\n  return replace(replacement, symbols, function (match, ch) {\n    var capture;\n    switch (charAt(ch, 0)) {\n      case '$': return '$';\n      case '&': return matched;\n      case '`': return stringSlice(str, 0, position);\n      case \"'\": return stringSlice(str, tailPos);\n      case '<':\n        capture = namedCaptures[stringSlice(ch, 1, -1)];\n        break;\n      default: // \\d\\d?\n        var n = +ch;\n        if (n === 0) return match;\n        if (n > m) {\n          var f = floor(n / 10);\n          if (f === 0) return match;\n          if (f <= m) return captures[f - 1] === undefined ? charAt(ch, 1) : captures[f - 1] + charAt(ch, 1);\n          return match;\n        }\n        capture = captures[n - 1];\n    }\n    return capture === undefined ? '' : capture;\n  });\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAEhD,IAAIE,KAAK,GAAGC,IAAI,CAACD,KAAK;AACtB,IAAIE,MAAM,GAAGL,WAAW,CAAC,EAAE,CAACK,MAAM,CAAC;AACnC,IAAIC,OAAO,GAAGN,WAAW,CAAC,EAAE,CAACM,OAAO,CAAC;AACrC,IAAIC,WAAW,GAAGP,WAAW,CAAC,EAAE,CAACQ,KAAK,CAAC;AACvC;AACA,IAAIC,oBAAoB,GAAG,6BAA6B;AACxD,IAAIC,6BAA6B,GAAG,qBAAqB;;AAEzD;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,WAAW,EAAE;EACvF,IAAIC,OAAO,GAAGJ,QAAQ,GAAGF,OAAO,CAACO,MAAM;EACvC,IAAIC,CAAC,GAAGL,QAAQ,CAACI,MAAM;EACvB,IAAIE,OAAO,GAAGZ,6BAA6B;EAC3C,IAAIO,aAAa,KAAKM,SAAS,EAAE;IAC/BN,aAAa,GAAGf,QAAQ,CAACe,aAAa,CAAC;IACvCK,OAAO,GAAGb,oBAAoB;EAChC;EACA,OAAOH,OAAO,CAACY,WAAW,EAAEI,OAAO,EAAE,UAAUE,KAAK,EAAEC,EAAE,EAAE;IACxD,IAAIC,OAAO;IACX,QAAQrB,MAAM,CAACoB,EAAE,EAAE,CAAC,CAAC;MACnB,KAAK,GAAG;QAAE,OAAO,GAAG;MACpB,KAAK,GAAG;QAAE,OAAOZ,OAAO;MACxB,KAAK,GAAG;QAAE,OAAON,WAAW,CAACO,GAAG,EAAE,CAAC,EAAEC,QAAQ,CAAC;MAC9C,KAAK,GAAG;QAAE,OAAOR,WAAW,CAACO,GAAG,EAAEK,OAAO,CAAC;MAC1C,KAAK,GAAG;QACNO,OAAO,GAAGT,aAAa,CAACV,WAAW,CAACkB,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C;MACF;QAAS;QACP,IAAIE,CAAC,GAAG,CAACF,EAAE;QACX,IAAIE,CAAC,KAAK,CAAC,EAAE,OAAOH,KAAK;QACzB,IAAIG,CAAC,GAAGN,CAAC,EAAE;UACT,IAAIO,CAAC,GAAGzB,KAAK,CAACwB,CAAC,GAAG,EAAE,CAAC;UACrB,IAAIC,CAAC,KAAK,CAAC,EAAE,OAAOJ,KAAK;UACzB,IAAII,CAAC,IAAIP,CAAC,EAAE,OAAOL,QAAQ,CAACY,CAAC,GAAG,CAAC,CAAC,KAAKL,SAAS,GAAGlB,MAAM,CAACoB,EAAE,EAAE,CAAC,CAAC,GAAGT,QAAQ,CAACY,CAAC,GAAG,CAAC,CAAC,GAAGvB,MAAM,CAACoB,EAAE,EAAE,CAAC,CAAC;UAClG,OAAOD,KAAK;QACd;QACAE,OAAO,GAAGV,QAAQ,CAACW,CAAC,GAAG,CAAC,CAAC;IAC7B;IACA,OAAOD,OAAO,KAAKH,SAAS,GAAG,EAAE,GAAGG,OAAO;EAC7C,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}