{"ast": null, "code": "'use strict';\n\nvar classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\n// eslint-disable-next-line es/no-array-isarray -- safe\nmodule.exports = Array.isArray || function isArray(argument) {\n  return classof(argument) === 'Array';\n};", "map": {"version": 3, "names": ["classof", "require", "module", "exports", "Array", "isArray", "argument"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/is-array.js"], "sourcesContent": ["'use strict';\nvar classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\n// eslint-disable-next-line es/no-array-isarray -- safe\nmodule.exports = Array.isArray || function isArray(argument) {\n  return classof(argument) === 'Array';\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,0BAA0B,CAAC;;AAEjD;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAGC,KAAK,CAACC,OAAO,IAAI,SAASA,OAAOA,CAACC,QAAQ,EAAE;EAC3D,OAAON,OAAO,CAACM,QAAQ,CAAC,KAAK,OAAO;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}