{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar toString = require('../internals/to-string');\nvar whitespaces = require('../internals/whitespaces');\nvar replace = uncurryThis(''.replace);\nvar ltrim = RegExp('^[' + whitespaces + ']+');\nvar rtrim = RegExp('(^|[^' + whitespaces + '])[' + whitespaces + ']+$');\n\n// `String.prototype.{ trim, trimStart, trimEnd, trimLeft, trimRight }` methods implementation\nvar createMethod = function (TYPE) {\n  return function ($this) {\n    var string = toString(requireObjectCoercible($this));\n    if (TYPE & 1) string = replace(string, ltrim, '');\n    if (TYPE & 2) string = replace(string, rtrim, '$1');\n    return string;\n  };\n};\nmodule.exports = {\n  // `String.prototype.{ trimLeft, trimStart }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimstart\n  start: createMethod(1),\n  // `String.prototype.{ trimRight, trimEnd }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimend\n  end: createMethod(2),\n  // `String.prototype.trim` method\n  // https://tc39.es/ecma262/#sec-string.prototype.trim\n  trim: createMethod(3)\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "requireObjectCoercible", "toString", "whitespaces", "replace", "ltrim", "RegExp", "rtrim", "createMethod", "TYPE", "$this", "string", "module", "exports", "start", "end", "trim"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/string-trim.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar toString = require('../internals/to-string');\nvar whitespaces = require('../internals/whitespaces');\n\nvar replace = uncurryThis(''.replace);\nvar ltrim = RegExp('^[' + whitespaces + ']+');\nvar rtrim = RegExp('(^|[^' + whitespaces + '])[' + whitespaces + ']+$');\n\n// `String.prototype.{ trim, trimStart, trimEnd, trimLeft, trimRight }` methods implementation\nvar createMethod = function (TYPE) {\n  return function ($this) {\n    var string = toString(requireObjectCoercible($this));\n    if (TYPE & 1) string = replace(string, ltrim, '');\n    if (TYPE & 2) string = replace(string, rtrim, '$1');\n    return string;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.{ trimLeft, trimStart }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimstart\n  start: createMethod(1),\n  // `String.prototype.{ trimRight, trimEnd }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimend\n  end: createMethod(2),\n  // `String.prototype.trim` method\n  // https://tc39.es/ecma262/#sec-string.prototype.trim\n  trim: createMethod(3)\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,uCAAuC,CAAC;AAC7E,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIG,WAAW,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AAErD,IAAII,OAAO,GAAGL,WAAW,CAAC,EAAE,CAACK,OAAO,CAAC;AACrC,IAAIC,KAAK,GAAGC,MAAM,CAAC,IAAI,GAAGH,WAAW,GAAG,IAAI,CAAC;AAC7C,IAAII,KAAK,GAAGD,MAAM,CAAC,OAAO,GAAGH,WAAW,GAAG,KAAK,GAAGA,WAAW,GAAG,KAAK,CAAC;;AAEvE;AACA,IAAIK,YAAY,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACjC,OAAO,UAAUC,KAAK,EAAE;IACtB,IAAIC,MAAM,GAAGT,QAAQ,CAACD,sBAAsB,CAACS,KAAK,CAAC,CAAC;IACpD,IAAID,IAAI,GAAG,CAAC,EAAEE,MAAM,GAAGP,OAAO,CAACO,MAAM,EAAEN,KAAK,EAAE,EAAE,CAAC;IACjD,IAAII,IAAI,GAAG,CAAC,EAAEE,MAAM,GAAGP,OAAO,CAACO,MAAM,EAAEJ,KAAK,EAAE,IAAI,CAAC;IACnD,OAAOI,MAAM;EACf,CAAC;AACH,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG;EACf;EACA;EACAC,KAAK,EAAEN,YAAY,CAAC,CAAC,CAAC;EACtB;EACA;EACAO,GAAG,EAAEP,YAAY,CAAC,CAAC,CAAC;EACpB;EACA;EACAQ,IAAI,EAAER,YAAY,CAAC,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}