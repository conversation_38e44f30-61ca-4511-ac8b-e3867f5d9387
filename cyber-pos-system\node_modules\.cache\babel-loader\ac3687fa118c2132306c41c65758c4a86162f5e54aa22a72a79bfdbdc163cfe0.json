{"ast": null, "code": "'use strict';\n\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar create = require('../internals/object-create');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_PURE = require('../internals/is-pure');\nvar ITERATOR = wellKnownSymbol('iterator');\nvar BUGGY_SAFARI_ITERATORS = false;\n\n// `%IteratorPrototype%` object\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-object\nvar IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;\n\n/* eslint-disable es/no-array-prototype-keys -- safe */\nif ([].keys) {\n  arrayIterator = [].keys();\n  // Safari 8 has buggy iterators w/o `next`\n  if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;else {\n    PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));\n    if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;\n  }\n}\nvar NEW_ITERATOR_PROTOTYPE = !isObject(IteratorPrototype) || fails(function () {\n  var test = {};\n  // FF44- legacy iterators case\n  return IteratorPrototype[ITERATOR].call(test) !== test;\n});\nif (NEW_ITERATOR_PROTOTYPE) IteratorPrototype = {};else if (IS_PURE) IteratorPrototype = create(IteratorPrototype);\n\n// `%IteratorPrototype%[@@iterator]()` method\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-@@iterator\nif (!isCallable(IteratorPrototype[ITERATOR])) {\n  defineBuiltIn(IteratorPrototype, ITERATOR, function () {\n    return this;\n  });\n}\nmodule.exports = {\n  IteratorPrototype: IteratorPrototype,\n  BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS\n};", "map": {"version": 3, "names": ["fails", "require", "isCallable", "isObject", "create", "getPrototypeOf", "defineBuiltIn", "wellKnownSymbol", "IS_PURE", "ITERATOR", "BUGGY_SAFARI_ITERATORS", "IteratorPrototype", "PrototypeOfArrayIteratorPrototype", "arrayIterator", "keys", "Object", "prototype", "NEW_ITERATOR_PROTOTYPE", "test", "call", "module", "exports"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/iterators-core.js"], "sourcesContent": ["'use strict';\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar create = require('../internals/object-create');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_PURE = require('../internals/is-pure');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar BUGGY_SAFARI_ITERATORS = false;\n\n// `%IteratorPrototype%` object\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-object\nvar IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;\n\n/* eslint-disable es/no-array-prototype-keys -- safe */\nif ([].keys) {\n  arrayIterator = [].keys();\n  // Safari 8 has buggy iterators w/o `next`\n  if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;\n  else {\n    PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));\n    if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;\n  }\n}\n\nvar NEW_ITERATOR_PROTOTYPE = !isObject(IteratorPrototype) || fails(function () {\n  var test = {};\n  // FF44- legacy iterators case\n  return IteratorPrototype[ITERATOR].call(test) !== test;\n});\n\nif (NEW_ITERATOR_PROTOTYPE) IteratorPrototype = {};\nelse if (IS_PURE) IteratorPrototype = create(IteratorPrototype);\n\n// `%IteratorPrototype%[@@iterator]()` method\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-@@iterator\nif (!isCallable(IteratorPrototype[ITERATOR])) {\n  defineBuiltIn(IteratorPrototype, ITERATOR, function () {\n    return this;\n  });\n}\n\nmodule.exports = {\n  IteratorPrototype: IteratorPrototype,\n  BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIG,MAAM,GAAGH,OAAO,CAAC,4BAA4B,CAAC;AAClD,IAAII,cAAc,GAAGJ,OAAO,CAAC,sCAAsC,CAAC;AACpE,IAAIK,aAAa,GAAGL,OAAO,CAAC,8BAA8B,CAAC;AAC3D,IAAIM,eAAe,GAAGN,OAAO,CAAC,gCAAgC,CAAC;AAC/D,IAAIO,OAAO,GAAGP,OAAO,CAAC,sBAAsB,CAAC;AAE7C,IAAIQ,QAAQ,GAAGF,eAAe,CAAC,UAAU,CAAC;AAC1C,IAAIG,sBAAsB,GAAG,KAAK;;AAElC;AACA;AACA,IAAIC,iBAAiB,EAAEC,iCAAiC,EAAEC,aAAa;;AAEvE;AACA,IAAI,EAAE,CAACC,IAAI,EAAE;EACXD,aAAa,GAAG,EAAE,CAACC,IAAI,CAAC,CAAC;EACzB;EACA,IAAI,EAAE,MAAM,IAAID,aAAa,CAAC,EAAEH,sBAAsB,GAAG,IAAI,CAAC,KACzD;IACHE,iCAAiC,GAAGP,cAAc,CAACA,cAAc,CAACQ,aAAa,CAAC,CAAC;IACjF,IAAID,iCAAiC,KAAKG,MAAM,CAACC,SAAS,EAAEL,iBAAiB,GAAGC,iCAAiC;EACnH;AACF;AAEA,IAAIK,sBAAsB,GAAG,CAACd,QAAQ,CAACQ,iBAAiB,CAAC,IAAIX,KAAK,CAAC,YAAY;EAC7E,IAAIkB,IAAI,GAAG,CAAC,CAAC;EACb;EACA,OAAOP,iBAAiB,CAACF,QAAQ,CAAC,CAACU,IAAI,CAACD,IAAI,CAAC,KAAKA,IAAI;AACxD,CAAC,CAAC;AAEF,IAAID,sBAAsB,EAAEN,iBAAiB,GAAG,CAAC,CAAC,CAAC,KAC9C,IAAIH,OAAO,EAAEG,iBAAiB,GAAGP,MAAM,CAACO,iBAAiB,CAAC;;AAE/D;AACA;AACA,IAAI,CAACT,UAAU,CAACS,iBAAiB,CAACF,QAAQ,CAAC,CAAC,EAAE;EAC5CH,aAAa,CAACK,iBAAiB,EAAEF,QAAQ,EAAE,YAAY;IACrD,OAAO,IAAI;EACb,CAAC,CAAC;AACJ;AAEAW,MAAM,CAACC,OAAO,GAAG;EACfV,iBAAiB,EAAEA,iBAAiB;EACpCD,sBAAsB,EAAEA;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}