import React from 'react';
import {
  Package,
  TrendingDown,
  AlertTriangle,
  Calendar,
  DollarSign,
  BarChart3,
  Activity,
  Tag
} from 'lucide-react';
import { Product } from '../../types';

interface InventoryStatsProps {
  products: Product[];
}

const InventoryStats: React.FC<InventoryStatsProps> = ({ products }) => {
  // Calculate basic stats
  const totalProducts = products.length;
  const activeProducts = products.filter(p => p.isActive).length;
  const inactiveProducts = totalProducts - activeProducts;
  
  const totalStockValue = products.reduce((sum, product) => 
    sum + (product.price * product.stockQuantity), 0
  );
  
  const averagePrice = products.length > 0 
    ? products.reduce((sum, product) => sum + product.price, 0) / products.length 
    : 0;

  // Stock analysis
  const inStockProducts = products.filter(p => p.stockQuantity > 0).length;
  const outOfStockProducts = products.filter(p => p.stockQuantity === 0).length;
  const lowStockProducts = products.filter(p => 
    p.stockQuantity > 0 && p.stockQuantity <= p.reorderLevel
  ).length;

  // Expiry analysis
  const productsWithExpiry = products.filter(p => p.hasExpiry).length;
  const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  
  const expiringProducts = products.filter(p => 
    p.hasExpiry && p.expiryDate && p.expiryDate <= thirtyDaysFromNow
  ).length;

  // Category analysis
  const categoryStats = products.reduce((acc, product) => {
    if (!acc[product.category]) {
      acc[product.category] = {
        count: 0,
        totalValue: 0,
        totalStock: 0,
        lowStock: 0
      };
    }
    acc[product.category].count++;
    acc[product.category].totalValue += product.price * product.stockQuantity;
    acc[product.category].totalStock += product.stockQuantity;
    if (product.stockQuantity <= product.reorderLevel) {
      acc[product.category].lowStock++;
    }
    return acc;
  }, {} as Record<string, { count: number; totalValue: number; totalStock: number; lowStock: number }>);

  const topCategories = Object.entries(categoryStats)
    .sort(([,a], [,b]) => b.totalValue - a.totalValue)
    .slice(0, 5);

  // Top value products
  const topValueProducts = products
    .map(product => ({
      ...product,
      totalValue: product.price * product.stockQuantity
    }))
    .sort((a, b) => b.totalValue - a.totalValue)
    .slice(0, 5);

  const mainStats = [
    {
      name: 'Total Products',
      value: totalProducts.toString(),
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      name: 'Stock Value',
      value: `KSh ${totalStockValue.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      name: 'Low Stock Items',
      value: lowStockProducts.toString(),
      icon: TrendingDown,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      name: 'Out of Stock',
      value: outOfStockProducts.toString(),
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {mainStats.map((stat) => (
          <div key={stat.name} className="bg-white rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-lg font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Stock Status Breakdown */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Stock Status Overview
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                <span className="text-sm text-gray-600">In Stock</span>
              </div>
              <span className="text-sm font-semibold text-gray-900">{inStockProducts}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                <span className="text-sm text-gray-600">Low Stock</span>
              </div>
              <span className="text-sm font-semibold text-gray-900">{lowStockProducts}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                <span className="text-sm text-gray-600">Out of Stock</span>
              </div>
              <span className="text-sm font-semibold text-gray-900">{outOfStockProducts}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-gray-400 rounded-full mr-3"></div>
                <span className="text-sm text-gray-600">Inactive</span>
              </div>
              <span className="text-sm font-semibold text-gray-900">{inactiveProducts}</span>
            </div>
          </div>
        </div>

        {/* Expiry Analysis */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Expiry Analysis
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Products with Expiry</span>
              <span className="text-sm font-semibold text-gray-900">{productsWithExpiry}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Expiring in 30 Days</span>
              <span className={`text-sm font-semibold ${expiringProducts > 0 ? 'text-orange-600' : 'text-gray-900'}`}>
                {expiringProducts}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">No Expiry Tracking</span>
              <span className="text-sm font-semibold text-gray-900">{totalProducts - productsWithExpiry}</span>
            </div>
            {expiringProducts > 0 && (
              <div className="bg-orange-50 border border-orange-200 rounded-md p-3 mt-3">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 text-orange-600 mr-2" />
                  <span className="text-sm text-orange-700">
                    {expiringProducts} product{expiringProducts > 1 ? 's' : ''} expiring soon
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Top Categories by Value */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Tag className="h-5 w-5 mr-2" />
            Top Categories by Value
          </h3>
          <div className="space-y-3">
            {topCategories.length > 0 ? (
              topCategories.map(([category, stats]) => (
                <div key={category} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <span className="text-sm font-medium text-gray-700">{category}</span>
                    <div className="text-xs text-gray-500">
                      {stats.count} products • {stats.totalStock} units
                      {stats.lowStock > 0 && (
                        <span className="text-orange-600 ml-1">• {stats.lowStock} low stock</span>
                      )}
                    </div>
                  </div>
                  <span className="text-sm font-semibold text-gray-900">
                    KSh {stats.totalValue.toLocaleString()}
                  </span>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">No categories yet</p>
            )}
          </div>
        </div>

        {/* Top Products by Value */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Top Products by Stock Value
          </h3>
          <div className="space-y-3">
            {topValueProducts.length > 0 ? (
              topValueProducts.map((product) => (
                <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <span className="text-sm font-medium text-gray-700">{product.name}</span>
                    <div className="text-xs text-gray-500">
                      {product.stockQuantity} units @ KSh {product.price.toLocaleString()} each
                    </div>
                  </div>
                  <span className="text-sm font-semibold text-gray-900">
                    KSh {product.totalValue.toLocaleString()}
                  </span>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">No products yet</p>
            )}
          </div>
        </div>

        {/* Summary Metrics */}
        <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Financial Summary
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-sm text-gray-600">Total Stock Value</p>
              <p className="text-2xl font-bold text-green-600">
                KSh {totalStockValue.toLocaleString()}
              </p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-600">Average Product Price</p>
              <p className="text-2xl font-bold text-blue-600">
                KSh {averagePrice.toLocaleString()}
              </p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-sm text-gray-600">Categories</p>
              <p className="text-2xl font-bold text-purple-600">
                {Object.keys(categoryStats).length}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InventoryStats;
