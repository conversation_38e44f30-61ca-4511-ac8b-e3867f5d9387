{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\nvar aFunction = function (argument) {\n  return isCallable(argument) ? argument : undefined;\n};\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(globalThis[namespace]) : globalThis[namespace] && globalThis[namespace][method];\n};", "map": {"version": 3, "names": ["globalThis", "require", "isCallable", "aFunction", "argument", "undefined", "module", "exports", "namespace", "method", "arguments", "length"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/get-built-in.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (argument) {\n  return isCallable(argument) ? argument : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(globalThis[namespace]) : globalThis[namespace] && globalThis[namespace][method];\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AAEpD,IAAIE,SAAS,GAAG,SAAAA,CAAUC,QAAQ,EAAE;EAClC,OAAOF,UAAU,CAACE,QAAQ,CAAC,GAAGA,QAAQ,GAAGC,SAAS;AACpD,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;EAC5C,OAAOC,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGR,SAAS,CAACH,UAAU,CAACQ,SAAS,CAAC,CAAC,GAAGR,UAAU,CAACQ,SAAS,CAAC,IAAIR,UAAU,CAACQ,SAAS,CAAC,CAACC,MAAM,CAAC;AACzH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}