{"ast": null, "code": "'use strict';\n\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\n// eslint-disable-next-line es/no-object-getownpropertynames -- safe\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};", "map": {"version": 3, "names": ["internalObjectKeys", "require", "enumBugKeys", "hiddenKeys", "concat", "exports", "f", "Object", "getOwnPropertyNames", "O"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/object-get-own-property-names.js"], "sourcesContent": ["'use strict';\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\n// eslint-disable-next-line es/no-object-getownpropertynames -- safe\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,kBAAkB,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AACrE,IAAIC,WAAW,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAEvD,IAAIE,UAAU,GAAGD,WAAW,CAACE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC;;AAE1D;AACA;AACA;AACAC,OAAO,CAACC,CAAC,GAAGC,MAAM,CAACC,mBAAmB,IAAI,SAASA,mBAAmBA,CAACC,CAAC,EAAE;EACxE,OAAOT,kBAAkB,CAACS,CAAC,EAAEN,UAAU,CAAC;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}