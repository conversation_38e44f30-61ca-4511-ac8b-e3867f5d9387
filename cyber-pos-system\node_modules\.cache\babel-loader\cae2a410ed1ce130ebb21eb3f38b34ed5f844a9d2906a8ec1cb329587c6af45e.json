{"ast": null, "code": "'use strict';\n\nvar DESCRIPTORS = require('../internals/descriptors');\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\nvar definePropertyModule = require('../internals/object-define-property');\nvar anObject = require('../internals/an-object');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar objectKeys = require('../internals/object-keys');\n\n// `Object.defineProperties` method\n// https://tc39.es/ecma262/#sec-object.defineproperties\n// eslint-disable-next-line es/no-object-defineproperties -- safe\nexports.f = DESCRIPTORS && !V8_PROTOTYPE_DEFINE_BUG ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var props = toIndexedObject(Properties);\n  var keys = objectKeys(Properties);\n  var length = keys.length;\n  var index = 0;\n  var key;\n  while (length > index) definePropertyModule.f(O, key = keys[index++], props[key]);\n  return O;\n};", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "V8_PROTOTYPE_DEFINE_BUG", "definePropertyModule", "anObject", "toIndexedObject", "objectKeys", "exports", "f", "Object", "defineProperties", "O", "Properties", "props", "keys", "length", "index", "key"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/object-define-properties.js"], "sourcesContent": ["'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\nvar definePropertyModule = require('../internals/object-define-property');\nvar anObject = require('../internals/an-object');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar objectKeys = require('../internals/object-keys');\n\n// `Object.defineProperties` method\n// https://tc39.es/ecma262/#sec-object.defineproperties\n// eslint-disable-next-line es/no-object-defineproperties -- safe\nexports.f = DESCRIPTORS && !V8_PROTOTYPE_DEFINE_BUG ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var props = toIndexedObject(Properties);\n  var keys = objectKeys(Properties);\n  var length = keys.length;\n  var index = 0;\n  var key;\n  while (length > index) definePropertyModule.f(O, key = keys[index++], props[key]);\n  return O;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACrD,IAAIC,uBAAuB,GAAGD,OAAO,CAAC,sCAAsC,CAAC;AAC7E,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,qCAAqC,CAAC;AACzE,IAAIG,QAAQ,GAAGH,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAII,eAAe,GAAGJ,OAAO,CAAC,gCAAgC,CAAC;AAC/D,IAAIK,UAAU,GAAGL,OAAO,CAAC,0BAA0B,CAAC;;AAEpD;AACA;AACA;AACAM,OAAO,CAACC,CAAC,GAAGR,WAAW,IAAI,CAACE,uBAAuB,GAAGO,MAAM,CAACC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,CAAC,EAAEC,UAAU,EAAE;EACvHR,QAAQ,CAACO,CAAC,CAAC;EACX,IAAIE,KAAK,GAAGR,eAAe,CAACO,UAAU,CAAC;EACvC,IAAIE,IAAI,GAAGR,UAAU,CAACM,UAAU,CAAC;EACjC,IAAIG,MAAM,GAAGD,IAAI,CAACC,MAAM;EACxB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG;EACP,OAAOF,MAAM,GAAGC,KAAK,EAAEb,oBAAoB,CAACK,CAAC,CAACG,CAAC,EAAEM,GAAG,GAAGH,IAAI,CAACE,KAAK,EAAE,CAAC,EAAEH,KAAK,CAACI,GAAG,CAAC,CAAC;EACjF,OAAON,CAAC;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}