{"ast": null, "code": "'use strict';\n\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\ntest[TO_STRING_TAG] = 'z';\nmodule.exports = String(test) === '[object z]';", "map": {"version": 3, "names": ["wellKnownSymbol", "require", "TO_STRING_TAG", "test", "module", "exports", "String"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/to-string-tag-support.js"], "sourcesContent": ["'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,gCAAgC,CAAC;AAE/D,IAAIC,aAAa,GAAGF,eAAe,CAAC,aAAa,CAAC;AAClD,IAAIG,IAAI,GAAG,CAAC,CAAC;AAEbA,IAAI,CAACD,aAAa,CAAC,GAAG,GAAG;AAEzBE,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACH,IAAI,CAAC,KAAK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}