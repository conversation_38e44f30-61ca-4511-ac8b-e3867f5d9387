{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar store = require('../internals/shared-store');\nvar functionToString = uncurryThis(Function.toString);\n\n// this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\nif (!isCallable(store.inspectSource)) {\n  store.inspectSource = function (it) {\n    return functionToString(it);\n  };\n}\nmodule.exports = store.inspectSource;", "map": {"version": 3, "names": ["uncurryThis", "require", "isCallable", "store", "functionToString", "Function", "toString", "inspectSource", "it", "module", "exports"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/inspect-source.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar store = require('../internals/shared-store');\n\nvar functionToString = uncurryThis(Function.toString);\n\n// this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\nif (!isCallable(store.inspectSource)) {\n  store.inspectSource = function (it) {\n    return functionToString(it);\n  };\n}\n\nmodule.exports = store.inspectSource;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,KAAK,GAAGF,OAAO,CAAC,2BAA2B,CAAC;AAEhD,IAAIG,gBAAgB,GAAGJ,WAAW,CAACK,QAAQ,CAACC,QAAQ,CAAC;;AAErD;AACA,IAAI,CAACJ,UAAU,CAACC,KAAK,CAACI,aAAa,CAAC,EAAE;EACpCJ,KAAK,CAACI,aAAa,GAAG,UAAUC,EAAE,EAAE;IAClC,OAAOJ,gBAAgB,CAACI,EAAE,CAAC;EAC7B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGP,KAAK,CAACI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}