{"ast": null, "code": "'use strict';\n\nvar getBuiltIn = require('../internals/get-built-in');\nmodule.exports = getBuiltIn('document', 'documentElement');", "map": {"version": 3, "names": ["getBuiltIn", "require", "module", "exports"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/html.js"], "sourcesContent": ["'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('document', 'documentElement');\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAErDC,MAAM,CAACC,OAAO,GAAGH,UAAU,CAAC,UAAU,EAAE,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}