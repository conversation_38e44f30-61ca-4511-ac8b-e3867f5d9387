{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\reports\\\\InventoryAnalytics.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Package, AlertTriangle, TrendingDown, TrendingUp, BarChart3, RefreshCw, Filter } from 'lucide-react';\nimport { useProducts } from '../../hooks/useProducts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InventoryAnalytics = () => {\n  _s();\n  const {\n    products,\n    loading\n  } = useProducts();\n  const [alerts, setAlerts] = useState([]);\n  const [metrics, setMetrics] = useState(null);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [alertFilter, setAlertFilter] = useState('all');\n  useEffect(() => {\n    if (products.length > 0) {\n      calculateMetrics();\n      generateAlerts();\n    }\n  }, [products]);\n  const calculateMetrics = () => {\n    const totalProducts = products.length;\n    const lowStockItems = products.filter(p => p.stockQuantity <= p.reorderLevel && p.stockQuantity > 0).length;\n    const outOfStockItems = products.filter(p => p.stockQuantity === 0).length;\n\n    // Calculate expiring soon items (within 30 days)\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\n    const expiringSoonItems = products.filter(p => p.hasExpiry && p.expiryDate && p.expiryDate <= thirtyDaysFromNow).length;\n    const totalStockValue = products.reduce((sum, p) => sum + p.stockQuantity * p.price, 0);\n    const averageStockLevel = totalProducts > 0 ? products.reduce((sum, p) => sum + p.stockQuantity, 0) / totalProducts : 0;\n    setMetrics({\n      totalProducts,\n      lowStockItems,\n      outOfStockItems,\n      expiringSoonItems,\n      totalStockValue,\n      averageStockLevel\n    });\n  };\n  const generateAlerts = () => {\n    const newAlerts = [];\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\n    products.forEach(product => {\n      // Out of stock alert\n      if (product.stockQuantity === 0) {\n        newAlerts.push({\n          id: `${product.id}-out-of-stock`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'out_of_stock'\n        });\n      }\n      // Low stock alert\n      else if (product.stockQuantity <= product.reorderLevel) {\n        newAlerts.push({\n          id: `${product.id}-low-stock`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'low_stock'\n        });\n      }\n\n      // Expiring soon alert\n      if (product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow) {\n        newAlerts.push({\n          id: `${product.id}-expiring`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'expiring_soon',\n          expiryDate: product.expiryDate\n        });\n      }\n    });\n    setAlerts(newAlerts);\n  };\n  const getAlertIcon = alertType => {\n    switch (alertType) {\n      case 'out_of_stock':\n        return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n          className: \"h-5 w-5 text-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 16\n        }, this);\n      case 'low_stock':\n        return /*#__PURE__*/_jsxDEV(TrendingDown, {\n          className: \"h-5 w-5 text-orange-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 16\n        }, this);\n      case 'expiring_soon':\n        return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n          className: \"h-5 w-5 text-yellow-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Package, {\n          className: \"h-5 w-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getAlertColor = alertType => {\n    switch (alertType) {\n      case 'out_of_stock':\n        return 'border-red-200 bg-red-50';\n      case 'low_stock':\n        return 'border-orange-200 bg-orange-50';\n      case 'expiring_soon':\n        return 'border-yellow-200 bg-yellow-50';\n      default:\n        return 'border-gray-200 bg-gray-50';\n    }\n  };\n  const categories = ['all', ...Array.from(new Set(products.map(p => p.category)))];\n  const filteredAlerts = alerts.filter(alert => {\n    const categoryMatch = selectedCategory === 'all' || alert.category === selectedCategory;\n    const alertMatch = alertFilter === 'all' || alert.alertType === alertFilter;\n    return categoryMatch && alertMatch;\n  });\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          className: \"h-8 w-8 text-primary-600 animate-spin mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Loading inventory data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [metrics && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(Package, {\n              className: \"h-8 w-8 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: metrics.totalProducts\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: \"h-8 w-8 text-red-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Critical Alerts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: metrics.outOfStockItems + metrics.lowStockItems\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: [metrics.outOfStockItems, \" out of stock, \", metrics.lowStockItems, \" low stock\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-8 w-8 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Stock Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"KSh \", metrics.totalStockValue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"h-8 w-8 text-purple-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Avg. Stock Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: metrics.averageStockLevel.toFixed(0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            className: \"h-5 w-5 mr-2 text-orange-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), \"Inventory Alerts (\", filteredAlerts.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-4 w-4 text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category,\n                children: category === 'all' ? 'All Categories' : category\n              }, category, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: alertFilter,\n            onChange: e => setAlertFilter(e.target.value),\n            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Alerts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"out_of_stock\",\n              children: \"Out of Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"low_stock\",\n              children: \"Low Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"expiring_soon\",\n              children: \"Expiring Soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: filteredAlerts.length > 0 ? filteredAlerts.map(alert => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `border rounded-lg p-4 ${getAlertColor(alert.alertType)}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [getAlertIcon(alert.alertType), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: alert.productName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: alert.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: [\"Stock: \", alert.currentStock]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 21\n              }, this), alert.alertType !== 'expiring_soon' && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: [\"Reorder at: \", alert.reorderLevel]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 23\n              }, this), alert.expiryDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-red-600\",\n                children: [\"Expires: \", alert.expiryDate.toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 17\n          }, this)\n        }, alert.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(Package, {\n            className: \"mx-auto h-12 w-12 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-900\",\n            children: \"No Alerts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500\",\n            children: \"All inventory levels are within normal ranges.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(InventoryAnalytics, \"LBf5QY4gdHnWaBlSxWQrmGkNt3Y=\", false, function () {\n  return [useProducts];\n});\n_c = InventoryAnalytics;\nexport default InventoryAnalytics;\nvar _c;\n$RefreshReg$(_c, \"InventoryAnalytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Package", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrendingDown", "TrendingUp", "BarChart3", "RefreshCw", "Filter", "useProducts", "jsxDEV", "_jsxDEV", "InventoryAnalytics", "_s", "products", "loading", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "metrics", "setMetrics", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "alertFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "calculateMetrics", "generateAlerts", "totalProducts", "lowStockItems", "filter", "p", "stockQuantity", "reorderLevel", "outOfStockItems", "thirtyDaysFromNow", "Date", "setDate", "getDate", "expiringSoonItems", "hasEx<PERSON>ry", "expiryDate", "totalStockValue", "reduce", "sum", "price", "averageStockLevel", "new<PERSON><PERSON><PERSON>", "for<PERSON>ach", "product", "push", "id", "productName", "name", "currentStock", "category", "alertType", "getAlertIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAlertColor", "categories", "Array", "from", "Set", "map", "filtered<PERSON>lerts", "alert", "categoryMatch", "alertMatch", "children", "toLocaleString", "toFixed", "value", "onChange", "e", "target", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/reports/InventoryAnalytics.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Package,\n  AlertTriangle,\n  TrendingDown,\n  TrendingUp,\n  BarChart3,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Filter\n} from 'lucide-react';\nimport { useProducts } from '../../hooks/useProducts';\nimport { Product } from '../../types';\n\ninterface InventoryAlert {\n  id: string;\n  productName: string;\n  currentStock: number;\n  reorderLevel: number;\n  category: string;\n  alertType: 'low_stock' | 'out_of_stock' | 'expiring_soon';\n  expiryDate?: Date;\n}\n\ninterface InventoryMetrics {\n  totalProducts: number;\n  lowStockItems: number;\n  outOfStockItems: number;\n  expiringSoonItems: number;\n  totalStockValue: number;\n  averageStockLevel: number;\n}\n\nconst InventoryAnalytics: React.FC = () => {\n  const { products, loading } = useProducts();\n  const [alerts, setAlerts] = useState<InventoryAlert[]>([]);\n  const [metrics, setMetrics] = useState<InventoryMetrics | null>(null);\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [alertFilter, setAlertFilter] = useState<string>('all');\n\n  useEffect(() => {\n    if (products.length > 0) {\n      calculateMetrics();\n      generateAlerts();\n    }\n  }, [products]);\n\n  const calculateMetrics = () => {\n    const totalProducts = products.length;\n    const lowStockItems = products.filter(p => p.stockQuantity <= p.reorderLevel && p.stockQuantity > 0).length;\n    const outOfStockItems = products.filter(p => p.stockQuantity === 0).length;\n    \n    // Calculate expiring soon items (within 30 days)\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\n    const expiringSoonItems = products.filter(p => \n      p.hasExpiry && p.expiryDate && p.expiryDate <= thirtyDaysFromNow\n    ).length;\n\n    const totalStockValue = products.reduce((sum, p) => sum + (p.stockQuantity * p.price), 0);\n    const averageStockLevel = totalProducts > 0 ? \n      products.reduce((sum, p) => sum + p.stockQuantity, 0) / totalProducts : 0;\n\n    setMetrics({\n      totalProducts,\n      lowStockItems,\n      outOfStockItems,\n      expiringSoonItems,\n      totalStockValue,\n      averageStockLevel,\n    });\n  };\n\n  const generateAlerts = () => {\n    const newAlerts: InventoryAlert[] = [];\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\n\n    products.forEach(product => {\n      // Out of stock alert\n      if (product.stockQuantity === 0) {\n        newAlerts.push({\n          id: `${product.id}-out-of-stock`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'out_of_stock',\n        });\n      }\n      // Low stock alert\n      else if (product.stockQuantity <= product.reorderLevel) {\n        newAlerts.push({\n          id: `${product.id}-low-stock`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'low_stock',\n        });\n      }\n\n      // Expiring soon alert\n      if (product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow) {\n        newAlerts.push({\n          id: `${product.id}-expiring`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'expiring_soon',\n          expiryDate: product.expiryDate,\n        });\n      }\n    });\n\n    setAlerts(newAlerts);\n  };\n\n  const getAlertIcon = (alertType: string) => {\n    switch (alertType) {\n      case 'out_of_stock':\n        return <AlertTriangle className=\"h-5 w-5 text-red-600\" />;\n      case 'low_stock':\n        return <TrendingDown className=\"h-5 w-5 text-orange-600\" />;\n      case 'expiring_soon':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />;\n      default:\n        return <Package className=\"h-5 w-5 text-gray-600\" />;\n    }\n  };\n\n  const getAlertColor = (alertType: string) => {\n    switch (alertType) {\n      case 'out_of_stock':\n        return 'border-red-200 bg-red-50';\n      case 'low_stock':\n        return 'border-orange-200 bg-orange-50';\n      case 'expiring_soon':\n        return 'border-yellow-200 bg-yellow-50';\n      default:\n        return 'border-gray-200 bg-gray-50';\n    }\n  };\n\n  const categories = ['all', ...Array.from(new Set(products.map(p => p.category)))];\n  const filteredAlerts = alerts.filter(alert => {\n    const categoryMatch = selectedCategory === 'all' || alert.category === selectedCategory;\n    const alertMatch = alertFilter === 'all' || alert.alertType === alertFilter;\n    return categoryMatch && alertMatch;\n  });\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-center py-12\">\n          <RefreshCw className=\"h-8 w-8 text-primary-600 animate-spin mr-3\" />\n          <span className=\"text-lg text-gray-600\">Loading inventory data...</span>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Inventory Metrics */}\n      {metrics && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Package className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Total Products</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{metrics.totalProducts}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Critical Alerts</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {metrics.outOfStockItems + metrics.lowStockItems}\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  {metrics.outOfStockItems} out of stock, {metrics.lowStockItems} low stock\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <TrendingUp className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Stock Value</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  KSh {metrics.totalStockValue.toLocaleString()}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <BarChart3 className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Avg. Stock Level</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {metrics.averageStockLevel.toFixed(0)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Filters and Alerts */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n            <AlertTriangle className=\"h-5 w-5 mr-2 text-orange-600\" />\n            Inventory Alerts ({filteredAlerts.length})\n          </h3>\n          \n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-4 w-4 text-gray-500\" />\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n              >\n                {categories.map(category => (\n                  <option key={category} value={category}>\n                    {category === 'all' ? 'All Categories' : category}\n                  </option>\n                ))}\n              </select>\n            </div>\n            \n            <select\n              value={alertFilter}\n              onChange={(e) => setAlertFilter(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n            >\n              <option value=\"all\">All Alerts</option>\n              <option value=\"out_of_stock\">Out of Stock</option>\n              <option value=\"low_stock\">Low Stock</option>\n              <option value=\"expiring_soon\">Expiring Soon</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"space-y-3\">\n          {filteredAlerts.length > 0 ? (\n            filteredAlerts.map((alert) => (\n              <div\n                key={alert.id}\n                className={`border rounded-lg p-4 ${getAlertColor(alert.alertType)}`}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    {getAlertIcon(alert.alertType)}\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">{alert.productName}</p>\n                      <p className=\"text-xs text-gray-500\">{alert.category}</p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-medium text-gray-900\">\n                      Stock: {alert.currentStock}\n                    </p>\n                    {alert.alertType !== 'expiring_soon' && (\n                      <p className=\"text-xs text-gray-500\">\n                        Reorder at: {alert.reorderLevel}\n                      </p>\n                    )}\n                    {alert.expiryDate && (\n                      <p className=\"text-xs text-red-600\">\n                        Expires: {alert.expiryDate.toLocaleDateString()}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))\n          ) : (\n            <div className=\"text-center py-8\">\n              <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No Alerts</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                All inventory levels are within normal ranges.\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InventoryAnalytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,UAAU,EACVC,SAAS,EACTC,SAAS,EACTC,MAAM,QACD,cAAc;AACrB,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsBtD,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC,QAAQ;IAAEC;EAAQ,CAAC,GAAGN,WAAW,CAAC,CAAC;EAC3C,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAmB,EAAE,CAAC;EAC1D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAA0B,IAAI,CAAC;EACrE,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAS,KAAK,CAAC;EACvE,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAS,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd,IAAIa,QAAQ,CAACU,MAAM,GAAG,CAAC,EAAE;MACvBC,gBAAgB,CAAC,CAAC;MAClBC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAME,aAAa,GAAGb,QAAQ,CAACU,MAAM;IACrC,MAAMI,aAAa,GAAGd,QAAQ,CAACe,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,IAAID,CAAC,CAACE,YAAY,IAAIF,CAAC,CAACC,aAAa,GAAG,CAAC,CAAC,CAACP,MAAM;IAC3G,MAAMS,eAAe,GAAGnB,QAAQ,CAACe,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,CAAC,CAAC,CAACP,MAAM;;IAE1E;IACA,MAAMU,iBAAiB,GAAG,IAAIC,IAAI,CAAC,CAAC;IACpCD,iBAAiB,CAACE,OAAO,CAACF,iBAAiB,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAC3D,MAAMC,iBAAiB,GAAGxB,QAAQ,CAACe,MAAM,CAACC,CAAC,IACzCA,CAAC,CAACS,SAAS,IAAIT,CAAC,CAACU,UAAU,IAAIV,CAAC,CAACU,UAAU,IAAIN,iBACjD,CAAC,CAACV,MAAM;IAER,MAAMiB,eAAe,GAAG3B,QAAQ,CAAC4B,MAAM,CAAC,CAACC,GAAG,EAAEb,CAAC,KAAKa,GAAG,GAAIb,CAAC,CAACC,aAAa,GAAGD,CAAC,CAACc,KAAM,EAAE,CAAC,CAAC;IACzF,MAAMC,iBAAiB,GAAGlB,aAAa,GAAG,CAAC,GACzCb,QAAQ,CAAC4B,MAAM,CAAC,CAACC,GAAG,EAAEb,CAAC,KAAKa,GAAG,GAAGb,CAAC,CAACC,aAAa,EAAE,CAAC,CAAC,GAAGJ,aAAa,GAAG,CAAC;IAE3ER,UAAU,CAAC;MACTQ,aAAa;MACbC,aAAa;MACbK,eAAe;MACfK,iBAAiB;MACjBG,eAAe;MACfI;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMnB,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMoB,SAA2B,GAAG,EAAE;IACtC,MAAMZ,iBAAiB,GAAG,IAAIC,IAAI,CAAC,CAAC;IACpCD,iBAAiB,CAACE,OAAO,CAACF,iBAAiB,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAE3DvB,QAAQ,CAACiC,OAAO,CAACC,OAAO,IAAI;MAC1B;MACA,IAAIA,OAAO,CAACjB,aAAa,KAAK,CAAC,EAAE;QAC/Be,SAAS,CAACG,IAAI,CAAC;UACbC,EAAE,EAAE,GAAGF,OAAO,CAACE,EAAE,eAAe;UAChCC,WAAW,EAAEH,OAAO,CAACI,IAAI;UACzBC,YAAY,EAAEL,OAAO,CAACjB,aAAa;UACnCC,YAAY,EAAEgB,OAAO,CAAChB,YAAY;UAClCsB,QAAQ,EAAEN,OAAO,CAACM,QAAQ;UAC1BC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IAAIP,OAAO,CAACjB,aAAa,IAAIiB,OAAO,CAAChB,YAAY,EAAE;QACtDc,SAAS,CAACG,IAAI,CAAC;UACbC,EAAE,EAAE,GAAGF,OAAO,CAACE,EAAE,YAAY;UAC7BC,WAAW,EAAEH,OAAO,CAACI,IAAI;UACzBC,YAAY,EAAEL,OAAO,CAACjB,aAAa;UACnCC,YAAY,EAAEgB,OAAO,CAAChB,YAAY;UAClCsB,QAAQ,EAAEN,OAAO,CAACM,QAAQ;UAC1BC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIP,OAAO,CAACT,SAAS,IAAIS,OAAO,CAACR,UAAU,IAAIQ,OAAO,CAACR,UAAU,IAAIN,iBAAiB,EAAE;QACtFY,SAAS,CAACG,IAAI,CAAC;UACbC,EAAE,EAAE,GAAGF,OAAO,CAACE,EAAE,WAAW;UAC5BC,WAAW,EAAEH,OAAO,CAACI,IAAI;UACzBC,YAAY,EAAEL,OAAO,CAACjB,aAAa;UACnCC,YAAY,EAAEgB,OAAO,CAAChB,YAAY;UAClCsB,QAAQ,EAAEN,OAAO,CAACM,QAAQ;UAC1BC,SAAS,EAAE,eAAe;UAC1Bf,UAAU,EAAEQ,OAAO,CAACR;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEFvB,SAAS,CAAC6B,SAAS,CAAC;EACtB,CAAC;EAED,MAAMU,YAAY,GAAID,SAAiB,IAAK;IAC1C,QAAQA,SAAS;MACf,KAAK,cAAc;QACjB,oBAAO5C,OAAA,CAACR,aAAa;UAACsD,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,WAAW;QACd,oBAAOlD,OAAA,CAACP,YAAY;UAACqD,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,eAAe;QAClB,oBAAOlD,OAAA,CAACR,aAAa;UAACsD,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9D;QACE,oBAAOlD,OAAA,CAACT,OAAO;UAACuD,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,aAAa,GAAIP,SAAiB,IAAK;IAC3C,QAAQA,SAAS;MACf,KAAK,cAAc;QACjB,OAAO,0BAA0B;MACnC,KAAK,WAAW;QACd,OAAO,gCAAgC;MACzC,KAAK,eAAe;QAClB,OAAO,gCAAgC;MACzC;QACE,OAAO,4BAA4B;IACvC;EACF,CAAC;EAED,MAAMQ,UAAU,GAAG,CAAC,KAAK,EAAE,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACpD,QAAQ,CAACqD,GAAG,CAACrC,CAAC,IAAIA,CAAC,CAACwB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACjF,MAAMc,cAAc,GAAGpD,MAAM,CAACa,MAAM,CAACwC,KAAK,IAAI;IAC5C,MAAMC,aAAa,GAAGlD,gBAAgB,KAAK,KAAK,IAAIiD,KAAK,CAACf,QAAQ,KAAKlC,gBAAgB;IACvF,MAAMmD,UAAU,GAAGjD,WAAW,KAAK,KAAK,IAAI+C,KAAK,CAACd,SAAS,KAAKjC,WAAW;IAC3E,OAAOgD,aAAa,IAAIC,UAAU;EACpC,CAAC,CAAC;EAEF,IAAIxD,OAAO,EAAE;IACX,oBACEJ,OAAA;MAAK8C,SAAS,EAAC,gCAAgC;MAAAe,QAAA,eAC7C7D,OAAA;QAAK8C,SAAS,EAAC,wCAAwC;QAAAe,QAAA,gBACrD7D,OAAA,CAACJ,SAAS;UAACkD,SAAS,EAAC;QAA4C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpElD,OAAA;UAAM8C,SAAS,EAAC,uBAAuB;UAAAe,QAAA,EAAC;QAAyB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElD,OAAA;IAAK8C,SAAS,EAAC,WAAW;IAAAe,QAAA,GAEvBtD,OAAO,iBACNP,OAAA;MAAK8C,SAAS,EAAC,sDAAsD;MAAAe,QAAA,gBACnE7D,OAAA;QAAK8C,SAAS,EAAC,gCAAgC;QAAAe,QAAA,eAC7C7D,OAAA;UAAK8C,SAAS,EAAC,mBAAmB;UAAAe,QAAA,gBAChC7D,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAe,QAAA,eAC5B7D,OAAA,CAACT,OAAO;cAACuD,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,MAAM;YAAAe,QAAA,gBACnB7D,OAAA;cAAG8C,SAAS,EAAC,mCAAmC;cAAAe,QAAA,EAAC;YAAc;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnElD,OAAA;cAAG8C,SAAS,EAAC,kCAAkC;cAAAe,QAAA,EAAEtD,OAAO,CAACS;YAAa;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAK8C,SAAS,EAAC,gCAAgC;QAAAe,QAAA,eAC7C7D,OAAA;UAAK8C,SAAS,EAAC,mBAAmB;UAAAe,QAAA,gBAChC7D,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAe,QAAA,eAC5B7D,OAAA,CAACR,aAAa;cAACsD,SAAS,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,MAAM;YAAAe,QAAA,gBACnB7D,OAAA;cAAG8C,SAAS,EAAC,mCAAmC;cAAAe,QAAA,EAAC;YAAe;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpElD,OAAA;cAAG8C,SAAS,EAAC,kCAAkC;cAAAe,QAAA,EAC5CtD,OAAO,CAACe,eAAe,GAAGf,OAAO,CAACU;YAAa;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACJlD,OAAA;cAAG8C,SAAS,EAAC,uBAAuB;cAAAe,QAAA,GACjCtD,OAAO,CAACe,eAAe,EAAC,iBAAe,EAACf,OAAO,CAACU,aAAa,EAAC,YACjE;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAK8C,SAAS,EAAC,gCAAgC;QAAAe,QAAA,eAC7C7D,OAAA;UAAK8C,SAAS,EAAC,mBAAmB;UAAAe,QAAA,gBAChC7D,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAe,QAAA,eAC5B7D,OAAA,CAACN,UAAU;cAACoD,SAAS,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,MAAM;YAAAe,QAAA,gBACnB7D,OAAA;cAAG8C,SAAS,EAAC,mCAAmC;cAAAe,QAAA,EAAC;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChElD,OAAA;cAAG8C,SAAS,EAAC,kCAAkC;cAAAe,QAAA,GAAC,MAC1C,EAACtD,OAAO,CAACuB,eAAe,CAACgC,cAAc,CAAC,CAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAK8C,SAAS,EAAC,gCAAgC;QAAAe,QAAA,eAC7C7D,OAAA;UAAK8C,SAAS,EAAC,mBAAmB;UAAAe,QAAA,gBAChC7D,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAe,QAAA,eAC5B7D,OAAA,CAACL,SAAS;cAACmD,SAAS,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,MAAM;YAAAe,QAAA,gBACnB7D,OAAA;cAAG8C,SAAS,EAAC,mCAAmC;cAAAe,QAAA,EAAC;YAAgB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrElD,OAAA;cAAG8C,SAAS,EAAC,kCAAkC;cAAAe,QAAA,EAC5CtD,OAAO,CAAC2B,iBAAiB,CAAC6B,OAAO,CAAC,CAAC;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDlD,OAAA;MAAK8C,SAAS,EAAC,gCAAgC;MAAAe,QAAA,gBAC7C7D,OAAA;QAAK8C,SAAS,EAAC,wCAAwC;QAAAe,QAAA,gBACrD7D,OAAA;UAAI8C,SAAS,EAAC,qDAAqD;UAAAe,QAAA,gBACjE7D,OAAA,CAACR,aAAa;YAACsD,SAAS,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBACxC,EAACO,cAAc,CAAC5C,MAAM,EAAC,GAC3C;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELlD,OAAA;UAAK8C,SAAS,EAAC,6BAA6B;UAAAe,QAAA,gBAC1C7D,OAAA;YAAK8C,SAAS,EAAC,6BAA6B;YAAAe,QAAA,gBAC1C7D,OAAA,CAACH,MAAM;cAACiD,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5ClD,OAAA;cACEgE,KAAK,EAAEvD,gBAAiB;cACxBwD,QAAQ,EAAGC,CAAC,IAAKxD,mBAAmB,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDlB,SAAS,EAAC,qDAAqD;cAAAe,QAAA,EAE9DT,UAAU,CAACI,GAAG,CAACb,QAAQ,iBACtB3C,OAAA;gBAAuBgE,KAAK,EAAErB,QAAS;gBAAAkB,QAAA,EACpClB,QAAQ,KAAK,KAAK,GAAG,gBAAgB,GAAGA;cAAQ,GADtCA,QAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEb,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlD,OAAA;YACEgE,KAAK,EAAErD,WAAY;YACnBsD,QAAQ,EAAGC,CAAC,IAAKtD,cAAc,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDlB,SAAS,EAAC,qDAAqD;YAAAe,QAAA,gBAE/D7D,OAAA;cAAQgE,KAAK,EAAC,KAAK;cAAAH,QAAA,EAAC;YAAU;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvClD,OAAA;cAAQgE,KAAK,EAAC,cAAc;cAAAH,QAAA,EAAC;YAAY;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClDlD,OAAA;cAAQgE,KAAK,EAAC,WAAW;cAAAH,QAAA,EAAC;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5ClD,OAAA;cAAQgE,KAAK,EAAC,eAAe;cAAAH,QAAA,EAAC;YAAa;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAe,QAAA,EACvBJ,cAAc,CAAC5C,MAAM,GAAG,CAAC,GACxB4C,cAAc,CAACD,GAAG,CAAEE,KAAK,iBACvB1D,OAAA;UAEE8C,SAAS,EAAE,yBAAyBK,aAAa,CAACO,KAAK,CAACd,SAAS,CAAC,EAAG;UAAAiB,QAAA,eAErE7D,OAAA;YAAK8C,SAAS,EAAC,mCAAmC;YAAAe,QAAA,gBAChD7D,OAAA;cAAK8C,SAAS,EAAC,mBAAmB;cAAAe,QAAA,GAC/BhB,YAAY,CAACa,KAAK,CAACd,SAAS,CAAC,eAC9B5C,OAAA;gBAAK8C,SAAS,EAAC,MAAM;gBAAAe,QAAA,gBACnB7D,OAAA;kBAAG8C,SAAS,EAAC,mCAAmC;kBAAAe,QAAA,EAAEH,KAAK,CAAClB;gBAAW;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxElD,OAAA;kBAAG8C,SAAS,EAAC,uBAAuB;kBAAAe,QAAA,EAAEH,KAAK,CAACf;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAe,QAAA,gBACzB7D,OAAA;gBAAG8C,SAAS,EAAC,mCAAmC;gBAAAe,QAAA,GAAC,SACxC,EAACH,KAAK,CAAChB,YAAY;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,EACHQ,KAAK,CAACd,SAAS,KAAK,eAAe,iBAClC5C,OAAA;gBAAG8C,SAAS,EAAC,uBAAuB;gBAAAe,QAAA,GAAC,cACvB,EAACH,KAAK,CAACrC,YAAY;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CACJ,EACAQ,KAAK,CAAC7B,UAAU,iBACf7B,OAAA;gBAAG8C,SAAS,EAAC,sBAAsB;gBAAAe,QAAA,GAAC,WACzB,EAACH,KAAK,CAAC7B,UAAU,CAACuC,kBAAkB,CAAC,CAAC;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA1BDQ,KAAK,CAACnB,EAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BV,CACN,CAAC,gBAEFlD,OAAA;UAAK8C,SAAS,EAAC,kBAAkB;UAAAe,QAAA,gBAC/B7D,OAAA,CAACT,OAAO;YAACuD,SAAS,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDlD,OAAA;YAAI8C,SAAS,EAAC,wCAAwC;YAAAe,QAAA,EAAC;UAAS;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrElD,OAAA;YAAG8C,SAAS,EAAC,4BAA4B;YAAAe,QAAA,EAAC;UAE1C;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChD,EAAA,CArRID,kBAA4B;EAAA,QACFH,WAAW;AAAA;AAAAuE,EAAA,GADrCpE,kBAA4B;AAuRlC,eAAeA,kBAAkB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}