{"ast": null, "code": "'use strict';\n\n/* global ActiveXObject -- old IE, WSH */\nvar anObject = require('../internals/an-object');\nvar definePropertiesModule = require('../internals/object-define-properties');\nvar enumBugKeys = require('../internals/enum-bug-keys');\nvar hiddenKeys = require('../internals/hidden-keys');\nvar html = require('../internals/html');\nvar documentCreateElement = require('../internals/document-create-element');\nvar sharedKey = require('../internals/shared-key');\nvar GT = '>';\nvar LT = '<';\nvar PROTOTYPE = 'prototype';\nvar SCRIPT = 'script';\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar EmptyConstructor = function () {/* empty */};\nvar scriptTag = function (content) {\n  return LT + SCRIPT + GT + content + LT + '/' + SCRIPT + GT;\n};\n\n// Create object with fake `null` prototype: use ActiveX Object with cleared prototype\nvar NullProtoObjectViaActiveX = function (activeXDocument) {\n  activeXDocument.write(scriptTag(''));\n  activeXDocument.close();\n  var temp = activeXDocument.parentWindow.Object;\n  // eslint-disable-next-line no-useless-assignment -- avoid memory leak\n  activeXDocument = null;\n  return temp;\n};\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar NullProtoObjectViaIFrame = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = documentCreateElement('iframe');\n  var JS = 'java' + SCRIPT + ':';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  html.appendChild(iframe);\n  // https://github.com/zloirock/core-js/issues/475\n  iframe.src = String(JS);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(scriptTag('document.F=Object'));\n  iframeDocument.close();\n  return iframeDocument.F;\n};\n\n// Check for document.domain and active x support\n// No need to use active x approach when document.domain is not set\n// see https://github.com/es-shims/es5-shim/issues/150\n// variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n// avoid IE GC bug\nvar activeXDocument;\nvar NullProtoObject = function () {\n  try {\n    activeXDocument = new ActiveXObject('htmlfile');\n  } catch (error) {/* ignore */}\n  NullProtoObject = typeof document != 'undefined' ? document.domain && activeXDocument ? NullProtoObjectViaActiveX(activeXDocument) // old IE\n  : NullProtoObjectViaIFrame() : NullProtoObjectViaActiveX(activeXDocument); // WSH\n  var length = enumBugKeys.length;\n  while (length--) delete NullProtoObject[PROTOTYPE][enumBugKeys[length]];\n  return NullProtoObject();\n};\nhiddenKeys[IE_PROTO] = true;\n\n// `Object.create` method\n// https://tc39.es/ecma262/#sec-object.create\n// eslint-disable-next-line es/no-object-create -- safe\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    EmptyConstructor[PROTOTYPE] = anObject(O);\n    result = new EmptyConstructor();\n    EmptyConstructor[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = NullProtoObject();\n  return Properties === undefined ? result : definePropertiesModule.f(result, Properties);\n};", "map": {"version": 3, "names": ["anObject", "require", "definePropertiesModule", "enumBugKeys", "hiddenKeys", "html", "documentCreateElement", "sharedKey", "GT", "LT", "PROTOTYPE", "SCRIPT", "IE_PROTO", "EmptyConstructor", "scriptTag", "content", "NullProtoObjectViaActiveX", "activeXDocument", "write", "close", "temp", "parentWindow", "Object", "NullProtoObjectViaIFrame", "iframe", "JS", "iframeDocument", "style", "display", "append<PERSON><PERSON><PERSON>", "src", "String", "contentWindow", "document", "open", "F", "NullProtoObject", "ActiveXObject", "error", "domain", "length", "module", "exports", "create", "O", "Properties", "result", "undefined", "f"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/object-create.js"], "sourcesContent": ["'use strict';\n/* global ActiveXObject -- old IE, WSH */\nvar anObject = require('../internals/an-object');\nvar definePropertiesModule = require('../internals/object-define-properties');\nvar enumBugKeys = require('../internals/enum-bug-keys');\nvar hiddenKeys = require('../internals/hidden-keys');\nvar html = require('../internals/html');\nvar documentCreateElement = require('../internals/document-create-element');\nvar sharedKey = require('../internals/shared-key');\n\nvar GT = '>';\nvar LT = '<';\nvar PROTOTYPE = 'prototype';\nvar SCRIPT = 'script';\nvar IE_PROTO = sharedKey('IE_PROTO');\n\nvar EmptyConstructor = function () { /* empty */ };\n\nvar scriptTag = function (content) {\n  return LT + SCRIPT + GT + content + LT + '/' + SCRIPT + GT;\n};\n\n// Create object with fake `null` prototype: use ActiveX Object with cleared prototype\nvar NullProtoObjectViaActiveX = function (activeXDocument) {\n  activeXDocument.write(scriptTag(''));\n  activeXDocument.close();\n  var temp = activeXDocument.parentWindow.Object;\n  // eslint-disable-next-line no-useless-assignment -- avoid memory leak\n  activeXDocument = null;\n  return temp;\n};\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar NullProtoObjectViaIFrame = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = documentCreateElement('iframe');\n  var JS = 'java' + SCRIPT + ':';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  html.appendChild(iframe);\n  // https://github.com/zloirock/core-js/issues/475\n  iframe.src = String(JS);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(scriptTag('document.F=Object'));\n  iframeDocument.close();\n  return iframeDocument.F;\n};\n\n// Check for document.domain and active x support\n// No need to use active x approach when document.domain is not set\n// see https://github.com/es-shims/es5-shim/issues/150\n// variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n// avoid IE GC bug\nvar activeXDocument;\nvar NullProtoObject = function () {\n  try {\n    activeXDocument = new ActiveXObject('htmlfile');\n  } catch (error) { /* ignore */ }\n  NullProtoObject = typeof document != 'undefined'\n    ? document.domain && activeXDocument\n      ? NullProtoObjectViaActiveX(activeXDocument) // old IE\n      : NullProtoObjectViaIFrame()\n    : NullProtoObjectViaActiveX(activeXDocument); // WSH\n  var length = enumBugKeys.length;\n  while (length--) delete NullProtoObject[PROTOTYPE][enumBugKeys[length]];\n  return NullProtoObject();\n};\n\nhiddenKeys[IE_PROTO] = true;\n\n// `Object.create` method\n// https://tc39.es/ecma262/#sec-object.create\n// eslint-disable-next-line es/no-object-create -- safe\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    EmptyConstructor[PROTOTYPE] = anObject(O);\n    result = new EmptyConstructor();\n    EmptyConstructor[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = NullProtoObject();\n  return Properties === undefined ? result : definePropertiesModule.f(result, Properties);\n};\n"], "mappings": "AAAA,YAAY;;AACZ;AACA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,uCAAuC,CAAC;AAC7E,IAAIE,WAAW,GAAGF,OAAO,CAAC,4BAA4B,CAAC;AACvD,IAAIG,UAAU,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAII,IAAI,GAAGJ,OAAO,CAAC,mBAAmB,CAAC;AACvC,IAAIK,qBAAqB,GAAGL,OAAO,CAAC,sCAAsC,CAAC;AAC3E,IAAIM,SAAS,GAAGN,OAAO,CAAC,yBAAyB,CAAC;AAElD,IAAIO,EAAE,GAAG,GAAG;AACZ,IAAIC,EAAE,GAAG,GAAG;AACZ,IAAIC,SAAS,GAAG,WAAW;AAC3B,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,QAAQ,GAAGL,SAAS,CAAC,UAAU,CAAC;AAEpC,IAAIM,gBAAgB,GAAG,SAAAA,CAAA,EAAY,CAAE,YAAa;AAElD,IAAIC,SAAS,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACjC,OAAON,EAAE,GAAGE,MAAM,GAAGH,EAAE,GAAGO,OAAO,GAAGN,EAAE,GAAG,GAAG,GAAGE,MAAM,GAAGH,EAAE;AAC5D,CAAC;;AAED;AACA,IAAIQ,yBAAyB,GAAG,SAAAA,CAAUC,eAAe,EAAE;EACzDA,eAAe,CAACC,KAAK,CAACJ,SAAS,CAAC,EAAE,CAAC,CAAC;EACpCG,eAAe,CAACE,KAAK,CAAC,CAAC;EACvB,IAAIC,IAAI,GAAGH,eAAe,CAACI,YAAY,CAACC,MAAM;EAC9C;EACAL,eAAe,GAAG,IAAI;EACtB,OAAOG,IAAI;AACb,CAAC;;AAED;AACA,IAAIG,wBAAwB,GAAG,SAAAA,CAAA,EAAY;EACzC;EACA,IAAIC,MAAM,GAAGlB,qBAAqB,CAAC,QAAQ,CAAC;EAC5C,IAAImB,EAAE,GAAG,MAAM,GAAGd,MAAM,GAAG,GAAG;EAC9B,IAAIe,cAAc;EAClBF,MAAM,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;EAC7BvB,IAAI,CAACwB,WAAW,CAACL,MAAM,CAAC;EACxB;EACAA,MAAM,CAACM,GAAG,GAAGC,MAAM,CAACN,EAAE,CAAC;EACvBC,cAAc,GAAGF,MAAM,CAACQ,aAAa,CAACC,QAAQ;EAC9CP,cAAc,CAACQ,IAAI,CAAC,CAAC;EACrBR,cAAc,CAACR,KAAK,CAACJ,SAAS,CAAC,mBAAmB,CAAC,CAAC;EACpDY,cAAc,CAACP,KAAK,CAAC,CAAC;EACtB,OAAOO,cAAc,CAACS,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAIlB,eAAe;AACnB,IAAImB,eAAe,GAAG,SAAAA,CAAA,EAAY;EAChC,IAAI;IACFnB,eAAe,GAAG,IAAIoB,aAAa,CAAC,UAAU,CAAC;EACjD,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAE;EAClBF,eAAe,GAAG,OAAOH,QAAQ,IAAI,WAAW,GAC5CA,QAAQ,CAACM,MAAM,IAAItB,eAAe,GAChCD,yBAAyB,CAACC,eAAe,CAAC,CAAC;EAAA,EAC3CM,wBAAwB,CAAC,CAAC,GAC5BP,yBAAyB,CAACC,eAAe,CAAC,CAAC,CAAC;EAChD,IAAIuB,MAAM,GAAGrC,WAAW,CAACqC,MAAM;EAC/B,OAAOA,MAAM,EAAE,EAAE,OAAOJ,eAAe,CAAC1B,SAAS,CAAC,CAACP,WAAW,CAACqC,MAAM,CAAC,CAAC;EACvE,OAAOJ,eAAe,CAAC,CAAC;AAC1B,CAAC;AAEDhC,UAAU,CAACQ,QAAQ,CAAC,GAAG,IAAI;;AAE3B;AACA;AACA;AACA6B,MAAM,CAACC,OAAO,GAAGpB,MAAM,CAACqB,MAAM,IAAI,SAASA,MAAMA,CAACC,CAAC,EAAEC,UAAU,EAAE;EAC/D,IAAIC,MAAM;EACV,IAAIF,CAAC,KAAK,IAAI,EAAE;IACd/B,gBAAgB,CAACH,SAAS,CAAC,GAAGV,QAAQ,CAAC4C,CAAC,CAAC;IACzCE,MAAM,GAAG,IAAIjC,gBAAgB,CAAC,CAAC;IAC/BA,gBAAgB,CAACH,SAAS,CAAC,GAAG,IAAI;IAClC;IACAoC,MAAM,CAAClC,QAAQ,CAAC,GAAGgC,CAAC;EACtB,CAAC,MAAME,MAAM,GAAGV,eAAe,CAAC,CAAC;EACjC,OAAOS,UAAU,KAAKE,SAAS,GAAGD,MAAM,GAAG5C,sBAAsB,CAAC8C,CAAC,CAACF,MAAM,EAAED,UAAU,CAAC;AACzF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}