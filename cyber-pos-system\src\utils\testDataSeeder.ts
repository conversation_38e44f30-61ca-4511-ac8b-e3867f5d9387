import { 
  collection, 
  addDoc, 
  serverTimestamp,
  writeBatch,
  doc
} from 'firebase/firestore';
import { db } from '../config/firebase';

// Sample services data
const sampleServices = [
  {
    name: 'Document Printing',
    description: 'Black and white document printing',
    basePrice: 10,
    category: 'Printing',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: [],
  },
  {
    name: 'Color Printing',
    description: 'Full color document printing',
    basePrice: 25,
    category: 'Printing',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: [],
  },
  {
    name: 'Document Scanning',
    description: 'Scan documents to PDF',
    basePrice: 15,
    category: 'Scanning',
    isActive: true,
    allowPriceOverride: false,
    bundledServices: [],
  },
  {
    name: 'Typing Services',
    description: 'Professional document typing',
    basePrice: 50,
    category: 'Typing',
    isActive: true,
    allowPriceOverride: true,
    bundledServices: [],
  },
  {
    name: 'Email Services',
    description: 'Email sending and receiving',
    basePrice: 20,
    category: 'Internet',
    isActive: true,
    allowPriceOverride: false,
    bundledServices: [],
  },
  {
    name: 'Internet Browsing',
    description: 'Internet access per hour',
    basePrice: 100,
    category: 'Internet',
    isActive: true,
    allowPriceOverride: false,
    bundledServices: [],
  },
];

// Sample products data
const sampleProducts = [
  {
    name: 'A4 Paper (Ream)',
    description: '500 sheets of A4 paper',
    price: 450,
    category: 'Paper',
    stockQuantity: 25,
    reorderLevel: 5,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Blue Pen',
    description: 'Ballpoint pen - blue ink',
    price: 15,
    category: 'Stationery',
    stockQuantity: 100,
    reorderLevel: 20,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Black Pen',
    description: 'Ballpoint pen - black ink',
    price: 15,
    category: 'Stationery',
    stockQuantity: 80,
    reorderLevel: 20,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Pencil',
    description: 'HB pencil',
    price: 10,
    category: 'Stationery',
    stockQuantity: 50,
    reorderLevel: 15,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Eraser',
    description: 'White eraser',
    price: 20,
    category: 'Stationery',
    stockQuantity: 30,
    reorderLevel: 10,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Notebook',
    description: '200 pages ruled notebook',
    price: 120,
    category: 'Books',
    stockQuantity: 15,
    reorderLevel: 5,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Stapler',
    description: 'Desktop stapler',
    price: 350,
    category: 'Office Supplies',
    stockQuantity: 8,
    reorderLevel: 3,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Staples (Box)',
    description: 'Box of 1000 staples',
    price: 80,
    category: 'Office Supplies',
    stockQuantity: 12,
    reorderLevel: 4,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Envelope (Pack)',
    description: 'Pack of 50 envelopes',
    price: 200,
    category: 'Paper',
    stockQuantity: 6,
    reorderLevel: 2,
    hasExpiry: false,
    isActive: true,
  },
  {
    name: 'Highlighter',
    description: 'Yellow highlighter pen',
    price: 35,
    category: 'Stationery',
    stockQuantity: 2,
    reorderLevel: 5,
    hasExpiry: false,
    isActive: true,
  },
];

// Sample transactions data
const generateSampleTransactions = (attendantId: string) => {
  const transactions = [];
  const now = new Date();
  
  // Generate transactions for the last 30 days
  for (let i = 0; i < 30; i++) {
    const transactionDate = new Date(now);
    transactionDate.setDate(now.getDate() - i);
    
    // Generate 2-8 transactions per day
    const transactionsPerDay = Math.floor(Math.random() * 7) + 2;
    
    for (let j = 0; j < transactionsPerDay; j++) {
      const transactionTime = new Date(transactionDate);
      transactionTime.setHours(
        Math.floor(Math.random() * 12) + 8, // 8 AM to 8 PM
        Math.floor(Math.random() * 60),
        Math.floor(Math.random() * 60)
      );
      
      // Random transaction items
      const items = [];
      const numItems = Math.floor(Math.random() * 4) + 1; // 1-4 items
      
      for (let k = 0; k < numItems; k++) {
        const isService = Math.random() > 0.4; // 60% chance of service
        
        if (isService) {
          const service = sampleServices[Math.floor(Math.random() * sampleServices.length)];
          items.push({
            id: `item-${Date.now()}-${k}`,
            type: 'service',
            itemId: `service-${service.name.replace(/\s+/g, '-').toLowerCase()}`,
            name: service.name,
            quantity: 1,
            unitPrice: service.basePrice,
            totalPrice: service.basePrice,
            notes: Math.random() > 0.8 ? 'Special request' : undefined,
          });
        } else {
          const product = sampleProducts[Math.floor(Math.random() * sampleProducts.length)];
          const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity
          items.push({
            id: `item-${Date.now()}-${k}`,
            type: 'product',
            itemId: `product-${product.name.replace(/\s+/g, '-').toLowerCase()}`,
            name: product.name,
            quantity,
            unitPrice: product.price,
            totalPrice: product.price * quantity,
          });
        }
      }
      
      const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
      const discount = Math.random() > 0.8 ? Math.floor(subtotal * 0.1) : 0; // 20% chance of 10% discount
      const total = subtotal - discount;
      
      const paymentMethods = ['cash', 'mpesa', 'debt'];
      const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];
      
      transactions.push({
        items,
        subtotal,
        discount,
        total,
        paymentMethod,
        attendantId,
        status: 'completed',
        notes: Math.random() > 0.9 ? 'Customer notes' : undefined,
        createdAt: transactionTime,
        updatedAt: transactionTime,
      });
    }
  }
  
  return transactions;
};

export const seedTestData = async (attendantId: string) => {
  try {
    console.log('Starting to seed test data...');
    
    // Seed services
    console.log('Seeding services...');
    for (const service of sampleServices) {
      await addDoc(collection(db, 'services'), {
        ...service,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    }
    
    // Seed products
    console.log('Seeding products...');
    for (const product of sampleProducts) {
      await addDoc(collection(db, 'products'), {
        ...product,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    }
    
    // Seed transactions
    console.log('Seeding transactions...');
    const transactions = generateSampleTransactions(attendantId);
    
    // Use batch writes for better performance
    const batchSize = 500;
    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = writeBatch(db);
      const batchTransactions = transactions.slice(i, i + batchSize);
      
      batchTransactions.forEach((transaction) => {
        const docRef = doc(collection(db, 'transactions'));
        batch.set(docRef, {
          ...transaction,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
        });
      });
      
      await batch.commit();
      console.log(`Seeded batch ${Math.floor(i / batchSize) + 1} of transactions`);
    }
    
    console.log('Test data seeding completed successfully!');
    return true;
  } catch (error) {
    console.error('Error seeding test data:', error);
    throw error;
  }
};
