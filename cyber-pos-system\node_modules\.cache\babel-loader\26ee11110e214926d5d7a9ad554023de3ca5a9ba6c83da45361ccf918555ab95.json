{"ast": null, "code": "'use strict';\n\nvar IteratorPrototype = require('../internals/iterators-core').IteratorPrototype;\nvar create = require('../internals/object-create');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar Iterators = require('../internals/iterators');\nvar returnThis = function () {\n  return this;\n};\nmodule.exports = function (IteratorConstructor, NAME, next, ENUMERABLE_NEXT) {\n  var TO_STRING_TAG = NAME + ' Iterator';\n  IteratorConstructor.prototype = create(IteratorPrototype, {\n    next: createPropertyDescriptor(+!ENUMERABLE_NEXT, next)\n  });\n  setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);\n  Iterators[TO_STRING_TAG] = returnThis;\n  return IteratorConstructor;\n};", "map": {"version": 3, "names": ["IteratorPrototype", "require", "create", "createPropertyDescriptor", "setToStringTag", "Iterators", "returnThis", "module", "exports", "IteratorConstructor", "NAME", "next", "ENUMERABLE_NEXT", "TO_STRING_TAG", "prototype"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/iterator-create-constructor.js"], "sourcesContent": ["'use strict';\nvar IteratorPrototype = require('../internals/iterators-core').IteratorPrototype;\nvar create = require('../internals/object-create');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar Iterators = require('../internals/iterators');\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (IteratorConstructor, NAME, next, ENUMERABLE_NEXT) {\n  var TO_STRING_TAG = NAME + ' Iterator';\n  IteratorConstructor.prototype = create(IteratorPrototype, { next: createPropertyDescriptor(+!ENUMERABLE_NEXT, next) });\n  setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);\n  Iterators[TO_STRING_TAG] = returnThis;\n  return IteratorConstructor;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,iBAAiB,GAAGC,OAAO,CAAC,6BAA6B,CAAC,CAACD,iBAAiB;AAChF,IAAIE,MAAM,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAClD,IAAIE,wBAAwB,GAAGF,OAAO,CAAC,yCAAyC,CAAC;AACjF,IAAIG,cAAc,GAAGH,OAAO,CAAC,gCAAgC,CAAC;AAC9D,IAAII,SAAS,GAAGJ,OAAO,CAAC,wBAAwB,CAAC;AAEjD,IAAIK,UAAU,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,IAAI;AAAE,CAAC;AAE7CC,MAAM,CAACC,OAAO,GAAG,UAAUC,mBAAmB,EAAEC,IAAI,EAAEC,IAAI,EAAEC,eAAe,EAAE;EAC3E,IAAIC,aAAa,GAAGH,IAAI,GAAG,WAAW;EACtCD,mBAAmB,CAACK,SAAS,GAAGZ,MAAM,CAACF,iBAAiB,EAAE;IAAEW,IAAI,EAAER,wBAAwB,CAAC,CAAC,CAACS,eAAe,EAAED,IAAI;EAAE,CAAC,CAAC;EACtHP,cAAc,CAACK,mBAAmB,EAAEI,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC;EAC/DR,SAAS,CAACQ,aAAa,CAAC,GAAGP,UAAU;EACrC,OAAOG,mBAAmB;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}