{"ast": null, "code": "'use strict';\n\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar addToUnscopables = require('../internals/add-to-unscopables');\nvar Iterators = require('../internals/iterators');\nvar InternalStateModule = require('../internals/internal-state');\nvar defineProperty = require('../internals/object-define-property').f;\nvar defineIterator = require('../internals/iterator-define');\nvar createIterResultObject = require('../internals/create-iter-result-object');\nvar IS_PURE = require('../internals/is-pure');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar ARRAY_ITERATOR = 'Array Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalState = InternalStateModule.getterFor(ARRAY_ITERATOR);\n\n// `Array.prototype.entries` method\n// https://tc39.es/ecma262/#sec-array.prototype.entries\n// `Array.prototype.keys` method\n// https://tc39.es/ecma262/#sec-array.prototype.keys\n// `Array.prototype.values` method\n// https://tc39.es/ecma262/#sec-array.prototype.values\n// `Array.prototype[@@iterator]` method\n// https://tc39.es/ecma262/#sec-array.prototype-@@iterator\n// `CreateArrayIterator` internal method\n// https://tc39.es/ecma262/#sec-createarrayiterator\nmodule.exports = defineIterator(Array, 'Array', function (iterated, kind) {\n  setInternalState(this, {\n    type: ARRAY_ITERATOR,\n    target: toIndexedObject(iterated),\n    // target\n    index: 0,\n    // next index\n    kind: kind // kind\n  });\n  // `%ArrayIteratorPrototype%.next` method\n  // https://tc39.es/ecma262/#sec-%arrayiteratorprototype%.next\n}, function () {\n  var state = getInternalState(this);\n  var target = state.target;\n  var index = state.index++;\n  if (!target || index >= target.length) {\n    state.target = null;\n    return createIterResultObject(undefined, true);\n  }\n  switch (state.kind) {\n    case 'keys':\n      return createIterResultObject(index, false);\n    case 'values':\n      return createIterResultObject(target[index], false);\n  }\n  return createIterResultObject([index, target[index]], false);\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values%\n// https://tc39.es/ecma262/#sec-createunmappedargumentsobject\n// https://tc39.es/ecma262/#sec-createmappedargumentsobject\nvar values = Iterators.Arguments = Iterators.Array;\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n// V8 ~ Chrome 45- bug\nif (!IS_PURE && DESCRIPTORS && values.name !== 'values') try {\n  defineProperty(values, 'name', {\n    value: 'values'\n  });\n} catch (error) {/* empty */}", "map": {"version": 3, "names": ["toIndexedObject", "require", "addToUnscopables", "Iterators", "InternalStateModule", "defineProperty", "f", "defineIterator", "createIterResultObject", "IS_PURE", "DESCRIPTORS", "ARRAY_ITERATOR", "setInternalState", "set", "getInternalState", "getter<PERSON>or", "module", "exports", "Array", "iterated", "kind", "type", "target", "index", "state", "length", "undefined", "values", "Arguments", "name", "value", "error"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/modules/es.array.iterator.js"], "sourcesContent": ["'use strict';\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar addToUnscopables = require('../internals/add-to-unscopables');\nvar Iterators = require('../internals/iterators');\nvar InternalStateModule = require('../internals/internal-state');\nvar defineProperty = require('../internals/object-define-property').f;\nvar defineIterator = require('../internals/iterator-define');\nvar createIterResultObject = require('../internals/create-iter-result-object');\nvar IS_PURE = require('../internals/is-pure');\nvar DESCRIPTORS = require('../internals/descriptors');\n\nvar ARRAY_ITERATOR = 'Array Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalState = InternalStateModule.getterFor(ARRAY_ITERATOR);\n\n// `Array.prototype.entries` method\n// https://tc39.es/ecma262/#sec-array.prototype.entries\n// `Array.prototype.keys` method\n// https://tc39.es/ecma262/#sec-array.prototype.keys\n// `Array.prototype.values` method\n// https://tc39.es/ecma262/#sec-array.prototype.values\n// `Array.prototype[@@iterator]` method\n// https://tc39.es/ecma262/#sec-array.prototype-@@iterator\n// `CreateArrayIterator` internal method\n// https://tc39.es/ecma262/#sec-createarrayiterator\nmodule.exports = defineIterator(Array, 'Array', function (iterated, kind) {\n  setInternalState(this, {\n    type: ARRAY_ITERATOR,\n    target: toIndexedObject(iterated), // target\n    index: 0,                          // next index\n    kind: kind                         // kind\n  });\n// `%ArrayIteratorPrototype%.next` method\n// https://tc39.es/ecma262/#sec-%arrayiteratorprototype%.next\n}, function () {\n  var state = getInternalState(this);\n  var target = state.target;\n  var index = state.index++;\n  if (!target || index >= target.length) {\n    state.target = null;\n    return createIterResultObject(undefined, true);\n  }\n  switch (state.kind) {\n    case 'keys': return createIterResultObject(index, false);\n    case 'values': return createIterResultObject(target[index], false);\n  } return createIterResultObject([index, target[index]], false);\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values%\n// https://tc39.es/ecma262/#sec-createunmappedargumentsobject\n// https://tc39.es/ecma262/#sec-createmappedargumentsobject\nvar values = Iterators.Arguments = Iterators.Array;\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n// V8 ~ Chrome 45- bug\nif (!IS_PURE && DESCRIPTORS && values.name !== 'values') try {\n  defineProperty(values, 'name', { value: 'values' });\n} catch (error) { /* empty */ }\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,gCAAgC,CAAC;AAC/D,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,iCAAiC,CAAC;AACjE,IAAIE,SAAS,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AACjD,IAAIG,mBAAmB,GAAGH,OAAO,CAAC,6BAA6B,CAAC;AAChE,IAAII,cAAc,GAAGJ,OAAO,CAAC,qCAAqC,CAAC,CAACK,CAAC;AACrE,IAAIC,cAAc,GAAGN,OAAO,CAAC,8BAA8B,CAAC;AAC5D,IAAIO,sBAAsB,GAAGP,OAAO,CAAC,wCAAwC,CAAC;AAC9E,IAAIQ,OAAO,GAAGR,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIS,WAAW,GAAGT,OAAO,CAAC,0BAA0B,CAAC;AAErD,IAAIU,cAAc,GAAG,gBAAgB;AACrC,IAAIC,gBAAgB,GAAGR,mBAAmB,CAACS,GAAG;AAC9C,IAAIC,gBAAgB,GAAGV,mBAAmB,CAACW,SAAS,CAACJ,cAAc,CAAC;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAK,MAAM,CAACC,OAAO,GAAGV,cAAc,CAACW,KAAK,EAAE,OAAO,EAAE,UAAUC,QAAQ,EAAEC,IAAI,EAAE;EACxER,gBAAgB,CAAC,IAAI,EAAE;IACrBS,IAAI,EAAEV,cAAc;IACpBW,MAAM,EAAEtB,eAAe,CAACmB,QAAQ,CAAC;IAAE;IACnCI,KAAK,EAAE,CAAC;IAA2B;IACnCH,IAAI,EAAEA,IAAI,CAAyB;EACrC,CAAC,CAAC;EACJ;EACA;AACA,CAAC,EAAE,YAAY;EACb,IAAII,KAAK,GAAGV,gBAAgB,CAAC,IAAI,CAAC;EAClC,IAAIQ,MAAM,GAAGE,KAAK,CAACF,MAAM;EACzB,IAAIC,KAAK,GAAGC,KAAK,CAACD,KAAK,EAAE;EACzB,IAAI,CAACD,MAAM,IAAIC,KAAK,IAAID,MAAM,CAACG,MAAM,EAAE;IACrCD,KAAK,CAACF,MAAM,GAAG,IAAI;IACnB,OAAOd,sBAAsB,CAACkB,SAAS,EAAE,IAAI,CAAC;EAChD;EACA,QAAQF,KAAK,CAACJ,IAAI;IAChB,KAAK,MAAM;MAAE,OAAOZ,sBAAsB,CAACe,KAAK,EAAE,KAAK,CAAC;IACxD,KAAK,QAAQ;MAAE,OAAOf,sBAAsB,CAACc,MAAM,CAACC,KAAK,CAAC,EAAE,KAAK,CAAC;EACpE;EAAE,OAAOf,sBAAsB,CAAC,CAACe,KAAK,EAAED,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AAChE,CAAC,EAAE,QAAQ,CAAC;;AAEZ;AACA;AACA;AACA,IAAII,MAAM,GAAGxB,SAAS,CAACyB,SAAS,GAAGzB,SAAS,CAACe,KAAK;;AAElD;AACAhB,gBAAgB,CAAC,MAAM,CAAC;AACxBA,gBAAgB,CAAC,QAAQ,CAAC;AAC1BA,gBAAgB,CAAC,SAAS,CAAC;;AAE3B;AACA,IAAI,CAACO,OAAO,IAAIC,WAAW,IAAIiB,MAAM,CAACE,IAAI,KAAK,QAAQ,EAAE,IAAI;EAC3DxB,cAAc,CAACsB,MAAM,EAAE,MAAM,EAAE;IAAEG,KAAK,EAAE;EAAS,CAAC,CAAC;AACrD,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}