{"ast": null, "code": "import { collection, addDoc, serverTimestamp, writeBatch, doc } from 'firebase/firestore';\nimport { db } from '../config/firebase';\n\n// Sample services data\nconst sampleServices = [{\n  name: 'Document Printing',\n  description: 'Black and white document printing',\n  basePrice: 10,\n  category: 'Printing',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: []\n}, {\n  name: 'Color Printing',\n  description: 'Full color document printing',\n  basePrice: 25,\n  category: 'Printing',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: []\n}, {\n  name: 'Document Scanning',\n  description: 'Scan documents to PDF',\n  basePrice: 15,\n  category: 'Scanning',\n  isActive: true,\n  allowPriceOverride: false,\n  bundledServices: []\n}, {\n  name: 'Typing Services',\n  description: 'Professional document typing',\n  basePrice: 50,\n  category: 'Typing',\n  isActive: true,\n  allowPriceOverride: true,\n  bundledServices: []\n}, {\n  name: 'Email Services',\n  description: 'Email sending and receiving',\n  basePrice: 20,\n  category: 'Internet',\n  isActive: true,\n  allowPriceOverride: false,\n  bundledServices: []\n}, {\n  name: 'Internet Browsing',\n  description: 'Internet access per hour',\n  basePrice: 100,\n  category: 'Internet',\n  isActive: true,\n  allowPriceOverride: false,\n  bundledServices: []\n}];\n\n// Sample products data\nconst sampleProducts = [{\n  name: 'A4 Paper (Ream)',\n  description: '500 sheets of A4 paper',\n  price: 450,\n  category: 'Paper',\n  stockQuantity: 25,\n  reorderLevel: 5,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Blue Pen',\n  description: 'Ballpoint pen - blue ink',\n  price: 15,\n  category: 'Stationery',\n  stockQuantity: 100,\n  reorderLevel: 20,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Black Pen',\n  description: 'Ballpoint pen - black ink',\n  price: 15,\n  category: 'Stationery',\n  stockQuantity: 80,\n  reorderLevel: 20,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Pencil',\n  description: 'HB pencil',\n  price: 10,\n  category: 'Stationery',\n  stockQuantity: 50,\n  reorderLevel: 15,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Eraser',\n  description: 'White eraser',\n  price: 20,\n  category: 'Stationery',\n  stockQuantity: 30,\n  reorderLevel: 10,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Notebook',\n  description: '200 pages ruled notebook',\n  price: 120,\n  category: 'Books',\n  stockQuantity: 15,\n  reorderLevel: 5,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Stapler',\n  description: 'Desktop stapler',\n  price: 350,\n  category: 'Office Supplies',\n  stockQuantity: 8,\n  reorderLevel: 3,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Staples (Box)',\n  description: 'Box of 1000 staples',\n  price: 80,\n  category: 'Office Supplies',\n  stockQuantity: 12,\n  reorderLevel: 4,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Envelope (Pack)',\n  description: 'Pack of 50 envelopes',\n  price: 200,\n  category: 'Paper',\n  stockQuantity: 6,\n  reorderLevel: 2,\n  hasExpiry: false,\n  isActive: true\n}, {\n  name: 'Highlighter',\n  description: 'Yellow highlighter pen',\n  price: 35,\n  category: 'Stationery',\n  stockQuantity: 2,\n  reorderLevel: 5,\n  hasExpiry: false,\n  isActive: true\n}];\n\n// Sample transactions data\nconst generateSampleTransactions = attendantId => {\n  const transactions = [];\n  const now = new Date();\n\n  // Generate transactions for the last 30 days\n  for (let i = 0; i < 30; i++) {\n    const transactionDate = new Date(now);\n    transactionDate.setDate(now.getDate() - i);\n\n    // Generate 2-8 transactions per day\n    const transactionsPerDay = Math.floor(Math.random() * 7) + 2;\n    for (let j = 0; j < transactionsPerDay; j++) {\n      const transactionTime = new Date(transactionDate);\n      transactionTime.setHours(Math.floor(Math.random() * 12) + 8,\n      // 8 AM to 8 PM\n      Math.floor(Math.random() * 60), Math.floor(Math.random() * 60));\n\n      // Random transaction items\n      const items = [];\n      const numItems = Math.floor(Math.random() * 4) + 1; // 1-4 items\n\n      for (let k = 0; k < numItems; k++) {\n        const isService = Math.random() > 0.4; // 60% chance of service\n\n        if (isService) {\n          const service = sampleServices[Math.floor(Math.random() * sampleServices.length)];\n          items.push({\n            id: `item-${Date.now()}-${k}`,\n            type: 'service',\n            itemId: `service-${service.name.replace(/\\s+/g, '-').toLowerCase()}`,\n            name: service.name,\n            quantity: 1,\n            unitPrice: service.basePrice,\n            totalPrice: service.basePrice,\n            notes: Math.random() > 0.8 ? 'Special request' : undefined\n          });\n        } else {\n          const product = sampleProducts[Math.floor(Math.random() * sampleProducts.length)];\n          const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity\n          items.push({\n            id: `item-${Date.now()}-${k}`,\n            type: 'product',\n            itemId: `product-${product.name.replace(/\\s+/g, '-').toLowerCase()}`,\n            name: product.name,\n            quantity,\n            unitPrice: product.price,\n            totalPrice: product.price * quantity\n          });\n        }\n      }\n      const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);\n      const discount = Math.random() > 0.8 ? Math.floor(subtotal * 0.1) : 0; // 20% chance of 10% discount\n      const total = subtotal - discount;\n      const paymentMethods = ['cash', 'mpesa', 'debt'];\n      const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];\n      transactions.push({\n        items,\n        subtotal,\n        discount,\n        total,\n        paymentMethod,\n        attendantId,\n        status: 'completed',\n        notes: Math.random() > 0.9 ? 'Customer notes' : undefined,\n        createdAt: transactionTime,\n        updatedAt: transactionTime\n      });\n    }\n  }\n  return transactions;\n};\nexport const seedTestData = async attendantId => {\n  try {\n    console.log('Starting to seed test data...');\n\n    // Seed services\n    console.log('Seeding services...');\n    for (const service of sampleServices) {\n      await addDoc(collection(db, 'services'), {\n        ...service,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n    }\n\n    // Seed products\n    console.log('Seeding products...');\n    for (const product of sampleProducts) {\n      await addDoc(collection(db, 'products'), {\n        ...product,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n    }\n\n    // Seed transactions\n    console.log('Seeding transactions...');\n    const transactions = generateSampleTransactions(attendantId);\n\n    // Use batch writes for better performance\n    const batchSize = 500;\n    for (let i = 0; i < transactions.length; i += batchSize) {\n      const batch = writeBatch(db);\n      const batchTransactions = transactions.slice(i, i + batchSize);\n      batchTransactions.forEach(transaction => {\n        const docRef = doc(collection(db, 'transactions'));\n        batch.set(docRef, {\n          ...transaction,\n          createdAt: transaction.createdAt,\n          updatedAt: transaction.updatedAt\n        });\n      });\n      await batch.commit();\n      console.log(`Seeded batch ${Math.floor(i / batchSize) + 1} of transactions`);\n    }\n    console.log('Test data seeding completed successfully!');\n    return true;\n  } catch (error) {\n    console.error('Error seeding test data:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["collection", "addDoc", "serverTimestamp", "writeBatch", "doc", "db", "sampleServices", "name", "description", "basePrice", "category", "isActive", "allowPriceOverride", "bundledServices", "sampleProducts", "price", "stockQuantity", "reorderLevel", "hasEx<PERSON>ry", "generateSampleTransactions", "attendantId", "transactions", "now", "Date", "i", "transactionDate", "setDate", "getDate", "transactionsPerDay", "Math", "floor", "random", "j", "transactionTime", "setHours", "items", "numItems", "k", "isService", "service", "length", "push", "id", "type", "itemId", "replace", "toLowerCase", "quantity", "unitPrice", "totalPrice", "notes", "undefined", "product", "subtotal", "reduce", "sum", "item", "discount", "total", "paymentMethods", "paymentMethod", "status", "createdAt", "updatedAt", "seedTestData", "console", "log", "batchSize", "batch", "batchTransactions", "slice", "for<PERSON>ach", "transaction", "doc<PERSON>ef", "set", "commit", "error"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/testDataSeeder.ts"], "sourcesContent": ["import { \n  collection, \n  addDoc, \n  serverTimestamp,\n  writeBatch,\n  doc\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\n\n// Sample services data\nconst sampleServices = [\n  {\n    name: 'Document Printing',\n    description: 'Black and white document printing',\n    basePrice: 10,\n    category: 'Printing',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: [],\n  },\n  {\n    name: 'Color Printing',\n    description: 'Full color document printing',\n    basePrice: 25,\n    category: 'Printing',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: [],\n  },\n  {\n    name: 'Document Scanning',\n    description: 'Scan documents to PDF',\n    basePrice: 15,\n    category: 'Scanning',\n    isActive: true,\n    allowPriceOverride: false,\n    bundledServices: [],\n  },\n  {\n    name: 'Typing Services',\n    description: 'Professional document typing',\n    basePrice: 50,\n    category: 'Typing',\n    isActive: true,\n    allowPriceOverride: true,\n    bundledServices: [],\n  },\n  {\n    name: 'Email Services',\n    description: 'Email sending and receiving',\n    basePrice: 20,\n    category: 'Internet',\n    isActive: true,\n    allowPriceOverride: false,\n    bundledServices: [],\n  },\n  {\n    name: 'Internet Browsing',\n    description: 'Internet access per hour',\n    basePrice: 100,\n    category: 'Internet',\n    isActive: true,\n    allowPriceOverride: false,\n    bundledServices: [],\n  },\n];\n\n// Sample products data\nconst sampleProducts = [\n  {\n    name: 'A4 Paper (Ream)',\n    description: '500 sheets of A4 paper',\n    price: 450,\n    category: 'Paper',\n    stockQuantity: 25,\n    reorderLevel: 5,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Blue Pen',\n    description: 'Ballpoint pen - blue ink',\n    price: 15,\n    category: 'Stationery',\n    stockQuantity: 100,\n    reorderLevel: 20,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Black Pen',\n    description: 'Ballpoint pen - black ink',\n    price: 15,\n    category: 'Stationery',\n    stockQuantity: 80,\n    reorderLevel: 20,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Pencil',\n    description: 'HB pencil',\n    price: 10,\n    category: 'Stationery',\n    stockQuantity: 50,\n    reorderLevel: 15,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Eraser',\n    description: 'White eraser',\n    price: 20,\n    category: 'Stationery',\n    stockQuantity: 30,\n    reorderLevel: 10,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Notebook',\n    description: '200 pages ruled notebook',\n    price: 120,\n    category: 'Books',\n    stockQuantity: 15,\n    reorderLevel: 5,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Stapler',\n    description: 'Desktop stapler',\n    price: 350,\n    category: 'Office Supplies',\n    stockQuantity: 8,\n    reorderLevel: 3,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Staples (Box)',\n    description: 'Box of 1000 staples',\n    price: 80,\n    category: 'Office Supplies',\n    stockQuantity: 12,\n    reorderLevel: 4,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Envelope (Pack)',\n    description: 'Pack of 50 envelopes',\n    price: 200,\n    category: 'Paper',\n    stockQuantity: 6,\n    reorderLevel: 2,\n    hasExpiry: false,\n    isActive: true,\n  },\n  {\n    name: 'Highlighter',\n    description: 'Yellow highlighter pen',\n    price: 35,\n    category: 'Stationery',\n    stockQuantity: 2,\n    reorderLevel: 5,\n    hasExpiry: false,\n    isActive: true,\n  },\n];\n\n// Sample transactions data\nconst generateSampleTransactions = (attendantId: string) => {\n  const transactions = [];\n  const now = new Date();\n  \n  // Generate transactions for the last 30 days\n  for (let i = 0; i < 30; i++) {\n    const transactionDate = new Date(now);\n    transactionDate.setDate(now.getDate() - i);\n    \n    // Generate 2-8 transactions per day\n    const transactionsPerDay = Math.floor(Math.random() * 7) + 2;\n    \n    for (let j = 0; j < transactionsPerDay; j++) {\n      const transactionTime = new Date(transactionDate);\n      transactionTime.setHours(\n        Math.floor(Math.random() * 12) + 8, // 8 AM to 8 PM\n        Math.floor(Math.random() * 60),\n        Math.floor(Math.random() * 60)\n      );\n      \n      // Random transaction items\n      const items = [];\n      const numItems = Math.floor(Math.random() * 4) + 1; // 1-4 items\n      \n      for (let k = 0; k < numItems; k++) {\n        const isService = Math.random() > 0.4; // 60% chance of service\n        \n        if (isService) {\n          const service = sampleServices[Math.floor(Math.random() * sampleServices.length)];\n          items.push({\n            id: `item-${Date.now()}-${k}`,\n            type: 'service',\n            itemId: `service-${service.name.replace(/\\s+/g, '-').toLowerCase()}`,\n            name: service.name,\n            quantity: 1,\n            unitPrice: service.basePrice,\n            totalPrice: service.basePrice,\n            notes: Math.random() > 0.8 ? 'Special request' : undefined,\n          });\n        } else {\n          const product = sampleProducts[Math.floor(Math.random() * sampleProducts.length)];\n          const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity\n          items.push({\n            id: `item-${Date.now()}-${k}`,\n            type: 'product',\n            itemId: `product-${product.name.replace(/\\s+/g, '-').toLowerCase()}`,\n            name: product.name,\n            quantity,\n            unitPrice: product.price,\n            totalPrice: product.price * quantity,\n          });\n        }\n      }\n      \n      const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);\n      const discount = Math.random() > 0.8 ? Math.floor(subtotal * 0.1) : 0; // 20% chance of 10% discount\n      const total = subtotal - discount;\n      \n      const paymentMethods = ['cash', 'mpesa', 'debt'];\n      const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];\n      \n      transactions.push({\n        items,\n        subtotal,\n        discount,\n        total,\n        paymentMethod,\n        attendantId,\n        status: 'completed',\n        notes: Math.random() > 0.9 ? 'Customer notes' : undefined,\n        createdAt: transactionTime,\n        updatedAt: transactionTime,\n      });\n    }\n  }\n  \n  return transactions;\n};\n\nexport const seedTestData = async (attendantId: string) => {\n  try {\n    console.log('Starting to seed test data...');\n    \n    // Seed services\n    console.log('Seeding services...');\n    for (const service of sampleServices) {\n      await addDoc(collection(db, 'services'), {\n        ...service,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n    }\n    \n    // Seed products\n    console.log('Seeding products...');\n    for (const product of sampleProducts) {\n      await addDoc(collection(db, 'products'), {\n        ...product,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n    }\n    \n    // Seed transactions\n    console.log('Seeding transactions...');\n    const transactions = generateSampleTransactions(attendantId);\n    \n    // Use batch writes for better performance\n    const batchSize = 500;\n    for (let i = 0; i < transactions.length; i += batchSize) {\n      const batch = writeBatch(db);\n      const batchTransactions = transactions.slice(i, i + batchSize);\n      \n      batchTransactions.forEach((transaction) => {\n        const docRef = doc(collection(db, 'transactions'));\n        batch.set(docRef, {\n          ...transaction,\n          createdAt: transaction.createdAt,\n          updatedAt: transaction.updatedAt,\n        });\n      });\n      \n      await batch.commit();\n      console.log(`Seeded batch ${Math.floor(i / batchSize) + 1} of transactions`);\n    }\n    \n    console.log('Test data seeding completed successfully!');\n    return true;\n  } catch (error) {\n    console.error('Error seeding test data:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,SACEA,UAAU,EACVC,MAAM,EACNC,eAAe,EACfC,UAAU,EACVC,GAAG,QACE,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,oBAAoB;;AAEvC;AACA,MAAMC,cAAc,GAAG,CACrB;EACEC,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,KAAK;EACzBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,KAAK;EACzBC,eAAe,EAAE;AACnB,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,0BAA0B;EACvCC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,KAAK;EACzBC,eAAe,EAAE;AACnB,CAAC,CACF;;AAED;AACA,MAAMC,cAAc,GAAG,CACrB;EACEP,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,wBAAwB;EACrCO,KAAK,EAAE,GAAG;EACVL,QAAQ,EAAE,OAAO;EACjBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE,0BAA0B;EACvCO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,YAAY;EACtBM,aAAa,EAAE,GAAG;EAClBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,2BAA2B;EACxCO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,YAAY;EACtBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,WAAW,EAAE,WAAW;EACxBO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,YAAY;EACtBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,WAAW,EAAE,cAAc;EAC3BO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,YAAY;EACtBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,UAAU;EAChBC,WAAW,EAAE,0BAA0B;EACvCO,KAAK,EAAE,GAAG;EACVL,QAAQ,EAAE,OAAO;EACjBM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,iBAAiB;EAC9BO,KAAK,EAAE,GAAG;EACVL,QAAQ,EAAE,iBAAiB;EAC3BM,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,eAAe;EACrBC,WAAW,EAAE,qBAAqB;EAClCO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,iBAAiB;EAC3BM,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,sBAAsB;EACnCO,KAAK,EAAE,GAAG;EACVL,QAAQ,EAAE,OAAO;EACjBM,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,IAAI,EAAE,aAAa;EACnBC,WAAW,EAAE,wBAAwB;EACrCO,KAAK,EAAE,EAAE;EACTL,QAAQ,EAAE,YAAY;EACtBM,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,KAAK;EAChBP,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,MAAMQ,0BAA0B,GAAIC,WAAmB,IAAK;EAC1D,MAAMC,YAAY,GAAG,EAAE;EACvB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;;EAEtB;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3B,MAAMC,eAAe,GAAG,IAAIF,IAAI,CAACD,GAAG,CAAC;IACrCG,eAAe,CAACC,OAAO,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;;IAE1C;IACA,MAAMI,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAE5D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,kBAAkB,EAAEI,CAAC,EAAE,EAAE;MAC3C,MAAMC,eAAe,GAAG,IAAIV,IAAI,CAACE,eAAe,CAAC;MACjDQ,eAAe,CAACC,QAAQ,CACtBL,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;MAAE;MACpCF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAC9BF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAC/B,CAAC;;MAED;MACA,MAAMI,KAAK,GAAG,EAAE;MAChB,MAAMC,QAAQ,GAAGP,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;MAEpD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;QACjC,MAAMC,SAAS,GAAGT,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;;QAEvC,IAAIO,SAAS,EAAE;UACb,MAAMC,OAAO,GAAGjC,cAAc,CAACuB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGzB,cAAc,CAACkC,MAAM,CAAC,CAAC;UACjFL,KAAK,CAACM,IAAI,CAAC;YACTC,EAAE,EAAE,QAAQnB,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIe,CAAC,EAAE;YAC7BM,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,WAAWL,OAAO,CAAChC,IAAI,CAACsC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;YACpEvC,IAAI,EAAEgC,OAAO,CAAChC,IAAI;YAClBwC,QAAQ,EAAE,CAAC;YACXC,SAAS,EAAET,OAAO,CAAC9B,SAAS;YAC5BwC,UAAU,EAAEV,OAAO,CAAC9B,SAAS;YAC7ByC,KAAK,EAAErB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,iBAAiB,GAAGoB;UACnD,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,MAAMC,OAAO,GAAGtC,cAAc,CAACe,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGjB,cAAc,CAAC0B,MAAM,CAAC,CAAC;UACjF,MAAMO,QAAQ,GAAGlB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACpDI,KAAK,CAACM,IAAI,CAAC;YACTC,EAAE,EAAE,QAAQnB,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIe,CAAC,EAAE;YAC7BM,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,WAAWQ,OAAO,CAAC7C,IAAI,CAACsC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;YACpEvC,IAAI,EAAE6C,OAAO,CAAC7C,IAAI;YAClBwC,QAAQ;YACRC,SAAS,EAAEI,OAAO,CAACrC,KAAK;YACxBkC,UAAU,EAAEG,OAAO,CAACrC,KAAK,GAAGgC;UAC9B,CAAC,CAAC;QACJ;MACF;MAEA,MAAMM,QAAQ,GAAGlB,KAAK,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACP,UAAU,EAAE,CAAC,CAAC;MACtE,MAAMQ,QAAQ,GAAG5B,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGF,IAAI,CAACC,KAAK,CAACuB,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACvE,MAAMK,KAAK,GAAGL,QAAQ,GAAGI,QAAQ;MAEjC,MAAME,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;MAChD,MAAMC,aAAa,GAAGD,cAAc,CAAC9B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG4B,cAAc,CAACnB,MAAM,CAAC,CAAC;MAEvFnB,YAAY,CAACoB,IAAI,CAAC;QAChBN,KAAK;QACLkB,QAAQ;QACRI,QAAQ;QACRC,KAAK;QACLE,aAAa;QACbxC,WAAW;QACXyC,MAAM,EAAE,WAAW;QACnBX,KAAK,EAAErB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,gBAAgB,GAAGoB,SAAS;QACzDW,SAAS,EAAE7B,eAAe;QAC1B8B,SAAS,EAAE9B;MACb,CAAC,CAAC;IACJ;EACF;EAEA,OAAOZ,YAAY;AACrB,CAAC;AAED,OAAO,MAAM2C,YAAY,GAAG,MAAO5C,WAAmB,IAAK;EACzD,IAAI;IACF6C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;IAE5C;IACAD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,KAAK,MAAM3B,OAAO,IAAIjC,cAAc,EAAE;MACpC,MAAML,MAAM,CAACD,UAAU,CAACK,EAAE,EAAE,UAAU,CAAC,EAAE;QACvC,GAAGkC,OAAO;QACVuB,SAAS,EAAE5D,eAAe,CAAC,CAAC;QAC5B6D,SAAS,EAAE7D,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ;;IAEA;IACA+D,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,KAAK,MAAMd,OAAO,IAAItC,cAAc,EAAE;MACpC,MAAMb,MAAM,CAACD,UAAU,CAACK,EAAE,EAAE,UAAU,CAAC,EAAE;QACvC,GAAG+C,OAAO;QACVU,SAAS,EAAE5D,eAAe,CAAC,CAAC;QAC5B6D,SAAS,EAAE7D,eAAe,CAAC;MAC7B,CAAC,CAAC;IACJ;;IAEA;IACA+D,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC,MAAM7C,YAAY,GAAGF,0BAA0B,CAACC,WAAW,CAAC;;IAE5D;IACA,MAAM+C,SAAS,GAAG,GAAG;IACrB,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,CAACmB,MAAM,EAAEhB,CAAC,IAAI2C,SAAS,EAAE;MACvD,MAAMC,KAAK,GAAGjE,UAAU,CAACE,EAAE,CAAC;MAC5B,MAAMgE,iBAAiB,GAAGhD,YAAY,CAACiD,KAAK,CAAC9C,CAAC,EAAEA,CAAC,GAAG2C,SAAS,CAAC;MAE9DE,iBAAiB,CAACE,OAAO,CAAEC,WAAW,IAAK;QACzC,MAAMC,MAAM,GAAGrE,GAAG,CAACJ,UAAU,CAACK,EAAE,EAAE,cAAc,CAAC,CAAC;QAClD+D,KAAK,CAACM,GAAG,CAACD,MAAM,EAAE;UAChB,GAAGD,WAAW;UACdV,SAAS,EAAEU,WAAW,CAACV,SAAS;UAChCC,SAAS,EAAES,WAAW,CAACT;QACzB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMK,KAAK,CAACO,MAAM,CAAC,CAAC;MACpBV,OAAO,CAACC,GAAG,CAAC,gBAAgBrC,IAAI,CAACC,KAAK,CAACN,CAAC,GAAG2C,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAC9E;IAEAF,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxD,OAAO,IAAI;EACb,CAAC,CAAC,OAAOU,KAAK,EAAE;IACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}