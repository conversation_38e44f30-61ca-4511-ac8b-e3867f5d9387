{"ast": null, "code": "'use strict';\n\n// `CreateIterResultObject` abstract operation\n// https://tc39.es/ecma262/#sec-createiterresultobject\nmodule.exports = function (value, done) {\n  return {\n    value: value,\n    done: done\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "value", "done"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/create-iter-result-object.js"], "sourcesContent": ["'use strict';\n// `CreateIterResultObject` abstract operation\n// https://tc39.es/ecma262/#sec-createiterresultobject\nmodule.exports = function (value, done) {\n  return { value: value, done: done };\n};\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAE;EACtC,OAAO;IAAED,KAAK,EAAEA,KAAK;IAAEC,IAAI,EAAEA;EAAK,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}