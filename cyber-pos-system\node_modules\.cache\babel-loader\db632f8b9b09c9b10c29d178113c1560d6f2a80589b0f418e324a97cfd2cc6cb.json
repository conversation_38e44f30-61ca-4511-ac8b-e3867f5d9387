{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\reports\\\\Reports.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BarChart3, Download, Calendar, TrendingUp, DollarSign, Users, Package, CreditCard, RefreshCw } from 'lucide-react';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, LineElement, PointElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Line, Doughnut } from 'react-chartjs-2';\nimport { useReports } from '../../hooks/useReports';\nimport { useAuth } from '../../contexts/AuthContext';\nimport InventoryAnalytics from './InventoryAnalytics';\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\n\n// Register Chart.js components\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, LineElement, PointElement, Title, Tooltip, Legend, ArcElement);\nconst Reports = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAuth();\n  const {\n    generateSalesReport,\n    loading\n  } = useReports();\n  const [selectedPeriod, setSelectedPeriod] = useState('week');\n  const [salesReport, setSalesReport] = useState(null);\n  const [customDateRange, setCustomDateRange] = useState({\n    start: '',\n    end: ''\n  });\n  const [activeTab, setActiveTab] = useState('sales');\n\n  // Load initial report\n  useEffect(() => {\n    loadReport();\n  }, [selectedPeriod]);\n  const loadReport = async () => {\n    try {\n      const report = await generateSalesReport(selectedPeriod);\n      setSalesReport(report);\n    } catch (error) {\n      console.error('Error loading report:', error);\n    }\n  };\n  const handleCustomDateRange = async () => {\n    if (customDateRange.start && customDateRange.end) {\n      try {\n        const report = await generateSalesReport('custom', new Date(customDateRange.start), new Date(customDateRange.end));\n        setSalesReport(report);\n      } catch (error) {\n        console.error('Error loading custom report:', error);\n      }\n    }\n  };\n  const exportCSV = () => {\n    if (!salesReport) return;\n\n    // Create CSV content\n    const csvContent = [['Metric', 'Value'], ['Total Sales', `KSh ${salesReport.metrics.totalSales.toLocaleString()}`], ['Total Transactions', salesReport.metrics.totalTransactions.toString()], ['Average Transaction Value', `KSh ${salesReport.metrics.averageTransactionValue.toFixed(2)}`], ['Cash Sales', `KSh ${salesReport.metrics.cashSales.toLocaleString()}`], ['M-PESA Sales', `KSh ${salesReport.metrics.mpesaSales.toLocaleString()}`], ['Credit Sales', `KSh ${salesReport.metrics.creditSales.toLocaleString()}`], [], ['Top Products', ''], ...salesReport.topProducts.map(p => [p.name, `KSh ${p.revenue.toLocaleString()}`]), [], ['Top Services', ''], ...salesReport.topServices.map(s => [s.name, `KSh ${s.revenue.toLocaleString()}`])].map(row => row.join(',')).join('\\n');\n\n    // Download CSV\n    const blob = new Blob([csvContent], {\n      type: 'text/csv'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `sales-report-${salesReport.period}-${new Date().toISOString().split('T')[0]}.csv`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    window.URL.revokeObjectURL(url);\n  };\n  const exportPDF = async () => {\n    if (!salesReport) return;\n    try {\n      // Create a temporary div with the report content\n      const reportElement = document.createElement('div');\n      reportElement.innerHTML = `\n        <div style=\"padding: 20px; font-family: Arial, sans-serif;\">\n          <h1 style=\"color: #2563eb; margin-bottom: 20px;\">Sales Report - ${salesReport.period}</h1>\n          <p style=\"margin-bottom: 20px;\">Generated on: ${new Date().toLocaleDateString()}</p>\n\n          <h2 style=\"color: #374151; margin: 20px 0 10px 0;\">Key Metrics</h2>\n          <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\">\n            <tr style=\"background-color: #f3f4f6;\">\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Total Sales</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.totalSales.toLocaleString()}</td>\n            </tr>\n            <tr>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Total Transactions</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${salesReport.metrics.totalTransactions}</td>\n            </tr>\n            <tr style=\"background-color: #f3f4f6;\">\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Average Transaction Value</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.averageTransactionValue.toFixed(2)}</td>\n            </tr>\n          </table>\n\n          <h2 style=\"color: #374151; margin: 20px 0 10px 0;\">Payment Methods</h2>\n          <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\">\n            <tr style=\"background-color: #f3f4f6;\">\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Cash</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.cashSales.toLocaleString()}</td>\n            </tr>\n            <tr>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">M-PESA</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.mpesaSales.toLocaleString()}</td>\n            </tr>\n            <tr style=\"background-color: #f3f4f6;\">\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Credit</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.creditSales.toLocaleString()}</td>\n            </tr>\n          </table>\n\n          <h2 style=\"color: #374151; margin: 20px 0 10px 0;\">Top Products</h2>\n          <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\">\n            <tr style=\"background-color: #f3f4f6;\">\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Product</th>\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Quantity</th>\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Revenue</th>\n            </tr>\n            ${salesReport.topProducts.slice(0, 5).map(p => `\n              <tr>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${p.name}</td>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${p.quantitySold}</td>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${p.revenue.toLocaleString()}</td>\n              </tr>\n            `).join('')}\n          </table>\n\n          <h2 style=\"color: #374151; margin: 20px 0 10px 0;\">Top Services</h2>\n          <table style=\"width: 100%; border-collapse: collapse;\">\n            <tr style=\"background-color: #f3f4f6;\">\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Service</th>\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Times Sold</th>\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Revenue</th>\n            </tr>\n            ${salesReport.topServices.slice(0, 5).map(s => `\n              <tr>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${s.name}</td>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${s.timesSold}</td>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${s.revenue.toLocaleString()}</td>\n              </tr>\n            `).join('')}\n          </table>\n        </div>\n      `;\n\n      // Temporarily add to DOM\n      document.body.appendChild(reportElement);\n\n      // Convert to canvas\n      const canvas = await html2canvas(reportElement, {\n        scale: 2,\n        useCORS: true,\n        allowTaint: true\n      });\n\n      // Remove from DOM\n      document.body.removeChild(reportElement);\n\n      // Create PDF\n      const imgData = canvas.toDataURL('image/png');\n      const pdf = new jsPDF('p', 'mm', 'a4');\n      const imgWidth = 210;\n      const pageHeight = 295;\n      const imgHeight = canvas.height * imgWidth / canvas.width;\n      let heightLeft = imgHeight;\n      let position = 0;\n      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\n      heightLeft -= pageHeight;\n      while (heightLeft >= 0) {\n        position = heightLeft - imgHeight;\n        pdf.addPage();\n        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\n        heightLeft -= pageHeight;\n      }\n      pdf.save(`sales-report-${salesReport.period}-${new Date().toISOString().split('T')[0]}.pdf`);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Error generating PDF. Please try again.');\n    }\n  };\n  if (!hasPermission(['admin', 'attendant'])) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(Users, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: \"You don't have permission to view reports.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"h-6 w-6 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Reports & Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: loadReport,\n            disabled: loading,\n            className: \"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center disabled:opacity-50\",\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: `h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), \"Refresh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: exportCSV,\n            disabled: !salesReport || activeTab !== 'sales',\n            className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center disabled:opacity-50\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), \"Export CSV\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: exportPDF,\n            disabled: !salesReport || activeTab !== 'sales',\n            className: \"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center disabled:opacity-50\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), \"Export PDF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('sales'),\n          className: `px-4 py-2 rounded-lg text-sm font-medium ${activeTab === 'sales' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n          children: \"Sales Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('inventory'),\n          className: `px-4 py-2 rounded-lg text-sm font-medium ${activeTab === 'inventory' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n          children: \"Inventory Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), activeTab === 'sales' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap items-center gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"h-4 w-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: \"Period:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: ['today', 'week', 'month', 'year'].map(period => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPeriod(period),\n            className: `px-3 py-1 rounded-md text-sm font-medium ${selectedPeriod === period ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n            children: period.charAt(0).toUpperCase() + period.slice(1)\n          }, period, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 ml-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: customDateRange.start,\n            onChange: e => setCustomDateRange(prev => ({\n              ...prev,\n              start: e.target.value\n            })),\n            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500\",\n            children: \"to\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: customDateRange.end,\n            onChange: e => setCustomDateRange(prev => ({\n              ...prev,\n              end: e.target.value\n            })),\n            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCustomDateRange,\n            disabled: !customDateRange.start || !customDateRange.end,\n            className: \"bg-primary-600 text-white px-3 py-1 rounded-md text-sm hover:bg-primary-700 disabled:opacity-50\",\n            children: \"Apply\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), activeTab === 'inventory' ? /*#__PURE__*/_jsxDEV(InventoryAnalytics, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            className: \"h-8 w-8 text-primary-600 animate-spin mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg text-gray-600\",\n            children: \"Generating report...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), !loading && !salesReport && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"mx-auto h-12 w-12 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-900\",\n            children: \"No Data Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500\",\n            children: \"No transactions found for the selected period.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), !loading && salesReport && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                  className: \"h-8 w-8 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Total Sales\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: [\"KSh \", salesReport.metrics.totalSales.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(BarChart3, {\n                  className: \"h-8 w-8 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Transactions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: salesReport.metrics.totalTransactions\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-8 w-8 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Avg. Transaction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: [\"KSh \", salesReport.metrics.averageTransactionValue.toFixed(0)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(CreditCard, {\n                  className: \"h-8 w-8 text-orange-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Payment Methods\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [\"Cash: KSh \", salesReport.metrics.cashSales.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [\"M-PESA: KSh \", salesReport.metrics.mpesaSales.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Daily Sales Trend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-64\",\n              children: /*#__PURE__*/_jsxDEV(Line, {\n                data: {\n                  labels: salesReport.dailyBreakdown.map(d => new Date(d.date).toLocaleDateString('en-US', {\n                    month: 'short',\n                    day: 'numeric'\n                  })),\n                  datasets: [{\n                    label: 'Sales (KSh)',\n                    data: salesReport.dailyBreakdown.map(d => d.sales),\n                    borderColor: 'rgb(37, 99, 235)',\n                    backgroundColor: 'rgba(37, 99, 235, 0.1)',\n                    tension: 0.1\n                  }]\n                },\n                options: {\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      position: 'top'\n                    }\n                  },\n                  scales: {\n                    y: {\n                      beginAtZero: true,\n                      ticks: {\n                        callback: function (value) {\n                          return 'KSh ' + Number(value).toLocaleString();\n                        }\n                      }\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Payment Methods\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-64\",\n              children: /*#__PURE__*/_jsxDEV(Doughnut, {\n                data: {\n                  labels: salesReport.paymentMethodBreakdown.map(p => {\n                    switch (p.method) {\n                      case 'cash':\n                        return 'Cash';\n                      case 'mpesa':\n                        return 'M-PESA';\n                      case 'debt':\n                        return 'Credit';\n                      default:\n                        return p.method;\n                    }\n                  }),\n                  datasets: [{\n                    data: salesReport.paymentMethodBreakdown.map(p => p.amount),\n                    backgroundColor: ['rgba(34, 197, 94, 0.8)', 'rgba(59, 130, 246, 0.8)', 'rgba(249, 115, 22, 0.8)'],\n                    borderColor: ['rgba(34, 197, 94, 1)', 'rgba(59, 130, 246, 1)', 'rgba(249, 115, 22, 1)'],\n                    borderWidth: 2\n                  }]\n                },\n                options: {\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      position: 'bottom'\n                    },\n                    tooltip: {\n                      callbacks: {\n                        label: function (context) {\n                          const value = context.parsed;\n                          const percentage = salesReport.paymentMethodBreakdown[context.dataIndex].percentage;\n                          return `${context.label}: KSh ${value.toLocaleString()} (${percentage.toFixed(1)}%)`;\n                        }\n                      }\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Package, {\n                className: \"h-5 w-5 mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this), \"Top Products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: salesReport.topProducts.length > 0 ? salesReport.topProducts.slice(0, 5).map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-500 w-6\",\n                    children: [index + 1, \".\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [\"Qty: \", product.quantitySold]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-green-600\",\n                  children: [\"KSh \", product.revenue.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 23\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 21\n              }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"No product sales in this period\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n                className: \"h-5 w-5 mr-2 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 17\n              }, this), \"Top Services\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: salesReport.topServices.length > 0 ? salesReport.topServices.slice(0, 5).map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-500 w-6\",\n                    children: [index + 1, \".\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: service.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [\"Times: \", service.timesSold]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-green-600\",\n                  children: [\"KSh \", service.revenue.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 23\n                }, this)]\n              }, service.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 21\n              }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"No service sales in this period\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Users, {\n                className: \"h-5 w-5 mr-2 text-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 17\n              }, this), \"Staff Performance\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: salesReport.attendantPerformance.length > 0 ? salesReport.attendantPerformance.slice(0, 5).map((attendant, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-500 w-6\",\n                    children: [index + 1, \".\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: attendant.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [attendant.transactionCount, \" transactions\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-green-600\",\n                    children: [\"KSh \", attendant.totalSales.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\"Avg: KSh \", attendant.averageTransactionValue.toFixed(0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 23\n                }, this)]\n              }, attendant.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 21\n              }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"No staff data available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"D2HDrTFRLOJM65fHitLALghiYdw=\", false, function () {\n  return [useAuth, useReports];\n});\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "BarChart3", "Download", "Calendar", "TrendingUp", "DollarSign", "Users", "Package", "CreditCard", "RefreshCw", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "LineElement", "PointElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Line", "Doughnut", "useReports", "useAuth", "InventoryAnalytics", "jsPDF", "html2canvas", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "Reports", "_s", "hasPermission", "generateSalesReport", "loading", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "salesReport", "setSalesReport", "customDateRange", "setCustomDateRange", "start", "end", "activeTab", "setActiveTab", "loadReport", "report", "error", "console", "handleCustomDateRange", "Date", "exportCSV", "csv<PERSON><PERSON>nt", "metrics", "totalSales", "toLocaleString", "totalTransactions", "toString", "averageTransactionValue", "toFixed", "cashSales", "mpesaSales", "creditSales", "topProducts", "map", "p", "name", "revenue", "topServices", "s", "row", "join", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "period", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "exportPDF", "reportElement", "innerHTML", "toLocaleDateString", "slice", "quantitySold", "timesSold", "canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "imgData", "toDataURL", "pdf", "imgWidth", "pageHeight", "imgHeight", "height", "width", "heightLeft", "position", "addImage", "addPage", "save", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "char<PERSON>t", "toUpperCase", "value", "onChange", "e", "prev", "target", "data", "labels", "dailyBreakdown", "d", "date", "month", "day", "datasets", "label", "sales", "borderColor", "backgroundColor", "tension", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "scales", "y", "beginAtZero", "ticks", "callback", "Number", "paymentMethodBreakdown", "method", "amount", "borderWidth", "tooltip", "callbacks", "context", "parsed", "percentage", "dataIndex", "length", "product", "index", "id", "service", "attendantPerformance", "attendant", "transactionCount", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/reports/Reports.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  BarChart3,\n  Download,\n  Calendar,\n  TrendingUp,\n  DollarSign,\n  Users,\n  Package,\n  CreditCard,\n  Filter,\n  RefreshCw\n} from 'lucide-react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport { Bar, Line, Doughnut } from 'react-chartjs-2';\nimport { useReports, SalesReport } from '../../hooks/useReports';\nimport { useAuth } from '../../contexts/AuthContext';\nimport InventoryAnalytics from './InventoryAnalytics';\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\nconst Reports: React.FC = () => {\n  const { hasPermission } = useAuth();\n  const { generateSalesReport, loading } = useReports();\n  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'year'>('week');\n  const [salesReport, setSalesReport] = useState<SalesReport | null>(null);\n  const [customDateRange, setCustomDateRange] = useState({ start: '', end: '' });\n  const [activeTab, setActiveTab] = useState<'sales' | 'inventory'>('sales');\n\n  // Load initial report\n  useEffect(() => {\n    loadReport();\n  }, [selectedPeriod]);\n\n  const loadReport = async () => {\n    try {\n      const report = await generateSalesReport(selectedPeriod);\n      setSalesReport(report);\n    } catch (error) {\n      console.error('Error loading report:', error);\n    }\n  };\n\n  const handleCustomDateRange = async () => {\n    if (customDateRange.start && customDateRange.end) {\n      try {\n        const report = await generateSalesReport(\n          'custom',\n          new Date(customDateRange.start),\n          new Date(customDateRange.end)\n        );\n        setSalesReport(report);\n      } catch (error) {\n        console.error('Error loading custom report:', error);\n      }\n    }\n  };\n\n  const exportCSV = () => {\n    if (!salesReport) return;\n\n    // Create CSV content\n    const csvContent = [\n      ['Metric', 'Value'],\n      ['Total Sales', `KSh ${salesReport.metrics.totalSales.toLocaleString()}`],\n      ['Total Transactions', salesReport.metrics.totalTransactions.toString()],\n      ['Average Transaction Value', `KSh ${salesReport.metrics.averageTransactionValue.toFixed(2)}`],\n      ['Cash Sales', `KSh ${salesReport.metrics.cashSales.toLocaleString()}`],\n      ['M-PESA Sales', `KSh ${salesReport.metrics.mpesaSales.toLocaleString()}`],\n      ['Credit Sales', `KSh ${salesReport.metrics.creditSales.toLocaleString()}`],\n      [],\n      ['Top Products', ''],\n      ...salesReport.topProducts.map(p => [p.name, `KSh ${p.revenue.toLocaleString()}`]),\n      [],\n      ['Top Services', ''],\n      ...salesReport.topServices.map(s => [s.name, `KSh ${s.revenue.toLocaleString()}`]),\n    ].map(row => row.join(',')).join('\\n');\n\n    // Download CSV\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `sales-report-${salesReport.period}-${new Date().toISOString().split('T')[0]}.csv`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    window.URL.revokeObjectURL(url);\n  };\n\n  const exportPDF = async () => {\n    if (!salesReport) return;\n\n    try {\n      // Create a temporary div with the report content\n      const reportElement = document.createElement('div');\n      reportElement.innerHTML = `\n        <div style=\"padding: 20px; font-family: Arial, sans-serif;\">\n          <h1 style=\"color: #2563eb; margin-bottom: 20px;\">Sales Report - ${salesReport.period}</h1>\n          <p style=\"margin-bottom: 20px;\">Generated on: ${new Date().toLocaleDateString()}</p>\n\n          <h2 style=\"color: #374151; margin: 20px 0 10px 0;\">Key Metrics</h2>\n          <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\">\n            <tr style=\"background-color: #f3f4f6;\">\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Total Sales</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.totalSales.toLocaleString()}</td>\n            </tr>\n            <tr>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Total Transactions</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${salesReport.metrics.totalTransactions}</td>\n            </tr>\n            <tr style=\"background-color: #f3f4f6;\">\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Average Transaction Value</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.averageTransactionValue.toFixed(2)}</td>\n            </tr>\n          </table>\n\n          <h2 style=\"color: #374151; margin: 20px 0 10px 0;\">Payment Methods</h2>\n          <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\">\n            <tr style=\"background-color: #f3f4f6;\">\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Cash</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.cashSales.toLocaleString()}</td>\n            </tr>\n            <tr>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">M-PESA</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.mpesaSales.toLocaleString()}</td>\n            </tr>\n            <tr style=\"background-color: #f3f4f6;\">\n              <td style=\"padding: 8px; border: 1px solid #d1d5db; font-weight: bold;\">Credit</td>\n              <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${salesReport.metrics.creditSales.toLocaleString()}</td>\n            </tr>\n          </table>\n\n          <h2 style=\"color: #374151; margin: 20px 0 10px 0;\">Top Products</h2>\n          <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\">\n            <tr style=\"background-color: #f3f4f6;\">\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Product</th>\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Quantity</th>\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Revenue</th>\n            </tr>\n            ${salesReport.topProducts.slice(0, 5).map(p => `\n              <tr>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${p.name}</td>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${p.quantitySold}</td>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${p.revenue.toLocaleString()}</td>\n              </tr>\n            `).join('')}\n          </table>\n\n          <h2 style=\"color: #374151; margin: 20px 0 10px 0;\">Top Services</h2>\n          <table style=\"width: 100%; border-collapse: collapse;\">\n            <tr style=\"background-color: #f3f4f6;\">\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Service</th>\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Times Sold</th>\n              <th style=\"padding: 8px; border: 1px solid #d1d5db; text-align: left;\">Revenue</th>\n            </tr>\n            ${salesReport.topServices.slice(0, 5).map(s => `\n              <tr>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${s.name}</td>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">${s.timesSold}</td>\n                <td style=\"padding: 8px; border: 1px solid #d1d5db;\">KSh ${s.revenue.toLocaleString()}</td>\n              </tr>\n            `).join('')}\n          </table>\n        </div>\n      `;\n\n      // Temporarily add to DOM\n      document.body.appendChild(reportElement);\n\n      // Convert to canvas\n      const canvas = await html2canvas(reportElement, {\n        scale: 2,\n        useCORS: true,\n        allowTaint: true,\n      });\n\n      // Remove from DOM\n      document.body.removeChild(reportElement);\n\n      // Create PDF\n      const imgData = canvas.toDataURL('image/png');\n      const pdf = new jsPDF('p', 'mm', 'a4');\n      const imgWidth = 210;\n      const pageHeight = 295;\n      const imgHeight = (canvas.height * imgWidth) / canvas.width;\n      let heightLeft = imgHeight;\n\n      let position = 0;\n\n      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\n      heightLeft -= pageHeight;\n\n      while (heightLeft >= 0) {\n        position = heightLeft - imgHeight;\n        pdf.addPage();\n        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\n        heightLeft -= pageHeight;\n      }\n\n      pdf.save(`sales-report-${salesReport.period}-${new Date().toISOString().split('T')[0]}.pdf`);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Error generating PDF. Please try again.');\n    }\n  };\n\n  if (!hasPermission(['admin', 'attendant'])) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <Users className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Access Denied</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            You don't have permission to view reports.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center\">\n            <BarChart3 className=\"h-6 w-6 text-primary-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">Reports & Analytics</h1>\n          </div>\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={loadReport}\n              disabled={loading}\n              className=\"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center disabled:opacity-50\"\n            >\n              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\n              Refresh\n            </button>\n            <button\n              onClick={exportCSV}\n              disabled={!salesReport || activeTab !== 'sales'}\n              className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center disabled:opacity-50\"\n            >\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export CSV\n            </button>\n            <button\n              onClick={exportPDF}\n              disabled={!salesReport || activeTab !== 'sales'}\n              className=\"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center disabled:opacity-50\"\n            >\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export PDF\n            </button>\n          </div>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"flex space-x-1 mb-6\">\n          <button\n            onClick={() => setActiveTab('sales')}\n            className={`px-4 py-2 rounded-lg text-sm font-medium ${\n              activeTab === 'sales'\n                ? 'bg-primary-600 text-white'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n          >\n            Sales Analytics\n          </button>\n          <button\n            onClick={() => setActiveTab('inventory')}\n            className={`px-4 py-2 rounded-lg text-sm font-medium ${\n              activeTab === 'inventory'\n                ? 'bg-primary-600 text-white'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n          >\n            Inventory Analytics\n          </button>\n        </div>\n\n        {/* Period Selection - Only show for sales tab */}\n        {activeTab === 'sales' && (\n        <div className=\"flex flex-wrap items-center gap-4 mb-6\">\n          <div className=\"flex items-center space-x-2\">\n            <Calendar className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm font-medium text-gray-700\">Period:</span>\n          </div>\n          <div className=\"flex space-x-2\">\n            {(['today', 'week', 'month', 'year'] as const).map((period) => (\n              <button\n                key={period}\n                onClick={() => setSelectedPeriod(period)}\n                className={`px-3 py-1 rounded-md text-sm font-medium ${\n                  selectedPeriod === period\n                    ? 'bg-primary-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                {period.charAt(0).toUpperCase() + period.slice(1)}\n              </button>\n            ))}\n          </div>\n\n          {/* Custom Date Range */}\n          <div className=\"flex items-center space-x-2 ml-4\">\n            <input\n              type=\"date\"\n              value={customDateRange.start}\n              onChange={(e) => setCustomDateRange(prev => ({ ...prev, start: e.target.value }))}\n              className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n            />\n            <span className=\"text-gray-500\">to</span>\n            <input\n              type=\"date\"\n              value={customDateRange.end}\n              onChange={(e) => setCustomDateRange(prev => ({ ...prev, end: e.target.value }))}\n              className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n            />\n            <button\n              onClick={handleCustomDateRange}\n              disabled={!customDateRange.start || !customDateRange.end}\n              className=\"bg-primary-600 text-white px-3 py-1 rounded-md text-sm hover:bg-primary-700 disabled:opacity-50\"\n            >\n              Apply\n            </button>\n          </div>\n        </div>\n        )}\n      </div>\n\n      {/* Content based on active tab */}\n      {activeTab === 'inventory' ? (\n        <InventoryAnalytics />\n      ) : (\n        <>\n\n      {loading && (\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"flex items-center justify-center py-12\">\n            <RefreshCw className=\"h-8 w-8 text-primary-600 animate-spin mr-3\" />\n            <span className=\"text-lg text-gray-600\">Generating report...</span>\n          </div>\n        </div>\n      )}\n\n      {!loading && !salesReport && (\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"text-center py-12\">\n            <BarChart3 className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No Data Available</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              No transactions found for the selected period.\n            </p>\n          </div>\n        </div>\n      )}\n\n      {!loading && salesReport && (\n        <>\n          {/* Key Metrics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <DollarSign className=\"h-8 w-8 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-500\">Total Sales</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    KSh {salesReport.metrics.totalSales.toLocaleString()}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <BarChart3 className=\"h-8 w-8 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-500\">Transactions</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {salesReport.metrics.totalTransactions}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <TrendingUp className=\"h-8 w-8 text-purple-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-500\">Avg. Transaction</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    KSh {salesReport.metrics.averageTransactionValue.toFixed(0)}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CreditCard className=\"h-8 w-8 text-orange-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-500\">Payment Methods</p>\n                  <p className=\"text-sm text-gray-600\">\n                    Cash: KSh {salesReport.metrics.cashSales.toLocaleString()}\n                  </p>\n                  <p className=\"text-sm text-gray-600\">\n                    M-PESA: KSh {salesReport.metrics.mpesaSales.toLocaleString()}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Charts Row */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Daily Sales Chart */}\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Daily Sales Trend</h3>\n              <div className=\"h-64\">\n                <Line\n                  data={{\n                    labels: salesReport.dailyBreakdown.map(d =>\n                      new Date(d.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })\n                    ),\n                    datasets: [\n                      {\n                        label: 'Sales (KSh)',\n                        data: salesReport.dailyBreakdown.map(d => d.sales),\n                        borderColor: 'rgb(37, 99, 235)',\n                        backgroundColor: 'rgba(37, 99, 235, 0.1)',\n                        tension: 0.1,\n                      },\n                    ],\n                  }}\n                  options={{\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        position: 'top' as const,\n                      },\n                    },\n                    scales: {\n                      y: {\n                        beginAtZero: true,\n                        ticks: {\n                          callback: function(value) {\n                            return 'KSh ' + Number(value).toLocaleString();\n                          }\n                        }\n                      }\n                    }\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Payment Methods Chart */}\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Payment Methods</h3>\n              <div className=\"h-64\">\n                <Doughnut\n                  data={{\n                    labels: salesReport.paymentMethodBreakdown.map(p => {\n                      switch(p.method) {\n                        case 'cash': return 'Cash';\n                        case 'mpesa': return 'M-PESA';\n                        case 'debt': return 'Credit';\n                        default: return p.method;\n                      }\n                    }),\n                    datasets: [\n                      {\n                        data: salesReport.paymentMethodBreakdown.map(p => p.amount),\n                        backgroundColor: [\n                          'rgba(34, 197, 94, 0.8)',\n                          'rgba(59, 130, 246, 0.8)',\n                          'rgba(249, 115, 22, 0.8)',\n                        ],\n                        borderColor: [\n                          'rgba(34, 197, 94, 1)',\n                          'rgba(59, 130, 246, 1)',\n                          'rgba(249, 115, 22, 1)',\n                        ],\n                        borderWidth: 2,\n                      },\n                    ],\n                  }}\n                  options={{\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        position: 'bottom' as const,\n                      },\n                      tooltip: {\n                        callbacks: {\n                          label: function(context) {\n                            const value = context.parsed;\n                            const percentage = salesReport.paymentMethodBreakdown[context.dataIndex].percentage;\n                            return `${context.label}: KSh ${value.toLocaleString()} (${percentage.toFixed(1)}%)`;\n                          }\n                        }\n                      }\n                    },\n                  }}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Performance Tables */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Top Products */}\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <Package className=\"h-5 w-5 mr-2 text-blue-600\" />\n                Top Products\n              </h3>\n              <div className=\"space-y-3\">\n                {salesReport.topProducts.length > 0 ? (\n                  salesReport.topProducts.slice(0, 5).map((product, index) => (\n                    <div key={product.id} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-sm font-medium text-gray-500 w-6\">\n                          {index + 1}.\n                        </span>\n                        <div className=\"ml-2\">\n                          <p className=\"text-sm font-medium text-gray-900\">{product.name}</p>\n                          <p className=\"text-xs text-gray-500\">Qty: {product.quantitySold}</p>\n                        </div>\n                      </div>\n                      <span className=\"text-sm font-medium text-green-600\">\n                        KSh {product.revenue.toLocaleString()}\n                      </span>\n                    </div>\n                  ))\n                ) : (\n                  <p className=\"text-sm text-gray-500\">No product sales in this period</p>\n                )}\n              </div>\n            </div>\n\n            {/* Top Services */}\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <BarChart3 className=\"h-5 w-5 mr-2 text-purple-600\" />\n                Top Services\n              </h3>\n              <div className=\"space-y-3\">\n                {salesReport.topServices.length > 0 ? (\n                  salesReport.topServices.slice(0, 5).map((service, index) => (\n                    <div key={service.id} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-sm font-medium text-gray-500 w-6\">\n                          {index + 1}.\n                        </span>\n                        <div className=\"ml-2\">\n                          <p className=\"text-sm font-medium text-gray-900\">{service.name}</p>\n                          <p className=\"text-xs text-gray-500\">Times: {service.timesSold}</p>\n                        </div>\n                      </div>\n                      <span className=\"text-sm font-medium text-green-600\">\n                        KSh {service.revenue.toLocaleString()}\n                      </span>\n                    </div>\n                  ))\n                ) : (\n                  <p className=\"text-sm text-gray-500\">No service sales in this period</p>\n                )}\n              </div>\n            </div>\n\n            {/* Staff Performance */}\n            <div className=\"bg-white shadow rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <Users className=\"h-5 w-5 mr-2 text-orange-600\" />\n                Staff Performance\n              </h3>\n              <div className=\"space-y-3\">\n                {salesReport.attendantPerformance.length > 0 ? (\n                  salesReport.attendantPerformance.slice(0, 5).map((attendant, index) => (\n                    <div key={attendant.id} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-sm font-medium text-gray-500 w-6\">\n                          {index + 1}.\n                        </span>\n                        <div className=\"ml-2\">\n                          <p className=\"text-sm font-medium text-gray-900\">{attendant.name}</p>\n                          <p className=\"text-xs text-gray-500\">\n                            {attendant.transactionCount} transactions\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <span className=\"text-sm font-medium text-green-600\">\n                          KSh {attendant.totalSales.toLocaleString()}\n                        </span>\n                        <p className=\"text-xs text-gray-500\">\n                          Avg: KSh {attendant.averageTransactionValue.toFixed(0)}\n                        </p>\n                      </div>\n                    </div>\n                  ))\n                ) : (\n                  <p className=\"text-sm text-gray-500\">No staff data available</p>\n                )}\n              </div>\n            </div>\n          </div>\n        </>\n      )}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,UAAU,EAEVC,SAAS,QACJ,cAAc;AACrB,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,YAAY,EACZC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;AACjB,SAAcC,IAAI,EAAEC,QAAQ,QAAQ,iBAAiB;AACrD,SAASC,UAAU,QAAqB,wBAAwB;AAChE,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,aAAa;;AAErC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACApB,OAAO,CAACqB,QAAQ,CACdpB,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,YAAY,EACZC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMa,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAc,CAAC,GAAGX,OAAO,CAAC,CAAC;EACnC,MAAM;IAAEY,mBAAmB;IAAEC;EAAQ,CAAC,GAAGd,UAAU,CAAC,CAAC;EACrD,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAsC,MAAM,CAAC;EACjG,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC;IAAE6C,KAAK,EAAE,EAAE;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAwB,OAAO,CAAC;;EAE1E;EACAC,SAAS,CAAC,MAAM;IACdgD,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACV,cAAc,CAAC,CAAC;EAEpB,MAAMU,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMb,mBAAmB,CAACE,cAAc,CAAC;MACxDG,cAAc,CAACQ,MAAM,CAAC;IACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAME,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAIV,eAAe,CAACE,KAAK,IAAIF,eAAe,CAACG,GAAG,EAAE;MAChD,IAAI;QACF,MAAMI,MAAM,GAAG,MAAMb,mBAAmB,CACtC,QAAQ,EACR,IAAIiB,IAAI,CAACX,eAAe,CAACE,KAAK,CAAC,EAC/B,IAAIS,IAAI,CAACX,eAAe,CAACG,GAAG,CAC9B,CAAC;QACDJ,cAAc,CAACQ,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF;EACF,CAAC;EAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACd,WAAW,EAAE;;IAElB;IACA,MAAMe,UAAU,GAAG,CACjB,CAAC,QAAQ,EAAE,OAAO,CAAC,EACnB,CAAC,aAAa,EAAE,OAAOf,WAAW,CAACgB,OAAO,CAACC,UAAU,CAACC,cAAc,CAAC,CAAC,EAAE,CAAC,EACzE,CAAC,oBAAoB,EAAElB,WAAW,CAACgB,OAAO,CAACG,iBAAiB,CAACC,QAAQ,CAAC,CAAC,CAAC,EACxE,CAAC,2BAA2B,EAAE,OAAOpB,WAAW,CAACgB,OAAO,CAACK,uBAAuB,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAC9F,CAAC,YAAY,EAAE,OAAOtB,WAAW,CAACgB,OAAO,CAACO,SAAS,CAACL,cAAc,CAAC,CAAC,EAAE,CAAC,EACvE,CAAC,cAAc,EAAE,OAAOlB,WAAW,CAACgB,OAAO,CAACQ,UAAU,CAACN,cAAc,CAAC,CAAC,EAAE,CAAC,EAC1E,CAAC,cAAc,EAAE,OAAOlB,WAAW,CAACgB,OAAO,CAACS,WAAW,CAACP,cAAc,CAAC,CAAC,EAAE,CAAC,EAC3E,EAAE,EACF,CAAC,cAAc,EAAE,EAAE,CAAC,EACpB,GAAGlB,WAAW,CAAC0B,WAAW,CAACC,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,IAAI,EAAE,OAAOD,CAAC,CAACE,OAAO,CAACZ,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,EAClF,EAAE,EACF,CAAC,cAAc,EAAE,EAAE,CAAC,EACpB,GAAGlB,WAAW,CAAC+B,WAAW,CAACJ,GAAG,CAACK,CAAC,IAAI,CAACA,CAAC,CAACH,IAAI,EAAE,OAAOG,CAAC,CAACF,OAAO,CAACZ,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CACnF,CAACS,GAAG,CAACM,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;;IAEtC;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACrB,UAAU,CAAC,EAAE;MAAEsB,IAAI,EAAE;IAAW,CAAC,CAAC;IACzD,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IAC5C,MAAMO,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;IACZI,CAAC,CAACI,QAAQ,GAAG,gBAAgB9C,WAAW,CAAC+C,MAAM,IAAI,IAAIlC,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC/FN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,CAAC,CAAC;IAC5BA,CAAC,CAACU,KAAK,CAAC,CAAC;IACTT,QAAQ,CAACO,IAAI,CAACG,WAAW,CAACX,CAAC,CAAC;IAC5BH,MAAM,CAACC,GAAG,CAACc,eAAe,CAAChB,GAAG,CAAC;EACjC,CAAC;EAED,MAAMiB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACvD,WAAW,EAAE;IAElB,IAAI;MACF;MACA,MAAMwD,aAAa,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACnDY,aAAa,CAACC,SAAS,GAAG;AAChC;AACA,4EAA4EzD,WAAW,CAAC+C,MAAM;AAC9F,0DAA0D,IAAIlC,IAAI,CAAC,CAAC,CAAC6C,kBAAkB,CAAC,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA,yEAAyE1D,WAAW,CAACgB,OAAO,CAACC,UAAU,CAACC,cAAc,CAAC,CAAC;AACxH;AACA;AACA;AACA,qEAAqElB,WAAW,CAACgB,OAAO,CAACG,iBAAiB;AAC1G;AACA;AACA;AACA,yEAAyEnB,WAAW,CAACgB,OAAO,CAACK,uBAAuB,CAACC,OAAO,CAAC,CAAC,CAAC;AAC/H;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyEtB,WAAW,CAACgB,OAAO,CAACO,SAAS,CAACL,cAAc,CAAC,CAAC;AACvH;AACA;AACA;AACA,yEAAyElB,WAAW,CAACgB,OAAO,CAACQ,UAAU,CAACN,cAAc,CAAC,CAAC;AACxH;AACA;AACA;AACA,yEAAyElB,WAAW,CAACgB,OAAO,CAACS,WAAW,CAACP,cAAc,CAAC,CAAC;AACzH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAclB,WAAW,CAAC0B,WAAW,CAACiC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChC,GAAG,CAACC,CAAC,IAAI;AAC3D;AACA,uEAAuEA,CAAC,CAACC,IAAI;AAC7E,uEAAuED,CAAC,CAACgC,YAAY;AACrF,2EAA2EhC,CAAC,CAACE,OAAO,CAACZ,cAAc,CAAC,CAAC;AACrG;AACA,aAAa,CAAC,CAACgB,IAAI,CAAC,EAAE,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAclC,WAAW,CAAC+B,WAAW,CAAC4B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChC,GAAG,CAACK,CAAC,IAAI;AAC3D;AACA,uEAAuEA,CAAC,CAACH,IAAI;AAC7E,uEAAuEG,CAAC,CAAC6B,SAAS;AAClF,2EAA2E7B,CAAC,CAACF,OAAO,CAACZ,cAAc,CAAC,CAAC;AACrG;AACA,aAAa,CAAC,CAACgB,IAAI,CAAC,EAAE,CAAC;AACvB;AACA;AACA,OAAO;;MAED;MACAS,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACK,aAAa,CAAC;;MAExC;MACA,MAAMM,MAAM,GAAG,MAAM3E,WAAW,CAACqE,aAAa,EAAE;QAC9CO,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE;MACd,CAAC,CAAC;;MAEF;MACAtB,QAAQ,CAACO,IAAI,CAACG,WAAW,CAACG,aAAa,CAAC;;MAExC;MACA,MAAMU,OAAO,GAAGJ,MAAM,CAACK,SAAS,CAAC,WAAW,CAAC;MAC7C,MAAMC,GAAG,GAAG,IAAIlF,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;MACtC,MAAMmF,QAAQ,GAAG,GAAG;MACpB,MAAMC,UAAU,GAAG,GAAG;MACtB,MAAMC,SAAS,GAAIT,MAAM,CAACU,MAAM,GAAGH,QAAQ,GAAIP,MAAM,CAACW,KAAK;MAC3D,IAAIC,UAAU,GAAGH,SAAS;MAE1B,IAAII,QAAQ,GAAG,CAAC;MAEhBP,GAAG,CAACQ,QAAQ,CAACV,OAAO,EAAE,KAAK,EAAE,CAAC,EAAES,QAAQ,EAAEN,QAAQ,EAAEE,SAAS,CAAC;MAC9DG,UAAU,IAAIJ,UAAU;MAExB,OAAOI,UAAU,IAAI,CAAC,EAAE;QACtBC,QAAQ,GAAGD,UAAU,GAAGH,SAAS;QACjCH,GAAG,CAACS,OAAO,CAAC,CAAC;QACbT,GAAG,CAACQ,QAAQ,CAACV,OAAO,EAAE,KAAK,EAAE,CAAC,EAAES,QAAQ,EAAEN,QAAQ,EAAEE,SAAS,CAAC;QAC9DG,UAAU,IAAIJ,UAAU;MAC1B;MAEAF,GAAG,CAACU,IAAI,CAAC,gBAAgB9E,WAAW,CAAC+C,MAAM,IAAI,IAAIlC,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC9F,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CqE,KAAK,CAAC,yCAAyC,CAAC;IAClD;EACF,CAAC;EAED,IAAI,CAACpF,aAAa,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,EAAE;IAC1C,oBACEN,OAAA;MAAK2F,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D5F,OAAA;QAAK2F,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5F,OAAA,CAACvB,KAAK;UAACkH,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDhG,OAAA;UAAI2F,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEhG,OAAA;UAAG2F,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhG,OAAA;IAAK2F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5F,OAAA;MAAK2F,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7C5F,OAAA;QAAK2F,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5F,OAAA;UAAK2F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5F,OAAA,CAAC5B,SAAS;YAACuH,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDhG,OAAA;YAAI2F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACNhG,OAAA;UAAK2F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5F,OAAA;YACEiG,OAAO,EAAE9E,UAAW;YACpB+E,QAAQ,EAAE1F,OAAQ;YAClBmF,SAAS,EAAC,wGAAwG;YAAAC,QAAA,gBAElH5F,OAAA,CAACpB,SAAS;cAAC+G,SAAS,EAAE,gBAAgBnF,OAAO,GAAG,cAAc,GAAG,EAAE;YAAG;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAE3E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA;YACEiG,OAAO,EAAExE,SAAU;YACnByE,QAAQ,EAAE,CAACvF,WAAW,IAAIM,SAAS,KAAK,OAAQ;YAChD0E,SAAS,EAAC,uGAAuG;YAAAC,QAAA,gBAEjH5F,OAAA,CAAC3B,QAAQ;cAACsH,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA;YACEiG,OAAO,EAAE/B,SAAU;YACnBgC,QAAQ,EAAE,CAACvF,WAAW,IAAIM,SAAS,KAAK,OAAQ;YAChD0E,SAAS,EAAC,2GAA2G;YAAAC,QAAA,gBAErH5F,OAAA,CAAC3B,QAAQ;cAACsH,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhG,OAAA;QAAK2F,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC5F,OAAA;UACEiG,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,OAAO,CAAE;UACrCyE,SAAS,EAAE,4CACT1E,SAAS,KAAK,OAAO,GACjB,2BAA2B,GAC3B,6CAA6C,EAChD;UAAA2E,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThG,OAAA;UACEiG,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,WAAW,CAAE;UACzCyE,SAAS,EAAE,4CACT1E,SAAS,KAAK,WAAW,GACrB,2BAA2B,GAC3B,6CAA6C,EAChD;UAAA2E,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL/E,SAAS,KAAK,OAAO,iBACtBjB,OAAA;QAAK2F,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5F,OAAA;UAAK2F,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5F,OAAA,CAAC1B,QAAQ;YAACqH,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ChG,OAAA;YAAM2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACNhG,OAAA;UAAK2F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC3B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAWtD,GAAG,CAAEoB,MAAM,iBACxD1D,OAAA;YAEEiG,OAAO,EAAEA,CAAA,KAAMvF,iBAAiB,CAACgD,MAAM,CAAE;YACzCiC,SAAS,EAAE,4CACTlF,cAAc,KAAKiD,MAAM,GACrB,2BAA2B,GAC3B,6CAA6C,EAChD;YAAAkC,QAAA,EAEFlC,MAAM,CAACyC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG1C,MAAM,CAACY,KAAK,CAAC,CAAC;UAAC,GAR5CZ,MAAM;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASL,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhG,OAAA;UAAK2F,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C5F,OAAA;YACEgD,IAAI,EAAC,MAAM;YACXqD,KAAK,EAAExF,eAAe,CAACE,KAAM;YAC7BuF,QAAQ,EAAGC,CAAC,IAAKzF,kBAAkB,CAAC0F,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEzF,KAAK,EAAEwF,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAClFV,SAAS,EAAC;UAAqD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACFhG,OAAA;YAAM2F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzChG,OAAA;YACEgD,IAAI,EAAC,MAAM;YACXqD,KAAK,EAAExF,eAAe,CAACG,GAAI;YAC3BsF,QAAQ,EAAGC,CAAC,IAAKzF,kBAAkB,CAAC0F,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAExF,GAAG,EAAEuF,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAChFV,SAAS,EAAC;UAAqD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACFhG,OAAA;YACEiG,OAAO,EAAE1E,qBAAsB;YAC/B2E,QAAQ,EAAE,CAACrF,eAAe,CAACE,KAAK,IAAI,CAACF,eAAe,CAACG,GAAI;YACzD2E,SAAS,EAAC,iGAAiG;YAAAC,QAAA,EAC5G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL/E,SAAS,KAAK,WAAW,gBACxBjB,OAAA,CAACJ,kBAAkB;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEtBhG,OAAA,CAAAE,SAAA;MAAA0F,QAAA,GAEDpF,OAAO,iBACNR,OAAA;QAAK2F,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5F,OAAA;UAAK2F,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5F,OAAA,CAACpB,SAAS;YAAC+G,SAAS,EAAC;UAA4C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpEhG,OAAA;YAAM2F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA,CAACxF,OAAO,IAAI,CAACG,WAAW,iBACvBX,OAAA;QAAK2F,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C5F,OAAA;UAAK2F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5F,OAAA,CAAC5B,SAAS;YAACuH,SAAS,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDhG,OAAA;YAAI2F,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EhG,OAAA;YAAG2F,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA,CAACxF,OAAO,IAAIG,WAAW,iBACtBX,OAAA,CAAAE,SAAA;QAAA0F,QAAA,gBAEE5F,OAAA;UAAK2F,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnE5F,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C5F,OAAA;cAAK2F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5F,OAAA;gBAAK2F,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B5F,OAAA,CAACxB,UAAU;kBAACmH,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNhG,OAAA;gBAAK2F,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChEhG,OAAA;kBAAG2F,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,MAC1C,EAACjF,WAAW,CAACgB,OAAO,CAACC,UAAU,CAACC,cAAc,CAAC,CAAC;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C5F,OAAA;cAAK2F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5F,OAAA;gBAAK2F,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B5F,OAAA,CAAC5B,SAAS;kBAACuH,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNhG,OAAA;gBAAK2F,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjEhG,OAAA;kBAAG2F,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAC5CjF,WAAW,CAACgB,OAAO,CAACG;gBAAiB;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C5F,OAAA;cAAK2F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5F,OAAA;gBAAK2F,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B5F,OAAA,CAACzB,UAAU;kBAACoH,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNhG,OAAA;gBAAK2F,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACrEhG,OAAA;kBAAG2F,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,MAC1C,EAACjF,WAAW,CAACgB,OAAO,CAACK,uBAAuB,CAACC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C5F,OAAA;cAAK2F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5F,OAAA;gBAAK2F,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B5F,OAAA,CAACrB,UAAU;kBAACgH,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNhG,OAAA;gBAAK2F,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpEhG,OAAA;kBAAG2F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,YACzB,EAACjF,WAAW,CAACgB,OAAO,CAACO,SAAS,CAACL,cAAc,CAAC,CAAC;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACJhG,OAAA;kBAAG2F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,cACvB,EAACjF,WAAW,CAACgB,OAAO,CAACQ,UAAU,CAACN,cAAc,CAAC,CAAC;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhG,OAAA;UAAK2F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpD5F,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C5F,OAAA;cAAI2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EhG,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB5F,OAAA,CAACR,IAAI;gBACHkH,IAAI,EAAE;kBACJC,MAAM,EAAEhG,WAAW,CAACiG,cAAc,CAACtE,GAAG,CAACuE,CAAC,IACtC,IAAIrF,IAAI,CAACqF,CAAC,CAACC,IAAI,CAAC,CAACzC,kBAAkB,CAAC,OAAO,EAAE;oBAAE0C,KAAK,EAAE,OAAO;oBAAEC,GAAG,EAAE;kBAAU,CAAC,CACjF,CAAC;kBACDC,QAAQ,EAAE,CACR;oBACEC,KAAK,EAAE,aAAa;oBACpBR,IAAI,EAAE/F,WAAW,CAACiG,cAAc,CAACtE,GAAG,CAACuE,CAAC,IAAIA,CAAC,CAACM,KAAK,CAAC;oBAClDC,WAAW,EAAE,kBAAkB;oBAC/BC,eAAe,EAAE,wBAAwB;oBACzCC,OAAO,EAAE;kBACX,CAAC;gBAEL,CAAE;gBACFC,OAAO,EAAE;kBACPC,UAAU,EAAE,IAAI;kBAChBC,mBAAmB,EAAE,KAAK;kBAC1BC,OAAO,EAAE;oBACPC,MAAM,EAAE;sBACNrC,QAAQ,EAAE;oBACZ;kBACF,CAAC;kBACDsC,MAAM,EAAE;oBACNC,CAAC,EAAE;sBACDC,WAAW,EAAE,IAAI;sBACjBC,KAAK,EAAE;wBACLC,QAAQ,EAAE,SAAAA,CAAS3B,KAAK,EAAE;0BACxB,OAAO,MAAM,GAAG4B,MAAM,CAAC5B,KAAK,CAAC,CAACxE,cAAc,CAAC,CAAC;wBAChD;sBACF;oBACF;kBACF;gBACF;cAAE;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhG,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C5F,OAAA;cAAI2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EhG,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB5F,OAAA,CAACP,QAAQ;gBACPiH,IAAI,EAAE;kBACJC,MAAM,EAAEhG,WAAW,CAACuH,sBAAsB,CAAC5F,GAAG,CAACC,CAAC,IAAI;oBAClD,QAAOA,CAAC,CAAC4F,MAAM;sBACb,KAAK,MAAM;wBAAE,OAAO,MAAM;sBAC1B,KAAK,OAAO;wBAAE,OAAO,QAAQ;sBAC7B,KAAK,MAAM;wBAAE,OAAO,QAAQ;sBAC5B;wBAAS,OAAO5F,CAAC,CAAC4F,MAAM;oBAC1B;kBACF,CAAC,CAAC;kBACFlB,QAAQ,EAAE,CACR;oBACEP,IAAI,EAAE/F,WAAW,CAACuH,sBAAsB,CAAC5F,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC6F,MAAM,CAAC;oBAC3Df,eAAe,EAAE,CACf,wBAAwB,EACxB,yBAAyB,EACzB,yBAAyB,CAC1B;oBACDD,WAAW,EAAE,CACX,sBAAsB,EACtB,uBAAuB,EACvB,uBAAuB,CACxB;oBACDiB,WAAW,EAAE;kBACf,CAAC;gBAEL,CAAE;gBACFd,OAAO,EAAE;kBACPC,UAAU,EAAE,IAAI;kBAChBC,mBAAmB,EAAE,KAAK;kBAC1BC,OAAO,EAAE;oBACPC,MAAM,EAAE;sBACNrC,QAAQ,EAAE;oBACZ,CAAC;oBACDgD,OAAO,EAAE;sBACPC,SAAS,EAAE;wBACTrB,KAAK,EAAE,SAAAA,CAASsB,OAAO,EAAE;0BACvB,MAAMnC,KAAK,GAAGmC,OAAO,CAACC,MAAM;0BAC5B,MAAMC,UAAU,GAAG/H,WAAW,CAACuH,sBAAsB,CAACM,OAAO,CAACG,SAAS,CAAC,CAACD,UAAU;0BACnF,OAAO,GAAGF,OAAO,CAACtB,KAAK,SAASb,KAAK,CAACxE,cAAc,CAAC,CAAC,KAAK6G,UAAU,CAACzG,OAAO,CAAC,CAAC,CAAC,IAAI;wBACtF;sBACF;oBACF;kBACF;gBACF;cAAE;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhG,OAAA;UAAK2F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpD5F,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C5F,OAAA;cAAI2F,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtE5F,OAAA,CAACtB,OAAO;gBAACiH,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBjF,WAAW,CAAC0B,WAAW,CAACuG,MAAM,GAAG,CAAC,GACjCjI,WAAW,CAAC0B,WAAW,CAACiC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChC,GAAG,CAAC,CAACuG,OAAO,EAAEC,KAAK,kBACrD9I,OAAA;gBAAsB2F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACjE5F,OAAA;kBAAK2F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC5F,OAAA;oBAAM2F,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,GACpDkD,KAAK,GAAG,CAAC,EAAC,GACb;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPhG,OAAA;oBAAK2F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB5F,OAAA;sBAAG2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEiD,OAAO,CAACrG;oBAAI;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnEhG,OAAA;sBAAG2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,OAAK,EAACiD,OAAO,CAACtE,YAAY;oBAAA;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhG,OAAA;kBAAM2F,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,GAAC,MAC/C,EAACiD,OAAO,CAACpG,OAAO,CAACZ,cAAc,CAAC,CAAC;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA,GAZC6C,OAAO,CAACE,EAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACN,CAAC,gBAEFhG,OAAA;gBAAG2F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACxE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhG,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C5F,OAAA;cAAI2F,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtE5F,OAAA,CAAC5B,SAAS;gBAACuH,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAExD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBjF,WAAW,CAAC+B,WAAW,CAACkG,MAAM,GAAG,CAAC,GACjCjI,WAAW,CAAC+B,WAAW,CAAC4B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChC,GAAG,CAAC,CAAC0G,OAAO,EAAEF,KAAK,kBACrD9I,OAAA;gBAAsB2F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACjE5F,OAAA;kBAAK2F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC5F,OAAA;oBAAM2F,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,GACpDkD,KAAK,GAAG,CAAC,EAAC,GACb;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPhG,OAAA;oBAAK2F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB5F,OAAA;sBAAG2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEoD,OAAO,CAACxG;oBAAI;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnEhG,OAAA;sBAAG2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,SAAO,EAACoD,OAAO,CAACxE,SAAS;oBAAA;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhG,OAAA;kBAAM2F,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,GAAC,MAC/C,EAACoD,OAAO,CAACvG,OAAO,CAACZ,cAAc,CAAC,CAAC;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA,GAZCgD,OAAO,CAACD,EAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACN,CAAC,gBAEFhG,OAAA;gBAAG2F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACxE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhG,OAAA;YAAK2F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C5F,OAAA;cAAI2F,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtE5F,OAAA,CAACvB,KAAK;gBAACkH,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBjF,WAAW,CAACsI,oBAAoB,CAACL,MAAM,GAAG,CAAC,GAC1CjI,WAAW,CAACsI,oBAAoB,CAAC3E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChC,GAAG,CAAC,CAAC4G,SAAS,EAAEJ,KAAK,kBAChE9I,OAAA;gBAAwB2F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACnE5F,OAAA;kBAAK2F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC5F,OAAA;oBAAM2F,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,GACpDkD,KAAK,GAAG,CAAC,EAAC,GACb;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPhG,OAAA;oBAAK2F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB5F,OAAA;sBAAG2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEsD,SAAS,CAAC1G;oBAAI;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEhG,OAAA;sBAAG2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACjCsD,SAAS,CAACC,gBAAgB,EAAC,eAC9B;oBAAA;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhG,OAAA;kBAAK2F,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5F,OAAA;oBAAM2F,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,MAC/C,EAACsD,SAAS,CAACtH,UAAU,CAACC,cAAc,CAAC,CAAC;kBAAA;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACPhG,OAAA;oBAAG2F,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,WAC1B,EAACsD,SAAS,CAAClH,uBAAuB,CAACC,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,GAnBEkD,SAAS,CAACH,EAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBjB,CACN,CAAC,gBAEFhG,OAAA;gBAAG2F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAChE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CACH;IAAA,eACG,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3F,EAAA,CA7lBID,OAAiB;EAAA,QACKT,OAAO,EACQD,UAAU;AAAA;AAAA0J,EAAA,GAF/ChJ,OAAiB;AA+lBvB,eAAeA,OAAO;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}