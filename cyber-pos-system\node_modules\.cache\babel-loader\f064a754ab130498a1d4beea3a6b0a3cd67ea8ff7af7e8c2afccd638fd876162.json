{"ast": null, "code": "var _jsxFileName = \"E:\\\\FX\\\\Cyber POS\\\\cyber-pos-system\\\\src\\\\components\\\\inventory\\\\InventoryStats.tsx\";\nimport React from 'react';\nimport { Package, TrendingDown, AlertTriangle, Calendar, DollarSign, BarChart3, Activity, Tag } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InventoryStats = ({\n  products\n}) => {\n  // Calculate basic stats\n  const totalProducts = products.length;\n  const activeProducts = products.filter(p => p.isActive).length;\n  const inactiveProducts = totalProducts - activeProducts;\n  const totalStockValue = products.reduce((sum, product) => sum + product.price * product.stockQuantity, 0);\n  const averagePrice = products.length > 0 ? products.reduce((sum, product) => sum + product.price, 0) / products.length : 0;\n\n  // Stock analysis\n  const inStockProducts = products.filter(p => p.stockQuantity > 0).length;\n  const outOfStockProducts = products.filter(p => p.stockQuantity === 0).length;\n  const lowStockProducts = products.filter(p => p.stockQuantity > 0 && p.stockQuantity <= p.reorderLevel).length;\n\n  // Expiry analysis\n  const productsWithExpiry = products.filter(p => p.hasExpiry).length;\n  const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n  const expiringProducts = products.filter(p => p.hasExpiry && p.expiryDate && p.expiryDate <= thirtyDaysFromNow).length;\n\n  // Category analysis\n  const categoryStats = products.reduce((acc, product) => {\n    if (!acc[product.category]) {\n      acc[product.category] = {\n        count: 0,\n        totalValue: 0,\n        totalStock: 0,\n        lowStock: 0\n      };\n    }\n    acc[product.category].count++;\n    acc[product.category].totalValue += product.price * product.stockQuantity;\n    acc[product.category].totalStock += product.stockQuantity;\n    if (product.stockQuantity <= product.reorderLevel) {\n      acc[product.category].lowStock++;\n    }\n    return acc;\n  }, {});\n  const topCategories = Object.entries(categoryStats).sort(([, a], [, b]) => b.totalValue - a.totalValue).slice(0, 5);\n\n  // Top value products\n  const topValueProducts = products.map(product => ({\n    ...product,\n    totalValue: product.price * product.stockQuantity\n  })).sort((a, b) => b.totalValue - a.totalValue).slice(0, 5);\n  const mainStats = [{\n    name: 'Total Products',\n    value: totalProducts.toString(),\n    icon: Package,\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-100'\n  }, {\n    name: 'Stock Value',\n    value: `KSh ${totalStockValue.toLocaleString()}`,\n    icon: DollarSign,\n    color: 'text-green-600',\n    bgColor: 'bg-green-100'\n  }, {\n    name: 'Low Stock Items',\n    value: lowStockProducts.toString(),\n    icon: TrendingDown,\n    color: 'text-orange-600',\n    bgColor: 'bg-orange-100'\n  }, {\n    name: 'Out of Stock',\n    value: outOfStockProducts.toString(),\n    icon: AlertTriangle,\n    color: 'text-red-600',\n    bgColor: 'bg-red-100'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n      children: mainStats.map(stat => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-2 rounded-lg ${stat.bgColor}`,\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              className: `h-5 w-5 ${stat.color}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: stat.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)\n      }, stat.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Activity, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), \"Stock Status Overview\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-green-500 rounded-full mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"In Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: inStockProducts\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-orange-500 rounded-full mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Low Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: lowStockProducts\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-red-500 rounded-full mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Out of Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: outOfStockProducts\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-gray-400 rounded-full mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: inactiveProducts\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), \"Expiry Analysis\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Products with Expiry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: productsWithExpiry\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Expiring in 30 Days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-sm font-semibold ${expiringProducts > 0 ? 'text-orange-600' : 'text-gray-900'}`,\n              children: expiringProducts\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"No Expiry Tracking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: totalProducts - productsWithExpiry\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), expiringProducts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-orange-50 border border-orange-200 rounded-md p-3 mt-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"h-4 w-4 text-orange-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-orange-700\",\n                children: [expiringProducts, \" product\", expiringProducts > 1 ? 's' : '', \" expiring soon\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Tag, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), \"Top Categories by Value\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: topCategories.length > 0 ? topCategories.map(([category, stats]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-gray-50 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: [stats.count, \" products \\u2022 \", stats.totalStock, \" units\", stats.lowStock > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-orange-600 ml-1\",\n                  children: [\"\\u2022 \", stats.lowStock, \" low stock\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: [\"KSh \", stats.totalValue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this)]\n          }, category, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 text-center py-4\",\n            children: \"No categories yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), \"Top Products by Stock Value\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: topValueProducts.length > 0 ? topValueProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-gray-50 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: [product.stockQuantity, \" units @ KSh \", product.price.toLocaleString(), \" each\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-900\",\n              children: [\"KSh \", product.totalValue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 text-center py-4\",\n            children: \"No products yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 lg:col-span-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n            className: \"h-5 w-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), \"Financial Summary\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-4 bg-green-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Total Stock Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-green-600\",\n              children: [\"KSh \", totalStockValue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-4 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Average Product Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-blue-600\",\n              children: [\"KSh \", averagePrice.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-4 bg-purple-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-purple-600\",\n              children: Object.keys(categoryStats).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_c = InventoryStats;\nexport default InventoryStats;\nvar _c;\n$RefreshReg$(_c, \"InventoryStats\");", "map": {"version": 3, "names": ["React", "Package", "TrendingDown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Calendar", "DollarSign", "BarChart3", "Activity", "Tag", "jsxDEV", "_jsxDEV", "InventoryStats", "products", "totalProducts", "length", "activeProducts", "filter", "p", "isActive", "inactiveProducts", "totalStockValue", "reduce", "sum", "product", "price", "stockQuantity", "averagePrice", "inStockProducts", "outOfStockProducts", "lowStockProducts", "reorderLevel", "productsWithExpiry", "hasEx<PERSON>ry", "thirtyDaysFromNow", "Date", "now", "expiringProducts", "expiryDate", "categoryStats", "acc", "category", "count", "totalValue", "totalStock", "lowStock", "topCategories", "Object", "entries", "sort", "a", "b", "slice", "topValueProducts", "map", "mainStats", "name", "value", "toString", "icon", "color", "bgColor", "toLocaleString", "className", "children", "stat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stats", "id", "keys", "_c", "$RefreshReg$"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/InventoryStats.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Package,\n  TrendingDown,\n  AlertTriangle,\n  Calendar,\n  DollarSign,\n  BarChart3,\n  Activity,\n  Tag\n} from 'lucide-react';\nimport { Product } from '../../types';\n\ninterface InventoryStatsProps {\n  products: Product[];\n}\n\nconst InventoryStats: React.FC<InventoryStatsProps> = ({ products }) => {\n  // Calculate basic stats\n  const totalProducts = products.length;\n  const activeProducts = products.filter(p => p.isActive).length;\n  const inactiveProducts = totalProducts - activeProducts;\n  \n  const totalStockValue = products.reduce((sum, product) => \n    sum + (product.price * product.stockQuantity), 0\n  );\n  \n  const averagePrice = products.length > 0 \n    ? products.reduce((sum, product) => sum + product.price, 0) / products.length \n    : 0;\n\n  // Stock analysis\n  const inStockProducts = products.filter(p => p.stockQuantity > 0).length;\n  const outOfStockProducts = products.filter(p => p.stockQuantity === 0).length;\n  const lowStockProducts = products.filter(p => \n    p.stockQuantity > 0 && p.stockQuantity <= p.reorderLevel\n  ).length;\n\n  // Expiry analysis\n  const productsWithExpiry = products.filter(p => p.hasExpiry).length;\n  const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n  \n  const expiringProducts = products.filter(p => \n    p.hasExpiry && p.expiryDate && p.expiryDate <= thirtyDaysFromNow\n  ).length;\n\n  // Category analysis\n  const categoryStats = products.reduce((acc, product) => {\n    if (!acc[product.category]) {\n      acc[product.category] = {\n        count: 0,\n        totalValue: 0,\n        totalStock: 0,\n        lowStock: 0\n      };\n    }\n    acc[product.category].count++;\n    acc[product.category].totalValue += product.price * product.stockQuantity;\n    acc[product.category].totalStock += product.stockQuantity;\n    if (product.stockQuantity <= product.reorderLevel) {\n      acc[product.category].lowStock++;\n    }\n    return acc;\n  }, {} as Record<string, { count: number; totalValue: number; totalStock: number; lowStock: number }>);\n\n  const topCategories = Object.entries(categoryStats)\n    .sort(([,a], [,b]) => b.totalValue - a.totalValue)\n    .slice(0, 5);\n\n  // Top value products\n  const topValueProducts = products\n    .map(product => ({\n      ...product,\n      totalValue: product.price * product.stockQuantity\n    }))\n    .sort((a, b) => b.totalValue - a.totalValue)\n    .slice(0, 5);\n\n  const mainStats = [\n    {\n      name: 'Total Products',\n      value: totalProducts.toString(),\n      icon: Package,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100'\n    },\n    {\n      name: 'Stock Value',\n      value: `KSh ${totalStockValue.toLocaleString()}`,\n      icon: DollarSign,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100'\n    },\n    {\n      name: 'Low Stock Items',\n      value: lowStockProducts.toString(),\n      icon: TrendingDown,\n      color: 'text-orange-600',\n      bgColor: 'bg-orange-100'\n    },\n    {\n      name: 'Out of Stock',\n      value: outOfStockProducts.toString(),\n      icon: AlertTriangle,\n      color: 'text-red-600',\n      bgColor: 'bg-red-100'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Main Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {mainStats.map((stat) => (\n          <div key={stat.name} className=\"bg-white rounded-lg shadow p-4\">\n            <div className=\"flex items-center\">\n              <div className={`p-2 rounded-lg ${stat.bgColor}`}>\n                <stat.icon className={`h-5 w-5 ${stat.color}`} />\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-500\">{stat.name}</p>\n                <p className=\"text-lg font-semibold text-gray-900\">{stat.value}</p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Detailed Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Stock Status Breakdown */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <Activity className=\"h-5 w-5 mr-2\" />\n            Stock Status Overview\n          </h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600\">In Stock</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{inStockProducts}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-orange-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600\">Low Stock</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{lowStockProducts}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-red-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600\">Out of Stock</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{outOfStockProducts}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-gray-400 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600\">Inactive</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{inactiveProducts}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Expiry Analysis */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <Calendar className=\"h-5 w-5 mr-2\" />\n            Expiry Analysis\n          </h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Products with Expiry</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{productsWithExpiry}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Expiring in 30 Days</span>\n              <span className={`text-sm font-semibold ${expiringProducts > 0 ? 'text-orange-600' : 'text-gray-900'}`}>\n                {expiringProducts}\n              </span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">No Expiry Tracking</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{totalProducts - productsWithExpiry}</span>\n            </div>\n            {expiringProducts > 0 && (\n              <div className=\"bg-orange-50 border border-orange-200 rounded-md p-3 mt-3\">\n                <div className=\"flex items-center\">\n                  <AlertTriangle className=\"h-4 w-4 text-orange-600 mr-2\" />\n                  <span className=\"text-sm text-orange-700\">\n                    {expiringProducts} product{expiringProducts > 1 ? 's' : ''} expiring soon\n                  </span>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Top Categories by Value */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <Tag className=\"h-5 w-5 mr-2\" />\n            Top Categories by Value\n          </h3>\n          <div className=\"space-y-3\">\n            {topCategories.length > 0 ? (\n              topCategories.map(([category, stats]) => (\n                <div key={category} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-700\">{category}</span>\n                    <div className=\"text-xs text-gray-500\">\n                      {stats.count} products • {stats.totalStock} units\n                      {stats.lowStock > 0 && (\n                        <span className=\"text-orange-600 ml-1\">• {stats.lowStock} low stock</span>\n                      )}\n                    </div>\n                  </div>\n                  <span className=\"text-sm font-semibold text-gray-900\">\n                    KSh {stats.totalValue.toLocaleString()}\n                  </span>\n                </div>\n              ))\n            ) : (\n              <p className=\"text-sm text-gray-500 text-center py-4\">No categories yet</p>\n            )}\n          </div>\n        </div>\n\n        {/* Top Products by Value */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <BarChart3 className=\"h-5 w-5 mr-2\" />\n            Top Products by Stock Value\n          </h3>\n          <div className=\"space-y-3\">\n            {topValueProducts.length > 0 ? (\n              topValueProducts.map((product) => (\n                <div key={product.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-700\">{product.name}</span>\n                    <div className=\"text-xs text-gray-500\">\n                      {product.stockQuantity} units @ KSh {product.price.toLocaleString()} each\n                    </div>\n                  </div>\n                  <span className=\"text-sm font-semibold text-gray-900\">\n                    KSh {product.totalValue.toLocaleString()}\n                  </span>\n                </div>\n              ))\n            ) : (\n              <p className=\"text-sm text-gray-500 text-center py-4\">No products yet</p>\n            )}\n          </div>\n        </div>\n\n        {/* Summary Metrics */}\n        <div className=\"bg-white rounded-lg shadow p-6 lg:col-span-2\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <DollarSign className=\"h-5 w-5 mr-2\" />\n            Financial Summary\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n              <p className=\"text-sm text-gray-600\">Total Stock Value</p>\n              <p className=\"text-2xl font-bold text-green-600\">\n                KSh {totalStockValue.toLocaleString()}\n              </p>\n            </div>\n            <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n              <p className=\"text-sm text-gray-600\">Average Product Price</p>\n              <p className=\"text-2xl font-bold text-blue-600\">\n                KSh {averagePrice.toLocaleString()}\n              </p>\n            </div>\n            <div className=\"text-center p-4 bg-purple-50 rounded-lg\">\n              <p className=\"text-sm text-gray-600\">Categories</p>\n              <p className=\"text-2xl font-bold text-purple-600\">\n                {Object.keys(categoryStats).length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InventoryStats;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,OAAO,EACPC,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtB,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACtE;EACA,MAAMC,aAAa,GAAGD,QAAQ,CAACE,MAAM;EACrC,MAAMC,cAAc,GAAGH,QAAQ,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAACJ,MAAM;EAC9D,MAAMK,gBAAgB,GAAGN,aAAa,GAAGE,cAAc;EAEvD,MAAMK,eAAe,GAAGR,QAAQ,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KACnDD,GAAG,GAAIC,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACE,aAAc,EAAE,CACjD,CAAC;EAED,MAAMC,YAAY,GAAGd,QAAQ,CAACE,MAAM,GAAG,CAAC,GACpCF,QAAQ,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAACC,KAAK,EAAE,CAAC,CAAC,GAAGZ,QAAQ,CAACE,MAAM,GAC3E,CAAC;;EAEL;EACA,MAAMa,eAAe,GAAGf,QAAQ,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACQ,aAAa,GAAG,CAAC,CAAC,CAACX,MAAM;EACxE,MAAMc,kBAAkB,GAAGhB,QAAQ,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACQ,aAAa,KAAK,CAAC,CAAC,CAACX,MAAM;EAC7E,MAAMe,gBAAgB,GAAGjB,QAAQ,CAACI,MAAM,CAACC,CAAC,IACxCA,CAAC,CAACQ,aAAa,GAAG,CAAC,IAAIR,CAAC,CAACQ,aAAa,IAAIR,CAAC,CAACa,YAC9C,CAAC,CAAChB,MAAM;;EAER;EACA,MAAMiB,kBAAkB,GAAGnB,QAAQ,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACe,SAAS,CAAC,CAAClB,MAAM;EACnE,MAAMmB,iBAAiB,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAEzE,MAAMC,gBAAgB,GAAGxB,QAAQ,CAACI,MAAM,CAACC,CAAC,IACxCA,CAAC,CAACe,SAAS,IAAIf,CAAC,CAACoB,UAAU,IAAIpB,CAAC,CAACoB,UAAU,IAAIJ,iBACjD,CAAC,CAACnB,MAAM;;EAER;EACA,MAAMwB,aAAa,GAAG1B,QAAQ,CAACS,MAAM,CAAC,CAACkB,GAAG,EAAEhB,OAAO,KAAK;IACtD,IAAI,CAACgB,GAAG,CAAChB,OAAO,CAACiB,QAAQ,CAAC,EAAE;MAC1BD,GAAG,CAAChB,OAAO,CAACiB,QAAQ,CAAC,GAAG;QACtBC,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE,CAAC;QACbC,UAAU,EAAE,CAAC;QACbC,QAAQ,EAAE;MACZ,CAAC;IACH;IACAL,GAAG,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAACC,KAAK,EAAE;IAC7BF,GAAG,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAACE,UAAU,IAAInB,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACE,aAAa;IACzEc,GAAG,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAACG,UAAU,IAAIpB,OAAO,CAACE,aAAa;IACzD,IAAIF,OAAO,CAACE,aAAa,IAAIF,OAAO,CAACO,YAAY,EAAE;MACjDS,GAAG,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAACI,QAAQ,EAAE;IAClC;IACA,OAAOL,GAAG;EACZ,CAAC,EAAE,CAAC,CAAgG,CAAC;EAErG,MAAMM,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACT,aAAa,CAAC,CAChDU,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,CAACR,UAAU,GAAGO,CAAC,CAACP,UAAU,CAAC,CACjDS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEd;EACA,MAAMC,gBAAgB,GAAGxC,QAAQ,CAC9ByC,GAAG,CAAC9B,OAAO,KAAK;IACf,GAAGA,OAAO;IACVmB,UAAU,EAAEnB,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACE;EACtC,CAAC,CAAC,CAAC,CACFuB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACR,UAAU,GAAGO,CAAC,CAACP,UAAU,CAAC,CAC3CS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAEd,MAAMG,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE3C,aAAa,CAAC4C,QAAQ,CAAC,CAAC;IAC/BC,IAAI,EAAEzD,OAAO;IACb0D,KAAK,EAAE,eAAe;IACtBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,OAAOpC,eAAe,CAACyC,cAAc,CAAC,CAAC,EAAE;IAChDH,IAAI,EAAErD,UAAU;IAChBsD,KAAK,EAAE,gBAAgB;IACvBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE3B,gBAAgB,CAAC4B,QAAQ,CAAC,CAAC;IAClCC,IAAI,EAAExD,YAAY;IAClByD,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE5B,kBAAkB,CAAC6B,QAAQ,CAAC,CAAC;IACpCC,IAAI,EAAEvD,aAAa;IACnBwD,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,oBACElD,OAAA;IAAKoD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBrD,OAAA;MAAKoD,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClET,SAAS,CAACD,GAAG,CAAEW,IAAI,iBAClBtD,OAAA;QAAqBoD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7DrD,OAAA;UAAKoD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrD,OAAA;YAAKoD,SAAS,EAAE,kBAAkBE,IAAI,CAACJ,OAAO,EAAG;YAAAG,QAAA,eAC/CrD,OAAA,CAACsD,IAAI,CAACN,IAAI;cAACI,SAAS,EAAE,WAAWE,IAAI,CAACL,KAAK;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACN1D,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBrD,OAAA;cAAGoD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEC,IAAI,CAACT;YAAI;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChE1D,OAAA;cAAGoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAEC,IAAI,CAACR;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GATEJ,IAAI,CAACT,IAAI;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1D,OAAA;MAAKoD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDrD,OAAA;QAAKoD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CrD,OAAA;UAAIoD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtErD,OAAA,CAACH,QAAQ;YAACuD,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1D,OAAA;UAAKoD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrD,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBAAKoD,SAAS,EAAC;cAAwC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9D1D,OAAA;gBAAMoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACN1D,OAAA;cAAMoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAEpC;YAAe;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACN1D,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBAAKoD,SAAS,EAAC;cAAyC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/D1D,OAAA;gBAAMoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN1D,OAAA;cAAMoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAElC;YAAgB;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eACN1D,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBAAKoD,SAAS,EAAC;cAAsC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5D1D,OAAA;gBAAMoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN1D,OAAA;cAAMoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAEnC;YAAkB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACN1D,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBAAKoD,SAAS,EAAC;cAAuC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7D1D,OAAA;gBAAMoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACN1D,OAAA;cAAMoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAE5C;YAAgB;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKoD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CrD,OAAA;UAAIoD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtErD,OAAA,CAACN,QAAQ;YAAC0D,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1D,OAAA;UAAKoD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrD,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrD,OAAA;cAAMoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnE1D,OAAA;cAAMoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAEhC;YAAkB;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACN1D,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrD,OAAA;cAAMoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClE1D,OAAA;cAAMoD,SAAS,EAAE,yBAAyB1B,gBAAgB,GAAG,CAAC,GAAG,iBAAiB,GAAG,eAAe,EAAG;cAAA2B,QAAA,EACpG3B;YAAgB;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN1D,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrD,OAAA;cAAMoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjE1D,OAAA;cAAMoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAElD,aAAa,GAAGkB;YAAkB;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,EACLhC,gBAAgB,GAAG,CAAC,iBACnB1B,OAAA;YAAKoD,SAAS,EAAC,2DAA2D;YAAAC,QAAA,eACxErD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA,CAACP,aAAa;gBAAC2D,SAAS,EAAC;cAA8B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1D1D,OAAA;gBAAMoD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GACtC3B,gBAAgB,EAAC,UAAQ,EAACA,gBAAgB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,gBAC7D;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKoD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CrD,OAAA;UAAIoD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtErD,OAAA,CAACF,GAAG;YAACsD,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1D,OAAA;UAAKoD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBlB,aAAa,CAAC/B,MAAM,GAAG,CAAC,GACvB+B,aAAa,CAACQ,GAAG,CAAC,CAAC,CAACb,QAAQ,EAAE6B,KAAK,CAAC,kBAClC3D,OAAA;YAAoBoD,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtFrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAMoD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEvB;cAAQ;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrE1D,OAAA;gBAAKoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACnCM,KAAK,CAAC5B,KAAK,EAAC,mBAAY,EAAC4B,KAAK,CAAC1B,UAAU,EAAC,QAC3C,EAAC0B,KAAK,CAACzB,QAAQ,GAAG,CAAC,iBACjBlC,OAAA;kBAAMoD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAC,SAAE,EAACM,KAAK,CAACzB,QAAQ,EAAC,YAAU;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1D,OAAA;cAAMoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,MAChD,EAACM,KAAK,CAAC3B,UAAU,CAACmB,cAAc,CAAC,CAAC;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA,GAZC5B,QAAQ;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAab,CACN,CAAC,gBAEF1D,OAAA;YAAGoD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC3E;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKoD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CrD,OAAA;UAAIoD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtErD,OAAA,CAACJ,SAAS;YAACwD,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,+BAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1D,OAAA;UAAKoD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBX,gBAAgB,CAACtC,MAAM,GAAG,CAAC,GAC1BsC,gBAAgB,CAACC,GAAG,CAAE9B,OAAO,iBAC3Bb,OAAA;YAAsBoD,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACxFrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAMoD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAExC,OAAO,CAACgC;cAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzE1D,OAAA;gBAAKoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACnCxC,OAAO,CAACE,aAAa,EAAC,eAAa,EAACF,OAAO,CAACC,KAAK,CAACqC,cAAc,CAAC,CAAC,EAAC,OACtE;cAAA;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1D,OAAA;cAAMoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,MAChD,EAACxC,OAAO,CAACmB,UAAU,CAACmB,cAAc,CAAC,CAAC;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA,GATC7C,OAAO,CAAC+C,EAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUf,CACN,CAAC,gBAEF1D,OAAA;YAAGoD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACzE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKoD,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DrD,OAAA;UAAIoD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACtErD,OAAA,CAACL,UAAU;YAACyD,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1D,OAAA;UAAKoD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDrD,OAAA;YAAKoD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDrD,OAAA;cAAGoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D1D,OAAA;cAAGoD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,MAC3C,EAAC3C,eAAe,CAACyC,cAAc,CAAC,CAAC;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN1D,OAAA;YAAKoD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDrD,OAAA;cAAGoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9D1D,OAAA;cAAGoD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,MAC1C,EAACrC,YAAY,CAACmC,cAAc,CAAC,CAAC;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN1D,OAAA;YAAKoD,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBACtDrD,OAAA;cAAGoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnD1D,OAAA;cAAGoD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAC9CjB,MAAM,CAACyB,IAAI,CAACjC,aAAa,CAAC,CAACxB;YAAM;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GAhRI7D,cAA6C;AAkRnD,eAAeA,cAAc;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}