{"ast": null, "code": "'use strict';\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\nexports.f = Object.getOwnPropertySymbols;", "map": {"version": 3, "names": ["exports", "f", "Object", "getOwnPropertySymbols"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/object-get-own-property-symbols.js"], "sourcesContent": ["'use strict';\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\nexports.f = Object.getOwnPropertySymbols;\n"], "mappings": "AAAA,YAAY;;AACZ;AACAA,OAAO,CAACC,CAAC,GAAGC,MAAM,CAACC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}