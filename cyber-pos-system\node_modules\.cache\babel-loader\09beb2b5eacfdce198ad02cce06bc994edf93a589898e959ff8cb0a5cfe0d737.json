{"ast": null, "code": "import _asyncToGenerator from '@babel/runtime/helpers/asyncToGenerator';\nimport _defineProperty from '@babel/runtime/helpers/defineProperty';\nimport requestAnimationFrame from 'raf';\nimport RGBColor from 'rgbcolor';\nimport { SVGPathData } from 'svg-pathdata';\nimport { canvasRGBA } from 'stackblur-canvas';\n\n/**\r\n * Options preset for `OffscreenCanvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @returns Preset object.\r\n */\nfunction offscreen() {\n  var {\n    DOMParser: DOMParserFallback\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var preset = {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser: DOMParserFallback,\n    createCanvas(width, height) {\n      return new OffscreenCanvas(width, height);\n    },\n    createImage(url) {\n      return _asyncToGenerator(function* () {\n        var response = yield fetch(url);\n        var blob = yield response.blob();\n        var img = yield createImageBitmap(blob);\n        return img;\n      })();\n    }\n  };\n  if (typeof DOMParser !== 'undefined' || typeof DOMParserFallback === 'undefined') {\n    Reflect.deleteProperty(preset, 'DOMParser');\n  }\n  return preset;\n}\n\n/**\r\n * Options preset for `node-canvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @param config.canvas - `node-canvas` exports.\r\n * @param config.fetch - WHATWG-compatible `fetch` function.\r\n * @returns Preset object.\r\n */\nfunction node(_ref) {\n  var {\n    DOMParser,\n    canvas,\n    fetch\n  } = _ref;\n  return {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser,\n    fetch,\n    createCanvas: canvas.createCanvas,\n    createImage: canvas.loadImage\n  };\n}\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  offscreen: offscreen,\n  node: node\n});\n\n/**\r\n * HTML-safe compress white-spaces.\r\n * @param str - String to compress.\r\n * @returns String.\r\n */\nfunction compressSpaces(str) {\n  return str.replace(/(?!\\u3000)\\s+/gm, ' ');\n}\n/**\r\n * HTML-safe left trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimLeft(str) {\n  return str.replace(/^[\\n \\t]+/, '');\n}\n/**\r\n * HTML-safe right trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimRight(str) {\n  return str.replace(/[\\n \\t]+$/, '');\n}\n/**\r\n * String to numbers array.\r\n * @param str - Numbers string.\r\n * @returns Numbers array.\r\n */\n\nfunction toNumbers(str) {\n  var matches = (str || '').match(/-?(\\d+(?:\\.\\d*(?:[eE][+-]?\\d+)?)?|\\.\\d+)(?=\\D|$)/gm) || [];\n  return matches.map(parseFloat);\n} // Microsoft Edge fix\n\nvar allUppercase = /^[A-Z-]+$/;\n/**\r\n * Normalize attribute name.\r\n * @param name - Attribute name.\r\n * @returns Normalized attribute name.\r\n */\n\nfunction normalizeAttributeName(name) {\n  if (allUppercase.test(name)) {\n    return name.toLowerCase();\n  }\n  return name;\n}\n/**\r\n * Parse external URL.\r\n * @param url - CSS url string.\r\n * @returns Parsed URL.\r\n */\n\nfunction parseExternalUrl(url) {\n  //                      single quotes [2]\n  //                      v         double quotes [3]\n  //                      v         v         no quotes [4]\n  //                      v         v         v\n  var urlMatch = /url\\(('([^']+)'|\"([^\"]+)\"|([^'\")]+))\\)/.exec(url) || [];\n  return urlMatch[2] || urlMatch[3] || urlMatch[4];\n}\n/**\r\n * Transform floats to integers in rgb colors.\r\n * @param color - Color to normalize.\r\n * @returns Normalized color.\r\n */\n\nfunction normalizeColor(color) {\n  if (!color.startsWith('rgb')) {\n    return color;\n  }\n  var rgbParts = 3;\n  var normalizedColor = color.replace(/\\d+(\\.\\d+)?/g, (num, isFloat) => rgbParts-- && isFloat ? String(Math.round(parseFloat(num))) : num);\n  return normalizedColor;\n}\n\n// slightly modified version of https://github.com/keeganstreet/specificity/blob/master/specificity.js\nvar attributeRegex = /(\\[[^\\]]+\\])/g;\nvar idRegex = /(#[^\\s+>~.[:]+)/g;\nvar classRegex = /(\\.[^\\s+>~.[:]+)/g;\nvar pseudoElementRegex = /(::[^\\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi;\nvar pseudoClassWithBracketsRegex = /(:[\\w-]+\\([^)]*\\))/gi;\nvar pseudoClassRegex = /(:[^\\s+>~.[:]+)/g;\nvar elementRegex = /([^\\s+>~.[:]+)/g;\nfunction findSelectorMatch(selector, regex) {\n  var matches = regex.exec(selector);\n  if (!matches) {\n    return [selector, 0];\n  }\n  return [selector.replace(regex, ' '), matches.length];\n}\n/**\r\n * Measure selector specificity.\r\n * @param selector - Selector to measure.\r\n * @returns Specificity.\r\n */\n\nfunction getSelectorSpecificity(selector) {\n  var specificity = [0, 0, 0];\n  var currentSelector = selector.replace(/:not\\(([^)]*)\\)/g, '     $1 ').replace(/{[\\s\\S]*/gm, ' ');\n  var delta = 0;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, attributeRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, idRegex);\n  specificity[0] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, classRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoElementRegex);\n  specificity[2] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassWithBracketsRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassRegex);\n  specificity[1] += delta;\n  currentSelector = currentSelector.replace(/[*\\s+>~]/g, ' ').replace(/[#.]/g, ' ');\n  [currentSelector, delta] = findSelectorMatch(currentSelector, elementRegex); // lgtm [js/useless-assignment-to-local]\n\n  specificity[2] += delta;\n  return specificity.join('');\n}\nvar PSEUDO_ZERO = .00000001;\n/**\r\n * Vector magnitude.\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorMagnitude(v) {\n  return Math.sqrt(Math.pow(v[0], 2) + Math.pow(v[1], 2));\n}\n/**\r\n * Ratio between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsRatio(u, v) {\n  return (u[0] * v[0] + u[1] * v[1]) / (vectorMagnitude(u) * vectorMagnitude(v));\n}\n/**\r\n * Angle between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsAngle(u, v) {\n  return (u[0] * v[1] < u[1] * v[0] ? -1 : 1) * Math.acos(vectorsRatio(u, v));\n}\nfunction CB1(t) {\n  return t * t * t;\n}\nfunction CB2(t) {\n  return 3 * t * t * (1 - t);\n}\nfunction CB3(t) {\n  return 3 * t * (1 - t) * (1 - t);\n}\nfunction CB4(t) {\n  return (1 - t) * (1 - t) * (1 - t);\n}\nfunction QB1(t) {\n  return t * t;\n}\nfunction QB2(t) {\n  return 2 * t * (1 - t);\n}\nfunction QB3(t) {\n  return (1 - t) * (1 - t);\n}\nclass Property {\n  constructor(document, name, value) {\n    this.document = document;\n    this.name = name;\n    this.value = value;\n    this.isNormalizedColor = false;\n  }\n  static empty(document) {\n    return new Property(document, 'EMPTY', '');\n  }\n  split() {\n    var separator = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ' ';\n    var {\n      document,\n      name\n    } = this;\n    return compressSpaces(this.getString()).trim().split(separator).map(value => new Property(document, name, value));\n  }\n  hasValue(zeroIsValue) {\n    var {\n      value\n    } = this;\n    return value !== null && value !== '' && (zeroIsValue || value !== 0) && typeof value !== 'undefined';\n  }\n  isString(regexp) {\n    var {\n      value\n    } = this;\n    var result = typeof value === 'string';\n    if (!result || !regexp) {\n      return result;\n    }\n    return regexp.test(value);\n  }\n  isUrlDefinition() {\n    return this.isString(/^url\\(/);\n  }\n  isPixels() {\n    if (!this.hasValue()) {\n      return false;\n    }\n    var asString = this.getString();\n    switch (true) {\n      case asString.endsWith('px'):\n      case /^[0-9]+$/.test(asString):\n        return true;\n      default:\n        return false;\n    }\n  }\n  setValue(value) {\n    this.value = value;\n    return this;\n  }\n  getValue(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return this.value;\n    }\n    return def;\n  }\n  getNumber(def) {\n    if (!this.hasValue()) {\n      if (typeof def === 'undefined') {\n        return 0;\n      }\n      return parseFloat(def);\n    }\n    var {\n      value\n    } = this;\n    var n = parseFloat(value);\n    if (this.isString(/%$/)) {\n      n /= 100.0;\n    }\n    return n;\n  }\n  getString(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return typeof this.value === 'undefined' ? '' : String(this.value);\n    }\n    return String(def);\n  }\n  getColor(def) {\n    var color = this.getString(def);\n    if (this.isNormalizedColor) {\n      return color;\n    }\n    this.isNormalizedColor = true;\n    color = normalizeColor(color);\n    this.value = color;\n    return color;\n  }\n  getDpi() {\n    return 96.0; // TODO: compute?\n  }\n  getRem() {\n    return this.document.rootEmSize;\n  }\n  getEm() {\n    return this.document.emSize;\n  }\n  getUnits() {\n    return this.getString().replace(/[0-9.-]/g, '');\n  }\n  getPixels(axisOrIsFontSize) {\n    var processPercent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (!this.hasValue()) {\n      return 0;\n    }\n    var [axis, isFontSize] = typeof axisOrIsFontSize === 'boolean' ? [undefined, axisOrIsFontSize] : [axisOrIsFontSize];\n    var {\n      viewPort\n    } = this.document.screen;\n    switch (true) {\n      case this.isString(/vmin$/):\n        return this.getNumber() / 100.0 * Math.min(viewPort.computeSize('x'), viewPort.computeSize('y'));\n      case this.isString(/vmax$/):\n        return this.getNumber() / 100.0 * Math.max(viewPort.computeSize('x'), viewPort.computeSize('y'));\n      case this.isString(/vw$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('x');\n      case this.isString(/vh$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('y');\n      case this.isString(/rem$/):\n        return this.getNumber() * this.getRem();\n      case this.isString(/em$/):\n        return this.getNumber() * this.getEm();\n      case this.isString(/ex$/):\n        return this.getNumber() * this.getEm() / 2.0;\n      case this.isString(/px$/):\n        return this.getNumber();\n      case this.isString(/pt$/):\n        return this.getNumber() * this.getDpi() * (1.0 / 72.0);\n      case this.isString(/pc$/):\n        return this.getNumber() * 15;\n      case this.isString(/cm$/):\n        return this.getNumber() * this.getDpi() / 2.54;\n      case this.isString(/mm$/):\n        return this.getNumber() * this.getDpi() / 25.4;\n      case this.isString(/in$/):\n        return this.getNumber() * this.getDpi();\n      case this.isString(/%$/) && isFontSize:\n        return this.getNumber() * this.getEm();\n      case this.isString(/%$/):\n        return this.getNumber() * viewPort.computeSize(axis);\n      default:\n        {\n          var n = this.getNumber();\n          if (processPercent && n < 1.0) {\n            return n * viewPort.computeSize(axis);\n          }\n          return n;\n        }\n    }\n  }\n  getMilliseconds() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n    if (this.isString(/ms$/)) {\n      return this.getNumber();\n    }\n    return this.getNumber() * 1000;\n  }\n  getRadians() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n    switch (true) {\n      case this.isString(/deg$/):\n        return this.getNumber() * (Math.PI / 180.0);\n      case this.isString(/grad$/):\n        return this.getNumber() * (Math.PI / 200.0);\n      case this.isString(/rad$/):\n        return this.getNumber();\n      default:\n        return this.getNumber() * (Math.PI / 180.0);\n    }\n  }\n  getDefinition() {\n    var asString = this.getString();\n    var name = /#([^)'\"]+)/.exec(asString);\n    if (name) {\n      name = name[1];\n    }\n    if (!name) {\n      name = asString;\n    }\n    return this.document.definitions[name];\n  }\n  getFillStyleDefinition(element, opacity) {\n    var def = this.getDefinition();\n    if (!def) {\n      return null;\n    } // gradient\n\n    if (typeof def.createGradient === 'function') {\n      return def.createGradient(this.document.ctx, element, opacity);\n    } // pattern\n\n    if (typeof def.createPattern === 'function') {\n      if (def.getHrefAttribute().hasValue()) {\n        var patternTransform = def.getAttribute('patternTransform');\n        def = def.getHrefAttribute().getDefinition();\n        if (patternTransform.hasValue()) {\n          def.getAttribute('patternTransform', true).setValue(patternTransform.value);\n        }\n      }\n      return def.createPattern(this.document.ctx, element, opacity);\n    }\n    return null;\n  }\n  getTextBaseline() {\n    if (!this.hasValue()) {\n      return null;\n    }\n    return Property.textBaselineMapping[this.getString()];\n  }\n  addOpacity(opacity) {\n    var value = this.getColor();\n    var len = value.length;\n    var commas = 0; // Simulate old RGBColor version, which can't parse rgba.\n\n    for (var i = 0; i < len; i++) {\n      if (value[i] === ',') {\n        commas++;\n      }\n      if (commas === 3) {\n        break;\n      }\n    }\n    if (opacity.hasValue() && this.isString() && commas !== 3) {\n      var color = new RGBColor(value);\n      if (color.ok) {\n        color.alpha = opacity.getNumber();\n        value = color.toRGBA();\n      }\n    }\n    return new Property(this.document, this.name, value);\n  }\n}\nProperty.textBaselineMapping = {\n  'baseline': 'alphabetic',\n  'before-edge': 'top',\n  'text-before-edge': 'top',\n  'middle': 'middle',\n  'central': 'middle',\n  'after-edge': 'bottom',\n  'text-after-edge': 'bottom',\n  'ideographic': 'ideographic',\n  'alphabetic': 'alphabetic',\n  'hanging': 'hanging',\n  'mathematical': 'alphabetic'\n};\nclass ViewPort {\n  constructor() {\n    this.viewPorts = [];\n  }\n  clear() {\n    this.viewPorts = [];\n  }\n  setCurrent(width, height) {\n    this.viewPorts.push({\n      width,\n      height\n    });\n  }\n  removeCurrent() {\n    this.viewPorts.pop();\n  }\n  getCurrent() {\n    var {\n      viewPorts\n    } = this;\n    return viewPorts[viewPorts.length - 1];\n  }\n  get width() {\n    return this.getCurrent().width;\n  }\n  get height() {\n    return this.getCurrent().height;\n  }\n  computeSize(d) {\n    if (typeof d === 'number') {\n      return d;\n    }\n    if (d === 'x') {\n      return this.width;\n    }\n    if (d === 'y') {\n      return this.height;\n    }\n    return Math.sqrt(Math.pow(this.width, 2) + Math.pow(this.height, 2)) / Math.sqrt(2);\n  }\n}\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n  static parse(point) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var [x = defaultValue, y = defaultValue] = toNumbers(point);\n    return new Point(x, y);\n  }\n  static parseScale(scale) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var [x = defaultValue, y = x] = toNumbers(scale);\n    return new Point(x, y);\n  }\n  static parsePath(path) {\n    var points = toNumbers(path);\n    var len = points.length;\n    var pathPoints = [];\n    for (var i = 0; i < len; i += 2) {\n      pathPoints.push(new Point(points[i], points[i + 1]));\n    }\n    return pathPoints;\n  }\n  angleTo(point) {\n    return Math.atan2(point.y - this.y, point.x - this.x);\n  }\n  applyTransform(transform) {\n    var {\n      x,\n      y\n    } = this;\n    var xp = x * transform[0] + y * transform[2] + transform[4];\n    var yp = x * transform[1] + y * transform[3] + transform[5];\n    this.x = xp;\n    this.y = yp;\n  }\n}\nclass Mouse {\n  constructor(screen) {\n    this.screen = screen;\n    this.working = false;\n    this.events = [];\n    this.eventElements = []; // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onClick = this.onClick.bind(this); // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onMouseMove = this.onMouseMove.bind(this);\n  }\n  isWorking() {\n    return this.working;\n  }\n  start() {\n    if (this.working) {\n      return;\n    }\n    var {\n      screen,\n      onClick,\n      onMouseMove\n    } = this;\n    var canvas = screen.ctx.canvas;\n    canvas.onclick = onClick;\n    canvas.onmousemove = onMouseMove;\n    this.working = true;\n  }\n  stop() {\n    if (!this.working) {\n      return;\n    }\n    var canvas = this.screen.ctx.canvas;\n    this.working = false;\n    canvas.onclick = null;\n    canvas.onmousemove = null;\n  }\n  hasEvents() {\n    return this.working && this.events.length > 0;\n  }\n  runEvents() {\n    if (!this.working) {\n      return;\n    }\n    var {\n      screen: document,\n      events,\n      eventElements\n    } = this;\n    var {\n      style\n    } = document.ctx.canvas;\n    if (style) {\n      style.cursor = '';\n    }\n    events.forEach((_ref, i) => {\n      var {\n        run\n      } = _ref;\n      var element = eventElements[i];\n      while (element) {\n        run(element);\n        element = element.parent;\n      }\n    }); // done running, clear\n\n    this.events = [];\n    this.eventElements = [];\n  }\n  checkPath(element, ctx) {\n    if (!this.working || !ctx) {\n      return;\n    }\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref2, i) => {\n      var {\n        x,\n        y\n      } = _ref2;\n      if (!eventElements[i] && ctx.isPointInPath && ctx.isPointInPath(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n  checkBoundingBox(element, boundingBox) {\n    if (!this.working || !boundingBox) {\n      return;\n    }\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref3, i) => {\n      var {\n        x,\n        y\n      } = _ref3;\n      if (!eventElements[i] && boundingBox.isPointInBox(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n  mapXY(x, y) {\n    var {\n      window,\n      ctx\n    } = this.screen;\n    var point = new Point(x, y);\n    var element = ctx.canvas;\n    while (element) {\n      point.x -= element.offsetLeft;\n      point.y -= element.offsetTop;\n      element = element.offsetParent;\n    }\n    if (window.scrollX) {\n      point.x += window.scrollX;\n    }\n    if (window.scrollY) {\n      point.y += window.scrollY;\n    }\n    return point;\n  }\n  onClick(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onclick',\n      x,\n      y,\n      run(eventTarget) {\n        if (eventTarget.onClick) {\n          eventTarget.onClick();\n        }\n      }\n    });\n  }\n  onMouseMove(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onmousemove',\n      x,\n      y,\n      run(eventTarget) {\n        if (eventTarget.onMouseMove) {\n          eventTarget.onMouseMove();\n        }\n      }\n    });\n  }\n}\nvar defaultWindow = typeof window !== 'undefined' ? window : null;\nvar defaultFetch$1 = typeof fetch !== 'undefined' ? fetch.bind(undefined) // `fetch` depends on context: `someObject.fetch(...)` will throw error.\n: null;\nclass Screen {\n  constructor(ctx) {\n    var {\n      fetch = defaultFetch$1,\n      window = defaultWindow\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.ctx = ctx;\n    this.FRAMERATE = 30;\n    this.MAX_VIRTUAL_PIXELS = 30000;\n    this.CLIENT_WIDTH = 800;\n    this.CLIENT_HEIGHT = 600;\n    this.viewPort = new ViewPort();\n    this.mouse = new Mouse(this);\n    this.animations = [];\n    this.waits = [];\n    this.frameDuration = 0;\n    this.isReadyLock = false;\n    this.isFirstRender = true;\n    this.intervalId = null;\n    this.window = window;\n    this.fetch = fetch;\n  }\n  wait(checker) {\n    this.waits.push(checker);\n  }\n  ready() {\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    if (!this.readyPromise) {\n      return Promise.resolve();\n    }\n    return this.readyPromise;\n  }\n  isReady() {\n    if (this.isReadyLock) {\n      return true;\n    }\n    var isReadyLock = this.waits.every(_ => _());\n    if (isReadyLock) {\n      this.waits = [];\n      if (this.resolveReady) {\n        this.resolveReady();\n      }\n    }\n    this.isReadyLock = isReadyLock;\n    return isReadyLock;\n  }\n  setDefaults(ctx) {\n    // initial values and defaults\n    ctx.strokeStyle = 'rgba(0,0,0,0)';\n    ctx.lineCap = 'butt';\n    ctx.lineJoin = 'miter';\n    ctx.miterLimit = 4;\n  }\n  setViewBox(_ref) {\n    var {\n      document,\n      ctx,\n      aspectRatio,\n      width,\n      desiredWidth,\n      height,\n      desiredHeight,\n      minX = 0,\n      minY = 0,\n      refX,\n      refY,\n      clip = false,\n      clipX = 0,\n      clipY = 0\n    } = _ref;\n    // aspect ratio - http://www.w3.org/TR/SVG/coords.html#PreserveAspectRatioAttribute\n    var cleanAspectRatio = compressSpaces(aspectRatio).replace(/^defer\\s/, ''); // ignore defer\n\n    var [aspectRatioAlign, aspectRatioMeetOrSlice] = cleanAspectRatio.split(' ');\n    var align = aspectRatioAlign || 'xMidYMid';\n    var meetOrSlice = aspectRatioMeetOrSlice || 'meet'; // calculate scale\n\n    var scaleX = width / desiredWidth;\n    var scaleY = height / desiredHeight;\n    var scaleMin = Math.min(scaleX, scaleY);\n    var scaleMax = Math.max(scaleX, scaleY);\n    var finalDesiredWidth = desiredWidth;\n    var finalDesiredHeight = desiredHeight;\n    if (meetOrSlice === 'meet') {\n      finalDesiredWidth *= scaleMin;\n      finalDesiredHeight *= scaleMin;\n    }\n    if (meetOrSlice === 'slice') {\n      finalDesiredWidth *= scaleMax;\n      finalDesiredHeight *= scaleMax;\n    }\n    var refXProp = new Property(document, 'refX', refX);\n    var refYProp = new Property(document, 'refY', refY);\n    var hasRefs = refXProp.hasValue() && refYProp.hasValue();\n    if (hasRefs) {\n      ctx.translate(-scaleMin * refXProp.getPixels('x'), -scaleMin * refYProp.getPixels('y'));\n    }\n    if (clip) {\n      var scaledClipX = scaleMin * clipX;\n      var scaledClipY = scaleMin * clipY;\n      ctx.beginPath();\n      ctx.moveTo(scaledClipX, scaledClipY);\n      ctx.lineTo(width, scaledClipY);\n      ctx.lineTo(width, height);\n      ctx.lineTo(scaledClipX, height);\n      ctx.closePath();\n      ctx.clip();\n    }\n    if (!hasRefs) {\n      var isMeetMinY = meetOrSlice === 'meet' && scaleMin === scaleY;\n      var isSliceMaxY = meetOrSlice === 'slice' && scaleMax === scaleY;\n      var isMeetMinX = meetOrSlice === 'meet' && scaleMin === scaleX;\n      var isSliceMaxX = meetOrSlice === 'slice' && scaleMax === scaleX;\n      if (align.startsWith('xMid') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width / 2.0 - finalDesiredWidth / 2.0, 0);\n      }\n      if (align.endsWith('YMid') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height / 2.0 - finalDesiredHeight / 2.0);\n      }\n      if (align.startsWith('xMax') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width - finalDesiredWidth, 0);\n      }\n      if (align.endsWith('YMax') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height - finalDesiredHeight);\n      }\n    } // scale\n\n    switch (true) {\n      case align === 'none':\n        ctx.scale(scaleX, scaleY);\n        break;\n      case meetOrSlice === 'meet':\n        ctx.scale(scaleMin, scaleMin);\n        break;\n      case meetOrSlice === 'slice':\n        ctx.scale(scaleMax, scaleMax);\n        break;\n    } // translate\n\n    ctx.translate(-minX, -minY);\n  }\n  start(element) {\n    var {\n      enableRedraw = false,\n      ignoreMouse = false,\n      ignoreAnimation = false,\n      ignoreDimensions = false,\n      ignoreClear = false,\n      forceRedraw,\n      scaleWidth,\n      scaleHeight,\n      offsetX,\n      offsetY\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var {\n      FRAMERATE,\n      mouse\n    } = this;\n    var frameDuration = 1000 / FRAMERATE;\n    this.frameDuration = frameDuration;\n    this.readyPromise = new Promise(resolve => {\n      this.resolveReady = resolve;\n    });\n    if (this.isReady()) {\n      this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n    }\n    if (!enableRedraw) {\n      return;\n    }\n    var now = Date.now();\n    var then = now;\n    var delta = 0;\n    var tick = () => {\n      now = Date.now();\n      delta = now - then;\n      if (delta >= frameDuration) {\n        then = now - delta % frameDuration;\n        if (this.shouldUpdate(ignoreAnimation, forceRedraw)) {\n          this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n          mouse.runEvents();\n        }\n      }\n      this.intervalId = requestAnimationFrame(tick);\n    };\n    if (!ignoreMouse) {\n      mouse.start();\n    }\n    this.intervalId = requestAnimationFrame(tick);\n  }\n  stop() {\n    if (this.intervalId) {\n      requestAnimationFrame.cancel(this.intervalId);\n      this.intervalId = null;\n    }\n    this.mouse.stop();\n  }\n  shouldUpdate(ignoreAnimation, forceRedraw) {\n    // need update from animations?\n    if (!ignoreAnimation) {\n      var {\n        frameDuration\n      } = this;\n      var shouldUpdate = this.animations.reduce((shouldUpdate, animation) => animation.update(frameDuration) || shouldUpdate, false);\n      if (shouldUpdate) {\n        return true;\n      }\n    } // need update from redraw?\n\n    if (typeof forceRedraw === 'function' && forceRedraw()) {\n      return true;\n    }\n    if (!this.isReadyLock && this.isReady()) {\n      return true;\n    } // need update from mouse events?\n\n    if (this.mouse.hasEvents()) {\n      return true;\n    }\n    return false;\n  }\n  render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY) {\n    var {\n      CLIENT_WIDTH,\n      CLIENT_HEIGHT,\n      viewPort,\n      ctx,\n      isFirstRender\n    } = this;\n    var canvas = ctx.canvas;\n    viewPort.clear();\n    if (canvas.width && canvas.height) {\n      viewPort.setCurrent(canvas.width, canvas.height);\n    } else {\n      viewPort.setCurrent(CLIENT_WIDTH, CLIENT_HEIGHT);\n    }\n    var widthStyle = element.getStyle('width');\n    var heightStyle = element.getStyle('height');\n    if (!ignoreDimensions && (isFirstRender || typeof scaleWidth !== 'number' && typeof scaleHeight !== 'number')) {\n      // set canvas size\n      if (widthStyle.hasValue()) {\n        canvas.width = widthStyle.getPixels('x');\n        if (canvas.style) {\n          canvas.style.width = \"\".concat(canvas.width, \"px\");\n        }\n      }\n      if (heightStyle.hasValue()) {\n        canvas.height = heightStyle.getPixels('y');\n        if (canvas.style) {\n          canvas.style.height = \"\".concat(canvas.height, \"px\");\n        }\n      }\n    }\n    var cWidth = canvas.clientWidth || canvas.width;\n    var cHeight = canvas.clientHeight || canvas.height;\n    if (ignoreDimensions && widthStyle.hasValue() && heightStyle.hasValue()) {\n      cWidth = widthStyle.getPixels('x');\n      cHeight = heightStyle.getPixels('y');\n    }\n    viewPort.setCurrent(cWidth, cHeight);\n    if (typeof offsetX === 'number') {\n      element.getAttribute('x', true).setValue(offsetX);\n    }\n    if (typeof offsetY === 'number') {\n      element.getAttribute('y', true).setValue(offsetY);\n    }\n    if (typeof scaleWidth === 'number' || typeof scaleHeight === 'number') {\n      var viewBox = toNumbers(element.getAttribute('viewBox').getString());\n      var xRatio = 0;\n      var yRatio = 0;\n      if (typeof scaleWidth === 'number') {\n        var _widthStyle = element.getStyle('width');\n        if (_widthStyle.hasValue()) {\n          xRatio = _widthStyle.getPixels('x') / scaleWidth;\n        } else if (!isNaN(viewBox[2])) {\n          xRatio = viewBox[2] / scaleWidth;\n        }\n      }\n      if (typeof scaleHeight === 'number') {\n        var _heightStyle = element.getStyle('height');\n        if (_heightStyle.hasValue()) {\n          yRatio = _heightStyle.getPixels('y') / scaleHeight;\n        } else if (!isNaN(viewBox[3])) {\n          yRatio = viewBox[3] / scaleHeight;\n        }\n      }\n      if (!xRatio) {\n        xRatio = yRatio;\n      }\n      if (!yRatio) {\n        yRatio = xRatio;\n      }\n      element.getAttribute('width', true).setValue(scaleWidth);\n      element.getAttribute('height', true).setValue(scaleHeight);\n      var transformStyle = element.getStyle('transform', true, true);\n      transformStyle.setValue(\"\".concat(transformStyle.getString(), \" scale(\").concat(1.0 / xRatio, \", \").concat(1.0 / yRatio, \")\"));\n    } // clear and render\n\n    if (!ignoreClear) {\n      ctx.clearRect(0, 0, cWidth, cHeight);\n    }\n    element.render(ctx);\n    if (isFirstRender) {\n      this.isFirstRender = false;\n    }\n  }\n}\nScreen.defaultWindow = defaultWindow;\nScreen.defaultFetch = defaultFetch$1;\nvar {\n  defaultFetch\n} = Screen;\nvar DefaultDOMParser = typeof DOMParser !== 'undefined' ? DOMParser : null;\nclass Parser {\n  constructor() {\n    var {\n      fetch = defaultFetch,\n      DOMParser = DefaultDOMParser\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.fetch = fetch;\n    this.DOMParser = DOMParser;\n  }\n  parse(resource) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (resource.startsWith('<')) {\n        return _this.parseFromString(resource);\n      }\n      return _this.load(resource);\n    })();\n  }\n  parseFromString(xml) {\n    var parser = new this.DOMParser();\n    try {\n      return this.checkDocument(parser.parseFromString(xml, 'image/svg+xml'));\n    } catch (err) {\n      return this.checkDocument(parser.parseFromString(xml, 'text/xml'));\n    }\n  }\n  checkDocument(document) {\n    var parserError = document.getElementsByTagName('parsererror')[0];\n    if (parserError) {\n      throw new Error(parserError.textContent);\n    }\n    return document;\n  }\n  load(url) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      var response = yield _this2.fetch(url);\n      var xml = yield response.text();\n      return _this2.parseFromString(xml);\n    })();\n  }\n}\nclass Translate {\n  constructor(_, point) {\n    this.type = 'translate';\n    this.point = null;\n    this.point = Point.parse(point);\n  }\n  apply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(x || 0.0, y || 0.0);\n  }\n  unapply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(-1.0 * x || 0.0, -1.0 * y || 0.0);\n  }\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.point;\n    point.applyTransform([1, 0, 0, 1, x || 0.0, y || 0.0]);\n  }\n}\nclass Rotate {\n  constructor(document, rotate, transformOrigin) {\n    this.type = 'rotate';\n    this.angle = null;\n    this.originX = null;\n    this.originY = null;\n    this.cx = 0;\n    this.cy = 0;\n    var numbers = toNumbers(rotate);\n    this.angle = new Property(document, 'angle', numbers[0]);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n    this.cx = numbers[1] || 0;\n    this.cy = numbers[2] || 0;\n  }\n  apply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n  unapply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(-1.0 * angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n  applyToPoint(point) {\n    var {\n      cx,\n      cy,\n      angle\n    } = this;\n    var rad = angle.getRadians();\n    point.applyTransform([1, 0, 0, 1, cx || 0.0, cy || 0.0 // this.p.y\n    ]);\n    point.applyTransform([Math.cos(rad), Math.sin(rad), -Math.sin(rad), Math.cos(rad), 0, 0]);\n    point.applyTransform([1, 0, 0, 1, -cx || 0.0, -cy || 0.0 // -this.p.y\n    ]);\n  }\n}\nclass Scale {\n  constructor(_, scale, transformOrigin) {\n    this.type = 'scale';\n    this.scale = null;\n    this.originX = null;\n    this.originY = null;\n    var scaleSize = Point.parseScale(scale); // Workaround for node-canvas\n\n    if (scaleSize.x === 0 || scaleSize.y === 0) {\n      scaleSize.x = PSEUDO_ZERO;\n      scaleSize.y = PSEUDO_ZERO;\n    }\n    this.scale = scaleSize;\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n  apply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(x, y || x);\n    ctx.translate(-tx, -ty);\n  }\n  unapply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(1.0 / x, 1.0 / y || x);\n    ctx.translate(-tx, -ty);\n  }\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.scale;\n    point.applyTransform([x || 0.0, 0, 0, y || 0.0, 0, 0]);\n  }\n}\nclass Matrix {\n  constructor(_, matrix, transformOrigin) {\n    this.type = 'matrix';\n    this.matrix = [];\n    this.originX = null;\n    this.originY = null;\n    this.matrix = toNumbers(matrix);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n  apply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n    ctx.translate(-tx, -ty);\n  }\n  unapply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var a = matrix[0];\n    var b = matrix[2];\n    var c = matrix[4];\n    var d = matrix[1];\n    var e = matrix[3];\n    var f = matrix[5];\n    var g = 0.0;\n    var h = 0.0;\n    var i = 1.0;\n    var det = 1 / (a * (e * i - f * h) - b * (d * i - f * g) + c * (d * h - e * g));\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(det * (e * i - f * h), det * (f * g - d * i), det * (c * h - b * i), det * (a * i - c * g), det * (b * f - c * e), det * (c * d - a * f));\n    ctx.translate(-tx, -ty);\n  }\n  applyToPoint(point) {\n    point.applyTransform(this.matrix);\n  }\n}\nclass Skew extends Matrix {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skew';\n    this.angle = null;\n    this.angle = new Property(document, 'angle', skew);\n  }\n}\nclass SkewX extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewX';\n    this.matrix = [1, 0, Math.tan(this.angle.getRadians()), 1, 0, 0];\n  }\n}\nclass SkewY extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewY';\n    this.matrix = [1, Math.tan(this.angle.getRadians()), 0, 1, 0, 0];\n  }\n}\nfunction parseTransforms(transform) {\n  return compressSpaces(transform).trim().replace(/\\)([a-zA-Z])/g, ') $1').replace(/\\)(\\s?,\\s?)/g, ') ').split(/\\s(?=[a-z])/);\n}\nfunction parseTransform(transform) {\n  var [type, value] = transform.split('(');\n  return [type.trim(), value.trim().replace(')', '')];\n}\nclass Transform {\n  constructor(document, transform, transformOrigin) {\n    this.document = document;\n    this.transforms = [];\n    var data = parseTransforms(transform);\n    data.forEach(transform => {\n      if (transform === 'none') {\n        return;\n      }\n      var [type, value] = parseTransform(transform);\n      var TransformType = Transform.transformTypes[type];\n      if (typeof TransformType !== 'undefined') {\n        this.transforms.push(new TransformType(this.document, value, transformOrigin));\n      }\n    });\n  }\n  static fromElement(document, element) {\n    var transformStyle = element.getStyle('transform', false, true);\n    var [transformOriginXProperty, transformOriginYProperty = transformOriginXProperty] = element.getStyle('transform-origin', false, true).split();\n    var transformOrigin = [transformOriginXProperty, transformOriginYProperty];\n    if (transformStyle.hasValue()) {\n      return new Transform(document, transformStyle.getString(), transformOrigin);\n    }\n    return null;\n  }\n  apply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n    for (var i = 0; i < len; i++) {\n      transforms[i].apply(ctx);\n    }\n  }\n  unapply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n    for (var i = len - 1; i >= 0; i--) {\n      transforms[i].unapply(ctx);\n    }\n  } // TODO: applyToPoint unused ... remove?\n\n  applyToPoint(point) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n    for (var i = 0; i < len; i++) {\n      transforms[i].applyToPoint(point);\n    }\n  }\n}\nTransform.transformTypes = {\n  translate: Translate,\n  rotate: Rotate,\n  scale: Scale,\n  matrix: Matrix,\n  skewX: SkewX,\n  skewY: SkewY\n};\nclass Element {\n  constructor(document, node) {\n    var captureTextNodes = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.document = document;\n    this.node = node;\n    this.captureTextNodes = captureTextNodes;\n    this.attributes = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.animationFrozen = false;\n    this.animationFrozenValue = '';\n    this.parent = null;\n    this.children = [];\n    if (!node || node.nodeType !== 1) {\n      // ELEMENT_NODE\n      return;\n    } // add attributes\n\n    Array.from(node.attributes).forEach(attribute => {\n      var nodeName = normalizeAttributeName(attribute.nodeName);\n      this.attributes[nodeName] = new Property(document, nodeName, attribute.value);\n    });\n    this.addStylesFromStyleDefinition(); // add inline styles\n\n    if (this.getAttribute('style').hasValue()) {\n      var styles = this.getAttribute('style').getString().split(';').map(_ => _.trim());\n      styles.forEach(style => {\n        if (!style) {\n          return;\n        }\n        var [name, value] = style.split(':').map(_ => _.trim());\n        this.styles[name] = new Property(document, name, value);\n      });\n    }\n    var {\n      definitions\n    } = document;\n    var id = this.getAttribute('id'); // add id\n\n    if (id.hasValue()) {\n      if (!definitions[id.getString()]) {\n        definitions[id.getString()] = this;\n      }\n    }\n    Array.from(node.childNodes).forEach(childNode => {\n      if (childNode.nodeType === 1) {\n        this.addChild(childNode); // ELEMENT_NODE\n      } else if (captureTextNodes && (childNode.nodeType === 3 || childNode.nodeType === 4)) {\n        var textNode = document.createTextNode(childNode);\n        if (textNode.getText().length > 0) {\n          this.addChild(textNode); // TEXT_NODE\n        }\n      }\n    });\n  }\n  getAttribute(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var attr = this.attributes[name];\n    if (!attr && createIfNotExists) {\n      var _attr = new Property(this.document, name, '');\n      this.attributes[name] = _attr;\n      return _attr;\n    }\n    return attr || Property.empty(this.document);\n  }\n  getHrefAttribute() {\n    for (var key in this.attributes) {\n      if (key === 'href' || key.endsWith(':href')) {\n        return this.attributes[key];\n      }\n    }\n    return Property.empty(this.document);\n  }\n  getStyle(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var skipAncestors = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var style = this.styles[name];\n    if (style) {\n      return style;\n    }\n    var attr = this.getAttribute(name);\n    if (attr !== null && attr !== void 0 && attr.hasValue()) {\n      this.styles[name] = attr; // move up to me to cache\n\n      return attr;\n    }\n    if (!skipAncestors) {\n      var {\n        parent\n      } = this;\n      if (parent) {\n        var parentStyle = parent.getStyle(name);\n        if (parentStyle !== null && parentStyle !== void 0 && parentStyle.hasValue()) {\n          return parentStyle;\n        }\n      }\n    }\n    if (createIfNotExists) {\n      var _style = new Property(this.document, name, '');\n      this.styles[name] = _style;\n      return _style;\n    }\n    return style || Property.empty(this.document);\n  }\n  render(ctx) {\n    // don't render display=none\n    // don't render visibility=hidden\n    if (this.getStyle('display').getString() === 'none' || this.getStyle('visibility').getString() === 'hidden') {\n      return;\n    }\n    ctx.save();\n    if (this.getStyle('mask').hasValue()) {\n      // mask\n      var mask = this.getStyle('mask').getDefinition();\n      if (mask) {\n        this.applyEffects(ctx);\n        mask.apply(ctx, this);\n      }\n    } else if (this.getStyle('filter').getValue('none') !== 'none') {\n      // filter\n      var filter = this.getStyle('filter').getDefinition();\n      if (filter) {\n        this.applyEffects(ctx);\n        filter.apply(ctx, this);\n      }\n    } else {\n      this.setContext(ctx);\n      this.renderChildren(ctx);\n      this.clearContext(ctx);\n    }\n    ctx.restore();\n  }\n  setContext(_) {// NO RENDER\n  }\n  applyEffects(ctx) {\n    // transform\n    var transform = Transform.fromElement(this.document, this);\n    if (transform) {\n      transform.apply(ctx);\n    } // clip\n\n    var clipPathStyleProp = this.getStyle('clip-path', false, true);\n    if (clipPathStyleProp.hasValue()) {\n      var clip = clipPathStyleProp.getDefinition();\n      if (clip) {\n        clip.apply(ctx);\n      }\n    }\n  }\n  clearContext(_) {// NO RENDER\n  }\n  renderChildren(ctx) {\n    this.children.forEach(child => {\n      child.render(ctx);\n    });\n  }\n  addChild(childNode) {\n    var child = childNode instanceof Element ? childNode : this.document.createElement(childNode);\n    child.parent = this;\n    if (!Element.ignoreChildTypes.includes(child.type)) {\n      this.children.push(child);\n    }\n  }\n  matchesSelector(selector) {\n    var _node$getAttribute;\n    var {\n      node\n    } = this;\n    if (typeof node.matches === 'function') {\n      return node.matches(selector);\n    }\n    var styleClasses = (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'class');\n    if (!styleClasses || styleClasses === '') {\n      return false;\n    }\n    return styleClasses.split(' ').some(styleClass => \".\".concat(styleClass) === selector);\n  }\n  addStylesFromStyleDefinition() {\n    var {\n      styles,\n      stylesSpecificity\n    } = this.document;\n    for (var selector in styles) {\n      if (!selector.startsWith('@') && this.matchesSelector(selector)) {\n        var style = styles[selector];\n        var specificity = stylesSpecificity[selector];\n        if (style) {\n          for (var name in style) {\n            var existingSpecificity = this.stylesSpecificity[name];\n            if (typeof existingSpecificity === 'undefined') {\n              existingSpecificity = '000';\n            }\n            if (specificity >= existingSpecificity) {\n              this.styles[name] = style[name];\n              this.stylesSpecificity[name] = specificity;\n            }\n          }\n        }\n      }\n    }\n  }\n  removeStyles(element, ignoreStyles) {\n    var toRestore = ignoreStyles.reduce((toRestore, name) => {\n      var styleProp = element.getStyle(name);\n      if (!styleProp.hasValue()) {\n        return toRestore;\n      }\n      var value = styleProp.getString();\n      styleProp.setValue('');\n      return [...toRestore, [name, value]];\n    }, []);\n    return toRestore;\n  }\n  restoreStyles(element, styles) {\n    styles.forEach(_ref => {\n      var [name, value] = _ref;\n      element.getStyle(name, true).setValue(value);\n    });\n  }\n  isFirstChild() {\n    var _this$parent;\n    return ((_this$parent = this.parent) === null || _this$parent === void 0 ? void 0 : _this$parent.children.indexOf(this)) === 0;\n  }\n}\nElement.ignoreChildTypes = ['title'];\nclass UnknownElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n  }\n}\nfunction wrapFontFamily(fontFamily) {\n  var trimmed = fontFamily.trim();\n  return /^('|\")/.test(trimmed) ? trimmed : \"\\\"\".concat(trimmed, \"\\\"\");\n}\nfunction prepareFontFamily(fontFamily) {\n  return typeof process === 'undefined' ? fontFamily : fontFamily.trim().split(',').map(wrapFontFamily).join(',');\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-style\r\n * @param fontStyle\r\n * @returns CSS font style.\r\n */\n\nfunction prepareFontStyle(fontStyle) {\n  if (!fontStyle) {\n    return '';\n  }\n  var targetFontStyle = fontStyle.trim().toLowerCase();\n  switch (targetFontStyle) {\n    case 'normal':\n    case 'italic':\n    case 'oblique':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontStyle;\n    default:\n      if (/^oblique\\s+(-|)\\d+deg$/.test(targetFontStyle)) {\n        return targetFontStyle;\n      }\n      return '';\n  }\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight\r\n * @param fontWeight\r\n * @returns CSS font weight.\r\n */\n\nfunction prepareFontWeight(fontWeight) {\n  if (!fontWeight) {\n    return '';\n  }\n  var targetFontWeight = fontWeight.trim().toLowerCase();\n  switch (targetFontWeight) {\n    case 'normal':\n    case 'bold':\n    case 'lighter':\n    case 'bolder':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontWeight;\n    default:\n      if (/^[\\d.]+$/.test(targetFontWeight)) {\n        return targetFontWeight;\n      }\n      return '';\n  }\n}\nclass Font {\n  constructor(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit) {\n    var inheritFont = inherit ? typeof inherit === 'string' ? Font.parse(inherit) : inherit : {};\n    this.fontFamily = fontFamily || inheritFont.fontFamily;\n    this.fontSize = fontSize || inheritFont.fontSize;\n    this.fontStyle = fontStyle || inheritFont.fontStyle;\n    this.fontWeight = fontWeight || inheritFont.fontWeight;\n    this.fontVariant = fontVariant || inheritFont.fontVariant;\n  }\n  static parse() {\n    var font = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var inherit = arguments.length > 1 ? arguments[1] : undefined;\n    var fontStyle = '';\n    var fontVariant = '';\n    var fontWeight = '';\n    var fontSize = '';\n    var fontFamily = '';\n    var parts = compressSpaces(font).trim().split(' ');\n    var set = {\n      fontSize: false,\n      fontStyle: false,\n      fontWeight: false,\n      fontVariant: false\n    };\n    parts.forEach(part => {\n      switch (true) {\n        case !set.fontStyle && Font.styles.includes(part):\n          if (part !== 'inherit') {\n            fontStyle = part;\n          }\n          set.fontStyle = true;\n          break;\n        case !set.fontVariant && Font.variants.includes(part):\n          if (part !== 'inherit') {\n            fontVariant = part;\n          }\n          set.fontStyle = true;\n          set.fontVariant = true;\n          break;\n        case !set.fontWeight && Font.weights.includes(part):\n          if (part !== 'inherit') {\n            fontWeight = part;\n          }\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          break;\n        case !set.fontSize:\n          if (part !== 'inherit') {\n            [fontSize] = part.split('/');\n          }\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          set.fontSize = true;\n          break;\n        default:\n          if (part !== 'inherit') {\n            fontFamily += part;\n          }\n      }\n    });\n    return new Font(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit);\n  }\n  toString() {\n    return [prepareFontStyle(this.fontStyle), this.fontVariant, prepareFontWeight(this.fontWeight), this.fontSize,\n    // Wrap fontFamily only on nodejs and only for canvas.ctx\n    prepareFontFamily(this.fontFamily)].join(' ').trim();\n  }\n}\nFont.styles = 'normal|italic|oblique|inherit';\nFont.variants = 'normal|small-caps|inherit';\nFont.weights = 'normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit';\nclass BoundingBox {\n  constructor() {\n    var x1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Number.NaN;\n    var y1 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.NaN;\n    var x2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.NaN;\n    var y2 = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Number.NaN;\n    this.x1 = x1;\n    this.y1 = y1;\n    this.x2 = x2;\n    this.y2 = y2;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n  get x() {\n    return this.x1;\n  }\n  get y() {\n    return this.y1;\n  }\n  get width() {\n    return this.x2 - this.x1;\n  }\n  get height() {\n    return this.y2 - this.y1;\n  }\n  addPoint(x, y) {\n    if (typeof x !== 'undefined') {\n      if (isNaN(this.x1) || isNaN(this.x2)) {\n        this.x1 = x;\n        this.x2 = x;\n      }\n      if (x < this.x1) {\n        this.x1 = x;\n      }\n      if (x > this.x2) {\n        this.x2 = x;\n      }\n    }\n    if (typeof y !== 'undefined') {\n      if (isNaN(this.y1) || isNaN(this.y2)) {\n        this.y1 = y;\n        this.y2 = y;\n      }\n      if (y < this.y1) {\n        this.y1 = y;\n      }\n      if (y > this.y2) {\n        this.y2 = y;\n      }\n    }\n  }\n  addX(x) {\n    this.addPoint(x, null);\n  }\n  addY(y) {\n    this.addPoint(null, y);\n  }\n  addBoundingBox(boundingBox) {\n    if (!boundingBox) {\n      return;\n    }\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = boundingBox;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n  sumCubic(t, p0, p1, p2, p3) {\n    return Math.pow(1 - t, 3) * p0 + 3 * Math.pow(1 - t, 2) * t * p1 + 3 * (1 - t) * Math.pow(t, 2) * p2 + Math.pow(t, 3) * p3;\n  }\n  bezierCurveAdd(forX, p0, p1, p2, p3) {\n    var b = 6 * p0 - 12 * p1 + 6 * p2;\n    var a = -3 * p0 + 9 * p1 - 9 * p2 + 3 * p3;\n    var c = 3 * p1 - 3 * p0;\n    if (a === 0) {\n      if (b === 0) {\n        return;\n      }\n      var t = -c / b;\n      if (0 < t && t < 1) {\n        if (forX) {\n          this.addX(this.sumCubic(t, p0, p1, p2, p3));\n        } else {\n          this.addY(this.sumCubic(t, p0, p1, p2, p3));\n        }\n      }\n      return;\n    }\n    var b2ac = Math.pow(b, 2) - 4 * c * a;\n    if (b2ac < 0) {\n      return;\n    }\n    var t1 = (-b + Math.sqrt(b2ac)) / (2 * a);\n    if (0 < t1 && t1 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t1, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t1, p0, p1, p2, p3));\n      }\n    }\n    var t2 = (-b - Math.sqrt(b2ac)) / (2 * a);\n    if (0 < t2 && t2 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t2, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t2, p0, p1, p2, p3));\n      }\n    }\n  } // from http://blog.hackers-cafe.net/2009/06/how-to-calculate-bezier-curves-bounding.html\n\n  addBezierCurve(p0x, p0y, p1x, p1y, p2x, p2y, p3x, p3y) {\n    this.addPoint(p0x, p0y);\n    this.addPoint(p3x, p3y);\n    this.bezierCurveAdd(true, p0x, p1x, p2x, p3x);\n    this.bezierCurveAdd(false, p0y, p1y, p2y, p3y);\n  }\n  addQuadraticCurve(p0x, p0y, p1x, p1y, p2x, p2y) {\n    var cp1x = p0x + 2 / 3 * (p1x - p0x); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp1y = p0y + 2 / 3 * (p1y - p0y); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp2x = cp1x + 1 / 3 * (p2x - p0x); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    var cp2y = cp1y + 1 / 3 * (p2y - p0y); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    this.addBezierCurve(p0x, p0y, cp1x, cp2x, cp1y, cp2y, p2x, p2y);\n  }\n  isPointInBox(x, y) {\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = this;\n    return x1 <= x && x <= x2 && y1 <= y && y <= y2;\n  }\n}\nclass PathParser extends SVGPathData {\n  constructor(path) {\n    super(path // Fix spaces after signs.\n    .replace(/([+\\-.])\\s+/gm, '$1') // Remove invalid part.\n    .replace(/[^MmZzLlHhVvCcSsQqTtAae\\d\\s.,+-].*/g, ''));\n    this.control = null;\n    this.start = null;\n    this.current = null;\n    this.command = null;\n    this.commands = this.commands;\n    this.i = -1;\n    this.previousCommand = null;\n    this.points = [];\n    this.angles = [];\n  }\n  reset() {\n    this.i = -1;\n    this.command = null;\n    this.previousCommand = null;\n    this.start = new Point(0, 0);\n    this.control = new Point(0, 0);\n    this.current = new Point(0, 0);\n    this.points = [];\n    this.angles = [];\n  }\n  isEnd() {\n    var {\n      i,\n      commands\n    } = this;\n    return i >= commands.length - 1;\n  }\n  next() {\n    var command = this.commands[++this.i];\n    this.previousCommand = this.command;\n    this.command = command;\n    return command;\n  }\n  getPoint() {\n    var xProp = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'x';\n    var yProp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'y';\n    var point = new Point(this.command[xProp], this.command[yProp]);\n    return this.makeAbsolute(point);\n  }\n  getAsControlPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.control = point;\n    return point;\n  }\n  getAsCurrentPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.current = point;\n    return point;\n  }\n  getReflectedControlPoint() {\n    var previousCommand = this.previousCommand.type;\n    if (previousCommand !== SVGPathData.CURVE_TO && previousCommand !== SVGPathData.SMOOTH_CURVE_TO && previousCommand !== SVGPathData.QUAD_TO && previousCommand !== SVGPathData.SMOOTH_QUAD_TO) {\n      return this.current;\n    } // reflect point\n\n    var {\n      current: {\n        x: cx,\n        y: cy\n      },\n      control: {\n        x: ox,\n        y: oy\n      }\n    } = this;\n    var point = new Point(2 * cx - ox, 2 * cy - oy);\n    return point;\n  }\n  makeAbsolute(point) {\n    if (this.command.relative) {\n      var {\n        x,\n        y\n      } = this.current;\n      point.x += x;\n      point.y += y;\n    }\n    return point;\n  }\n  addMarker(point, from, priorTo) {\n    var {\n      points,\n      angles\n    } = this; // if the last angle isn't filled in because we didn't have this point yet ...\n\n    if (priorTo && angles.length > 0 && !angles[angles.length - 1]) {\n      angles[angles.length - 1] = points[points.length - 1].angleTo(priorTo);\n    }\n    this.addMarkerAngle(point, from ? from.angleTo(point) : null);\n  }\n  addMarkerAngle(point, angle) {\n    this.points.push(point);\n    this.angles.push(angle);\n  }\n  getMarkerPoints() {\n    return this.points;\n  }\n  getMarkerAngles() {\n    var {\n      angles\n    } = this;\n    var len = angles.length;\n    for (var i = 0; i < len; i++) {\n      if (!angles[i]) {\n        for (var j = i + 1; j < len; j++) {\n          if (angles[j]) {\n            angles[i] = angles[j];\n            break;\n          }\n        }\n      }\n    }\n    return angles;\n  }\n}\nclass RenderedElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.modifiedEmSizeStack = false;\n  }\n  calculateOpacity() {\n    var opacity = 1.0; // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n\n    var element = this;\n    while (element) {\n      var opacityStyle = element.getStyle('opacity', false, true); // no ancestors on style call\n\n      if (opacityStyle.hasValue(true)) {\n        opacity *= opacityStyle.getNumber();\n      }\n      element = element.parent;\n    }\n    return opacity;\n  }\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (!fromMeasure) {\n      // causes stack overflow when measuring text with gradients\n      // fill\n      var fillStyleProp = this.getStyle('fill');\n      var fillOpacityStyleProp = this.getStyle('fill-opacity');\n      var strokeStyleProp = this.getStyle('stroke');\n      var strokeOpacityProp = this.getStyle('stroke-opacity');\n      if (fillStyleProp.isUrlDefinition()) {\n        var fillStyle = fillStyleProp.getFillStyleDefinition(this, fillOpacityStyleProp);\n        if (fillStyle) {\n          ctx.fillStyle = fillStyle;\n        }\n      } else if (fillStyleProp.hasValue()) {\n        if (fillStyleProp.getString() === 'currentColor') {\n          fillStyleProp.setValue(this.getStyle('color').getColor());\n        }\n        var _fillStyle = fillStyleProp.getColor();\n        if (_fillStyle !== 'inherit') {\n          ctx.fillStyle = _fillStyle === 'none' ? 'rgba(0,0,0,0)' : _fillStyle;\n        }\n      }\n      if (fillOpacityStyleProp.hasValue()) {\n        var _fillStyle2 = new Property(this.document, 'fill', ctx.fillStyle).addOpacity(fillOpacityStyleProp).getColor();\n        ctx.fillStyle = _fillStyle2;\n      } // stroke\n\n      if (strokeStyleProp.isUrlDefinition()) {\n        var strokeStyle = strokeStyleProp.getFillStyleDefinition(this, strokeOpacityProp);\n        if (strokeStyle) {\n          ctx.strokeStyle = strokeStyle;\n        }\n      } else if (strokeStyleProp.hasValue()) {\n        if (strokeStyleProp.getString() === 'currentColor') {\n          strokeStyleProp.setValue(this.getStyle('color').getColor());\n        }\n        var _strokeStyle = strokeStyleProp.getString();\n        if (_strokeStyle !== 'inherit') {\n          ctx.strokeStyle = _strokeStyle === 'none' ? 'rgba(0,0,0,0)' : _strokeStyle;\n        }\n      }\n      if (strokeOpacityProp.hasValue()) {\n        var _strokeStyle2 = new Property(this.document, 'stroke', ctx.strokeStyle).addOpacity(strokeOpacityProp).getString();\n        ctx.strokeStyle = _strokeStyle2;\n      }\n      var strokeWidthStyleProp = this.getStyle('stroke-width');\n      if (strokeWidthStyleProp.hasValue()) {\n        var newLineWidth = strokeWidthStyleProp.getPixels();\n        ctx.lineWidth = !newLineWidth ? PSEUDO_ZERO // browsers don't respect 0 (or node-canvas? :-)\n        : newLineWidth;\n      }\n      var strokeLinecapStyleProp = this.getStyle('stroke-linecap');\n      var strokeLinejoinStyleProp = this.getStyle('stroke-linejoin');\n      var strokeMiterlimitProp = this.getStyle('stroke-miterlimit'); // NEED TEST\n      // const pointOrderStyleProp = this.getStyle('paint-order');\n\n      var strokeDasharrayStyleProp = this.getStyle('stroke-dasharray');\n      var strokeDashoffsetProp = this.getStyle('stroke-dashoffset');\n      if (strokeLinecapStyleProp.hasValue()) {\n        ctx.lineCap = strokeLinecapStyleProp.getString();\n      }\n      if (strokeLinejoinStyleProp.hasValue()) {\n        ctx.lineJoin = strokeLinejoinStyleProp.getString();\n      }\n      if (strokeMiterlimitProp.hasValue()) {\n        ctx.miterLimit = strokeMiterlimitProp.getNumber();\n      } // NEED TEST\n      // if (pointOrderStyleProp.hasValue()) {\n      // \t// ?\n      // \tctx.paintOrder = pointOrderStyleProp.getValue();\n      // }\n\n      if (strokeDasharrayStyleProp.hasValue() && strokeDasharrayStyleProp.getString() !== 'none') {\n        var gaps = toNumbers(strokeDasharrayStyleProp.getString());\n        if (typeof ctx.setLineDash !== 'undefined') {\n          ctx.setLineDash(gaps);\n        } else\n          // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDash !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDash = gaps;\n          } else\n            // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDash !== 'undefined' && !(gaps.length === 1 && gaps[0] === 0)) {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDash = gaps;\n            }\n        var offset = strokeDashoffsetProp.getPixels();\n        if (typeof ctx.lineDashOffset !== 'undefined') {\n          ctx.lineDashOffset = offset;\n        } else\n          // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDashOffset !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDashOffset = offset;\n          } else\n            // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDashOffset !== 'undefined') {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDashOffset = offset;\n            }\n      }\n    } // font\n\n    this.modifiedEmSizeStack = false;\n    if (typeof ctx.font !== 'undefined') {\n      var fontStyleProp = this.getStyle('font');\n      var fontStyleStyleProp = this.getStyle('font-style');\n      var fontVariantStyleProp = this.getStyle('font-variant');\n      var fontWeightStyleProp = this.getStyle('font-weight');\n      var fontSizeStyleProp = this.getStyle('font-size');\n      var fontFamilyStyleProp = this.getStyle('font-family');\n      var font = new Font(fontStyleStyleProp.getString(), fontVariantStyleProp.getString(), fontWeightStyleProp.getString(), fontSizeStyleProp.hasValue() ? \"\".concat(fontSizeStyleProp.getPixels(true), \"px\") : '', fontFamilyStyleProp.getString(), Font.parse(fontStyleProp.getString(), ctx.font));\n      fontStyleStyleProp.setValue(font.fontStyle);\n      fontVariantStyleProp.setValue(font.fontVariant);\n      fontWeightStyleProp.setValue(font.fontWeight);\n      fontSizeStyleProp.setValue(font.fontSize);\n      fontFamilyStyleProp.setValue(font.fontFamily);\n      ctx.font = font.toString();\n      if (fontSizeStyleProp.isPixels()) {\n        this.document.emSize = fontSizeStyleProp.getPixels();\n        this.modifiedEmSizeStack = true;\n      }\n    }\n    if (!fromMeasure) {\n      // effects\n      this.applyEffects(ctx); // opacity\n\n      ctx.globalAlpha = this.calculateOpacity();\n    }\n  }\n  clearContext(ctx) {\n    super.clearContext(ctx);\n    if (this.modifiedEmSizeStack) {\n      this.document.popEmSize();\n    }\n  }\n}\nclass PathElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'path';\n    this.pathParser = null;\n    this.pathParser = new PathParser(this.getAttribute('d').getString());\n  }\n  path(ctx) {\n    var {\n      pathParser\n    } = this;\n    var boundingBox = new BoundingBox();\n    pathParser.reset();\n    if (ctx) {\n      ctx.beginPath();\n    }\n    while (!pathParser.isEnd()) {\n      switch (pathParser.next().type) {\n        case PathParser.MOVE_TO:\n          this.pathM(ctx, boundingBox);\n          break;\n        case PathParser.LINE_TO:\n          this.pathL(ctx, boundingBox);\n          break;\n        case PathParser.HORIZ_LINE_TO:\n          this.pathH(ctx, boundingBox);\n          break;\n        case PathParser.VERT_LINE_TO:\n          this.pathV(ctx, boundingBox);\n          break;\n        case PathParser.CURVE_TO:\n          this.pathC(ctx, boundingBox);\n          break;\n        case PathParser.SMOOTH_CURVE_TO:\n          this.pathS(ctx, boundingBox);\n          break;\n        case PathParser.QUAD_TO:\n          this.pathQ(ctx, boundingBox);\n          break;\n        case PathParser.SMOOTH_QUAD_TO:\n          this.pathT(ctx, boundingBox);\n          break;\n        case PathParser.ARC:\n          this.pathA(ctx, boundingBox);\n          break;\n        case PathParser.CLOSE_PATH:\n          this.pathZ(ctx, boundingBox);\n          break;\n      }\n    }\n    return boundingBox;\n  }\n  getBoundingBox(_) {\n    return this.path();\n  }\n  getMarkers() {\n    var {\n      pathParser\n    } = this;\n    var points = pathParser.getMarkerPoints();\n    var angles = pathParser.getMarkerAngles();\n    var markers = points.map((point, i) => [point, angles[i]]);\n    return markers;\n  }\n  renderChildren(ctx) {\n    this.path(ctx);\n    this.document.screen.mouse.checkPath(this, ctx);\n    var fillRuleStyleProp = this.getStyle('fill-rule');\n    if (ctx.fillStyle !== '') {\n      if (fillRuleStyleProp.getString('inherit') !== 'inherit') {\n        ctx.fill(fillRuleStyleProp.getString());\n      } else {\n        ctx.fill();\n      }\n    }\n    if (ctx.strokeStyle !== '') {\n      if (this.getAttribute('vector-effect').getString() === 'non-scaling-stroke') {\n        ctx.save();\n        ctx.setTransform(1, 0, 0, 1, 0, 0);\n        ctx.stroke();\n        ctx.restore();\n      } else {\n        ctx.stroke();\n      }\n    }\n    var markers = this.getMarkers();\n    if (markers) {\n      var markersLastIndex = markers.length - 1;\n      var markerStartStyleProp = this.getStyle('marker-start');\n      var markerMidStyleProp = this.getStyle('marker-mid');\n      var markerEndStyleProp = this.getStyle('marker-end');\n      if (markerStartStyleProp.isUrlDefinition()) {\n        var marker = markerStartStyleProp.getDefinition();\n        var [point, angle] = markers[0];\n        marker.render(ctx, point, angle);\n      }\n      if (markerMidStyleProp.isUrlDefinition()) {\n        var _marker = markerMidStyleProp.getDefinition();\n        for (var i = 1; i < markersLastIndex; i++) {\n          var [_point, _angle] = markers[i];\n          _marker.render(ctx, _point, _angle);\n        }\n      }\n      if (markerEndStyleProp.isUrlDefinition()) {\n        var _marker2 = markerEndStyleProp.getDefinition();\n        var [_point2, _angle2] = markers[markersLastIndex];\n        _marker2.render(ctx, _point2, _angle2);\n      }\n    }\n  }\n  static pathM(pathParser) {\n    var point = pathParser.getAsCurrentPoint();\n    pathParser.start = pathParser.current;\n    return {\n      point\n    };\n  }\n  pathM(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      point\n    } = PathElement.pathM(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point);\n    boundingBox.addPoint(x, y);\n    if (ctx) {\n      ctx.moveTo(x, y);\n    }\n  }\n  static pathL(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point\n    };\n  }\n  pathL(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathL(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n  static pathH(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point((command.relative ? current.x : 0) + command.x, current.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n  pathH(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathH(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n  static pathV(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point(current.x, (command.relative ? current.y : 0) + command.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n  pathV(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathV(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n  static pathC(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getPoint('x1', 'y1');\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n  pathC(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n  static pathS(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getReflectedControlPoint();\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n  pathS(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n  static pathQ(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getAsControlPoint('x1', 'y1');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n  pathQ(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n  static pathT(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getReflectedControlPoint();\n    pathParser.control = controlPoint;\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n  pathT(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n  static pathA(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var {\n      rX,\n      rY,\n      xRot,\n      lArcFlag,\n      sweepFlag\n    } = command;\n    var xAxisRotation = xRot * (Math.PI / 180.0);\n    var currentPoint = pathParser.getAsCurrentPoint(); // Conversion from endpoint to center parameterization\n    // http://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes\n    // x1', y1'\n\n    var currp = new Point(Math.cos(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.sin(xAxisRotation) * (current.y - currentPoint.y) / 2.0, -Math.sin(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * (current.y - currentPoint.y) / 2.0); // adjust radii\n\n    var l = Math.pow(currp.x, 2) / Math.pow(rX, 2) + Math.pow(currp.y, 2) / Math.pow(rY, 2);\n    if (l > 1) {\n      rX *= Math.sqrt(l);\n      rY *= Math.sqrt(l);\n    } // cx', cy'\n\n    var s = (lArcFlag === sweepFlag ? -1 : 1) * Math.sqrt((Math.pow(rX, 2) * Math.pow(rY, 2) - Math.pow(rX, 2) * Math.pow(currp.y, 2) - Math.pow(rY, 2) * Math.pow(currp.x, 2)) / (Math.pow(rX, 2) * Math.pow(currp.y, 2) + Math.pow(rY, 2) * Math.pow(currp.x, 2)));\n    if (isNaN(s)) {\n      s = 0;\n    }\n    var cpp = new Point(s * rX * currp.y / rY, s * -rY * currp.x / rX); // cx, cy\n\n    var centp = new Point((current.x + currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * cpp.x - Math.sin(xAxisRotation) * cpp.y, (current.y + currentPoint.y) / 2.0 + Math.sin(xAxisRotation) * cpp.x + Math.cos(xAxisRotation) * cpp.y); // initial angle\n\n    var a1 = vectorsAngle([1, 0], [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY]); // θ1\n    // angle delta\n\n    var u = [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY];\n    var v = [(-currp.x - cpp.x) / rX, (-currp.y - cpp.y) / rY];\n    var ad = vectorsAngle(u, v); // Δθ\n\n    if (vectorsRatio(u, v) <= -1) {\n      ad = Math.PI;\n    }\n    if (vectorsRatio(u, v) >= 1) {\n      ad = 0;\n    }\n    return {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    };\n  }\n  pathA(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser); // for markers\n\n    var dir = 1 - sweepFlag ? 1.0 : -1.0;\n    var ah = a1 + dir * (ad / 2.0);\n    var halfWay = new Point(centp.x + rX * Math.cos(ah), centp.y + rY * Math.sin(ah));\n    pathParser.addMarkerAngle(halfWay, ah - dir * Math.PI / 2);\n    pathParser.addMarkerAngle(currentPoint, ah - dir * Math.PI);\n    boundingBox.addPoint(currentPoint.x, currentPoint.y); // TODO: this is too naive, make it better\n\n    if (ctx && !isNaN(a1) && !isNaN(ad)) {\n      var r = rX > rY ? rX : rY;\n      var sx = rX > rY ? 1 : rX / rY;\n      var sy = rX > rY ? rY / rX : 1;\n      ctx.translate(centp.x, centp.y);\n      ctx.rotate(xAxisRotation);\n      ctx.scale(sx, sy);\n      ctx.arc(0, 0, r, a1, a1 + ad, Boolean(1 - sweepFlag));\n      ctx.scale(1 / sx, 1 / sy);\n      ctx.rotate(-xAxisRotation);\n      ctx.translate(-centp.x, -centp.y);\n    }\n  }\n  static pathZ(pathParser) {\n    pathParser.current = pathParser.start;\n  }\n  pathZ(ctx, boundingBox) {\n    PathElement.pathZ(this.pathParser);\n    if (ctx) {\n      // only close path if it is not a straight line\n      if (boundingBox.x1 !== boundingBox.x2 && boundingBox.y1 !== boundingBox.y2) {\n        ctx.closePath();\n      }\n    }\n  }\n}\nclass GlyphElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'glyph';\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    this.unicode = this.getAttribute('unicode').getString();\n    this.arabicForm = this.getAttribute('arabic-form').getString();\n  }\n}\nclass TextElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TextElement ? true : captureTextNodes);\n    this.type = 'text';\n    this.x = 0;\n    this.y = 0;\n    this.measureCache = -1;\n  }\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.setContext(ctx, fromMeasure);\n    var textBaseline = this.getStyle('dominant-baseline').getTextBaseline() || this.getStyle('alignment-baseline').getTextBaseline();\n    if (textBaseline) {\n      ctx.textBaseline = textBaseline;\n    }\n  }\n  initializeCoordinates() {\n    this.x = 0;\n    this.y = 0;\n    this.leafTexts = [];\n    this.textChunkStart = 0;\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n  }\n  getBoundingBox(ctx) {\n    if (this.type !== 'text') {\n      return this.getTElementBoundingBox(ctx);\n    } // first, calculate child positions\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx);\n    var boundingBox = null; // then calculate bounding box\n\n    this.children.forEach((_, i) => {\n      var childBoundingBox = this.getChildBoundingBox(ctx, this, this, i);\n      if (!boundingBox) {\n        boundingBox = childBoundingBox;\n      } else {\n        boundingBox.addBoundingBox(childBoundingBox);\n      }\n    });\n    return boundingBox;\n  }\n  getFontSize() {\n    var {\n      document,\n      parent\n    } = this;\n    var inheritFontSize = Font.parse(document.ctx.font).fontSize;\n    var fontSize = parent.getStyle('font-size').getNumber(inheritFontSize);\n    return fontSize;\n  }\n  getTElementBoundingBox(ctx) {\n    var fontSize = this.getFontSize();\n    return new BoundingBox(this.x, this.y - fontSize, this.x + this.measureText(ctx), this.y);\n  }\n  getGlyph(font, text, i) {\n    var char = text[i];\n    var glyph = null;\n    if (font.isArabic) {\n      var len = text.length;\n      var prevChar = text[i - 1];\n      var nextChar = text[i + 1];\n      var arabicForm = 'isolated';\n      if ((i === 0 || prevChar === ' ') && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'terminal';\n      }\n      if (i > 0 && prevChar !== ' ' && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'medial';\n      }\n      if (i > 0 && prevChar !== ' ' && (i === len - 1 || nextChar === ' ')) {\n        arabicForm = 'initial';\n      }\n      if (typeof font.glyphs[char] !== 'undefined') {\n        // NEED TEST\n        var maybeGlyph = font.glyphs[char];\n        glyph = maybeGlyph instanceof GlyphElement ? maybeGlyph : maybeGlyph[arabicForm];\n      }\n    } else {\n      glyph = font.glyphs[char];\n    }\n    if (!glyph) {\n      glyph = font.missingGlyph;\n    }\n    return glyph;\n  }\n  getText() {\n    return '';\n  }\n  getTextFromNode(node) {\n    var textNode = node || this.node;\n    var childNodes = Array.from(textNode.parentNode.childNodes);\n    var index = childNodes.indexOf(textNode);\n    var lastIndex = childNodes.length - 1;\n    var text = compressSpaces(\n    // textNode.value\n    // || textNode.text\n    textNode.textContent || '');\n    if (index === 0) {\n      text = trimLeft(text);\n    }\n    if (index === lastIndex) {\n      text = trimRight(text);\n    }\n    return text;\n  }\n  renderChildren(ctx) {\n    if (this.type !== 'text') {\n      this.renderTElementChildren(ctx);\n      return;\n    } // first, calculate child positions\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx); // then render\n\n    this.children.forEach((_, i) => {\n      this.renderChild(ctx, this, this, i);\n    });\n    var {\n      mouse\n    } = this.document.screen; // Do not calc bounding box if mouse is not working.\n\n    if (mouse.isWorking()) {\n      mouse.checkBoundingBox(this, this.getBoundingBox(ctx));\n    }\n  }\n  renderTElementChildren(ctx) {\n    var {\n      document,\n      parent\n    } = this;\n    var renderText = this.getText();\n    var customFont = parent.getStyle('font-family').getDefinition();\n    if (customFont) {\n      var {\n        unitsPerEm\n      } = customFont.fontFace;\n      var ctxFont = Font.parse(document.ctx.font);\n      var fontSize = parent.getStyle('font-size').getNumber(ctxFont.fontSize);\n      var fontStyle = parent.getStyle('font-style').getString(ctxFont.fontStyle);\n      var scale = fontSize / unitsPerEm;\n      var text = customFont.isRTL ? renderText.split('').reverse().join('') : renderText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        ctx.translate(this.x, this.y);\n        ctx.scale(scale, -scale);\n        var lw = ctx.lineWidth;\n        ctx.lineWidth = ctx.lineWidth * unitsPerEm / fontSize;\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, .4, 1, 0, 0);\n        }\n        glyph.render(ctx);\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, -.4, 1, 0, 0);\n        }\n        ctx.lineWidth = lw;\n        ctx.scale(1 / scale, -1 / scale);\n        ctx.translate(-this.x, -this.y);\n        this.x += fontSize * (glyph.horizAdvX || customFont.horizAdvX) / unitsPerEm;\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          this.x += dx[i];\n        }\n      }\n      return;\n    }\n    var {\n      x,\n      y\n    } = this; // NEED TEST\n    // if (ctx.paintOrder === 'stroke') {\n    // \tif (ctx.strokeStyle) {\n    // \t\tctx.strokeText(renderText, x, y);\n    // \t}\n    // \tif (ctx.fillStyle) {\n    // \t\tctx.fillText(renderText, x, y);\n    // \t}\n    // } else {\n\n    if (ctx.fillStyle) {\n      ctx.fillText(renderText, x, y);\n    }\n    if (ctx.strokeStyle) {\n      ctx.strokeText(renderText, x, y);\n    } // }\n  }\n  applyAnchoring() {\n    if (this.textChunkStart >= this.leafTexts.length) {\n      return;\n    } // This is basically the \"Apply anchoring\" part of https://www.w3.org/TR/SVG2/text.html#TextLayoutAlgorithm.\n    // The difference is that we apply the anchoring as soon as a chunk is finished. This saves some extra looping.\n    // Vertical text is not supported.\n\n    var firstElement = this.leafTexts[this.textChunkStart];\n    var textAnchor = firstElement.getStyle('text-anchor').getString('start');\n    var isRTL = false; // we treat RTL like LTR\n\n    var shift = 0;\n    if (textAnchor === 'start' && !isRTL || textAnchor === 'end' && isRTL) {\n      shift = firstElement.x - this.minX;\n    } else if (textAnchor === 'end' && !isRTL || textAnchor === 'start' && isRTL) {\n      shift = firstElement.x - this.maxX;\n    } else {\n      shift = firstElement.x - (this.minX + this.maxX) / 2;\n    }\n    for (var i = this.textChunkStart; i < this.leafTexts.length; i++) {\n      this.leafTexts[i].x += shift;\n    } // start new chunk\n\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n    this.textChunkStart = this.leafTexts.length;\n  }\n  adjustChildCoordinatesRecursive(ctx) {\n    this.children.forEach((_, i) => {\n      this.adjustChildCoordinatesRecursiveCore(ctx, this, this, i);\n    });\n    this.applyAnchoring();\n  }\n  adjustChildCoordinatesRecursiveCore(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n    if (child.children.length > 0) {\n      child.children.forEach((_, i) => {\n        textParent.adjustChildCoordinatesRecursiveCore(ctx, textParent, child, i);\n      });\n    } else {\n      // only leafs are relevant\n      this.adjustChildCoordinates(ctx, textParent, parent, i);\n    }\n  }\n  adjustChildCoordinates(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n    if (typeof child.measureText !== 'function') {\n      return child;\n    }\n    ctx.save();\n    child.setContext(ctx, true);\n    var xAttr = child.getAttribute('x');\n    var yAttr = child.getAttribute('y');\n    var dxAttr = child.getAttribute('dx');\n    var dyAttr = child.getAttribute('dy');\n    var customFont = child.getStyle('font-family').getDefinition();\n    var isRTL = Boolean(customFont) && customFont.isRTL;\n    if (i === 0) {\n      // First children inherit attributes from parent(s). Positional attributes\n      // are only inherited from a parent to it's first child.\n      if (!xAttr.hasValue()) {\n        xAttr.setValue(child.getInheritedAttribute('x'));\n      }\n      if (!yAttr.hasValue()) {\n        yAttr.setValue(child.getInheritedAttribute('y'));\n      }\n      if (!dxAttr.hasValue()) {\n        dxAttr.setValue(child.getInheritedAttribute('dx'));\n      }\n      if (!dyAttr.hasValue()) {\n        dyAttr.setValue(child.getInheritedAttribute('dy'));\n      }\n    }\n    var width = child.measureText(ctx);\n    if (isRTL) {\n      textParent.x -= width;\n    }\n    if (xAttr.hasValue()) {\n      // an \"x\" attribute marks the start of a new chunk\n      textParent.applyAnchoring();\n      child.x = xAttr.getPixels('x');\n      if (dxAttr.hasValue()) {\n        child.x += dxAttr.getPixels('x');\n      }\n    } else {\n      if (dxAttr.hasValue()) {\n        textParent.x += dxAttr.getPixels('x');\n      }\n      child.x = textParent.x;\n    }\n    textParent.x = child.x;\n    if (!isRTL) {\n      textParent.x += width;\n    }\n    if (yAttr.hasValue()) {\n      child.y = yAttr.getPixels('y');\n      if (dyAttr.hasValue()) {\n        child.y += dyAttr.getPixels('y');\n      }\n    } else {\n      if (dyAttr.hasValue()) {\n        textParent.y += dyAttr.getPixels('y');\n      }\n      child.y = textParent.y;\n    }\n    textParent.y = child.y; // update the current chunk and it's bounds\n\n    textParent.leafTexts.push(child);\n    textParent.minX = Math.min(textParent.minX, child.x, child.x + width);\n    textParent.maxX = Math.max(textParent.maxX, child.x, child.x + width);\n    child.clearContext(ctx);\n    ctx.restore();\n    return child;\n  }\n  getChildBoundingBox(ctx, textParent, parent, i) {\n    var child = parent.children[i]; // not a text node?\n\n    if (typeof child.getBoundingBox !== 'function') {\n      return null;\n    }\n    var boundingBox = child.getBoundingBox(ctx);\n    if (!boundingBox) {\n      return null;\n    }\n    child.children.forEach((_, i) => {\n      var childBoundingBox = textParent.getChildBoundingBox(ctx, textParent, child, i);\n      boundingBox.addBoundingBox(childBoundingBox);\n    });\n    return boundingBox;\n  }\n  renderChild(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n    child.render(ctx);\n    child.children.forEach((_, i) => {\n      textParent.renderChild(ctx, textParent, child, i);\n    });\n  }\n  measureText(ctx) {\n    var {\n      measureCache\n    } = this;\n    if (~measureCache) {\n      return measureCache;\n    }\n    var renderText = this.getText();\n    var measure = this.measureTargetText(ctx, renderText);\n    this.measureCache = measure;\n    return measure;\n  }\n  measureTargetText(ctx, targetText) {\n    if (!targetText.length) {\n      return 0;\n    }\n    var {\n      parent\n    } = this;\n    var customFont = parent.getStyle('font-family').getDefinition();\n    if (customFont) {\n      var fontSize = this.getFontSize();\n      var text = customFont.isRTL ? targetText.split('').reverse().join('') : targetText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n      var _measure = 0;\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        _measure += (glyph.horizAdvX || customFont.horizAdvX) * fontSize / customFont.fontFace.unitsPerEm;\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          _measure += dx[i];\n        }\n      }\n      return _measure;\n    }\n    if (!ctx.measureText) {\n      return targetText.length * 10;\n    }\n    ctx.save();\n    this.setContext(ctx, true);\n    var {\n      width: measure\n    } = ctx.measureText(targetText);\n    this.clearContext(ctx);\n    ctx.restore();\n    return measure;\n  }\n  /**\r\n   * Inherits positional attributes from {@link TextElement} parent(s). Attributes\r\n   * are only inherited from a parent to its first child.\r\n   * @param name - The attribute name.\r\n   * @returns The attribute value or null.\r\n   */\n\n  getInheritedAttribute(name) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias,consistent-this\n    var current = this;\n    while (current instanceof TextElement && current.isFirstChild()) {\n      var parentAttr = current.parent.getAttribute(name);\n      if (parentAttr.hasValue(true)) {\n        return parentAttr.getValue('0');\n      }\n      current = current.parent;\n    }\n    return null;\n  }\n}\nclass TSpanElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TSpanElement ? true : captureTextNodes);\n    this.type = 'tspan'; // if this node has children, then they own the text\n\n    this.text = this.children.length > 0 ? '' : this.getTextFromNode();\n  }\n  getText() {\n    return this.text;\n  }\n}\nclass TextNode extends TSpanElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'textNode';\n  }\n}\nclass SVGElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'svg';\n    this.root = false;\n  }\n  setContext(ctx) {\n    var _this$node$parentNode;\n    var {\n      document\n    } = this;\n    var {\n      screen,\n      window\n    } = document;\n    var canvas = ctx.canvas;\n    screen.setDefaults(ctx);\n    if (canvas.style && typeof ctx.font !== 'undefined' && window && typeof window.getComputedStyle !== 'undefined') {\n      ctx.font = window.getComputedStyle(canvas).getPropertyValue('font');\n      var fontSizeProp = new Property(document, 'fontSize', Font.parse(ctx.font).fontSize);\n      if (fontSizeProp.hasValue()) {\n        document.rootEmSize = fontSizeProp.getPixels('y');\n        document.emSize = document.rootEmSize;\n      }\n    } // create new view port\n\n    if (!this.getAttribute('x').hasValue()) {\n      this.getAttribute('x', true).setValue(0);\n    }\n    if (!this.getAttribute('y').hasValue()) {\n      this.getAttribute('y', true).setValue(0);\n    }\n    var {\n      width,\n      height\n    } = screen.viewPort;\n    if (!this.getStyle('width').hasValue()) {\n      this.getStyle('width', true).setValue('100%');\n    }\n    if (!this.getStyle('height').hasValue()) {\n      this.getStyle('height', true).setValue('100%');\n    }\n    if (!this.getStyle('color').hasValue()) {\n      this.getStyle('color', true).setValue('black');\n    }\n    var refXAttr = this.getAttribute('refX');\n    var refYAttr = this.getAttribute('refY');\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var viewBox = viewBoxAttr.hasValue() ? toNumbers(viewBoxAttr.getString()) : null;\n    var clip = !this.root && this.getStyle('overflow').getValue('hidden') !== 'visible';\n    var minX = 0;\n    var minY = 0;\n    var clipX = 0;\n    var clipY = 0;\n    if (viewBox) {\n      minX = viewBox[0];\n      minY = viewBox[1];\n    }\n    if (!this.root) {\n      width = this.getStyle('width').getPixels('x');\n      height = this.getStyle('height').getPixels('y');\n      if (this.type === 'marker') {\n        clipX = minX;\n        clipY = minY;\n        minX = 0;\n        minY = 0;\n      }\n    }\n    screen.viewPort.setCurrent(width, height); // Default value of transform-origin is center only for root SVG elements\n    // https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/transform-origin\n\n    if (this.node // is not temporary SVGElement\n    && (!this.parent || ((_this$node$parentNode = this.node.parentNode) === null || _this$node$parentNode === void 0 ? void 0 : _this$node$parentNode.nodeName) === 'foreignObject') && this.getStyle('transform', false, true).hasValue() && !this.getStyle('transform-origin', false, true).hasValue()) {\n      this.getStyle('transform-origin', true, true).setValue('50% 50%');\n    }\n    super.setContext(ctx);\n    ctx.translate(this.getAttribute('x').getPixels('x'), this.getAttribute('y').getPixels('y'));\n    if (viewBox) {\n      width = viewBox[2];\n      height = viewBox[3];\n    }\n    document.setViewBox({\n      ctx,\n      aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n      width: screen.viewPort.width,\n      desiredWidth: width,\n      height: screen.viewPort.height,\n      desiredHeight: height,\n      minX,\n      minY,\n      refX: refXAttr.getValue(),\n      refY: refYAttr.getValue(),\n      clip,\n      clipX,\n      clipY\n    });\n    if (viewBox) {\n      screen.viewPort.removeCurrent();\n      screen.viewPort.setCurrent(width, height);\n    }\n  }\n  clearContext(ctx) {\n    super.clearContext(ctx);\n    this.document.screen.viewPort.removeCurrent();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var widthAttr = this.getAttribute('width', true);\n    var heightAttr = this.getAttribute('height', true);\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var styleAttr = this.getAttribute('style');\n    var originWidth = widthAttr.getNumber(0);\n    var originHeight = heightAttr.getNumber(0);\n    if (preserveAspectRatio) {\n      if (typeof preserveAspectRatio === 'string') {\n        this.getAttribute('preserveAspectRatio', true).setValue(preserveAspectRatio);\n      } else {\n        var preserveAspectRatioAttr = this.getAttribute('preserveAspectRatio');\n        if (preserveAspectRatioAttr.hasValue()) {\n          preserveAspectRatioAttr.setValue(preserveAspectRatioAttr.getString().replace(/^\\s*(\\S.*\\S)\\s*$/, '$1'));\n        }\n      }\n    }\n    widthAttr.setValue(width);\n    heightAttr.setValue(height);\n    if (!viewBoxAttr.hasValue()) {\n      viewBoxAttr.setValue(\"0 0 \".concat(originWidth || width, \" \").concat(originHeight || height));\n    }\n    if (styleAttr.hasValue()) {\n      var widthStyle = this.getStyle('width');\n      var heightStyle = this.getStyle('height');\n      if (widthStyle.hasValue()) {\n        widthStyle.setValue(\"\".concat(width, \"px\"));\n      }\n      if (heightStyle.hasValue()) {\n        heightStyle.setValue(\"\".concat(height, \"px\"));\n      }\n    }\n  }\n}\nclass RectElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'rect';\n  }\n  path(ctx) {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width', false, true).getPixels('x');\n    var height = this.getStyle('height', false, true).getPixels('y');\n    var rxAttr = this.getAttribute('rx');\n    var ryAttr = this.getAttribute('ry');\n    var rx = rxAttr.getPixels('x');\n    var ry = ryAttr.getPixels('y');\n    if (rxAttr.hasValue() && !ryAttr.hasValue()) {\n      ry = rx;\n    }\n    if (ryAttr.hasValue() && !rxAttr.hasValue()) {\n      rx = ry;\n    }\n    rx = Math.min(rx, width / 2.0);\n    ry = Math.min(ry, height / 2.0);\n    if (ctx) {\n      var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n      ctx.beginPath(); // always start the path so we don't fill prior paths\n\n      if (height > 0 && width > 0) {\n        ctx.moveTo(x + rx, y);\n        ctx.lineTo(x + width - rx, y);\n        ctx.bezierCurveTo(x + width - rx + KAPPA * rx, y, x + width, y + ry - KAPPA * ry, x + width, y + ry);\n        ctx.lineTo(x + width, y + height - ry);\n        ctx.bezierCurveTo(x + width, y + height - ry + KAPPA * ry, x + width - rx + KAPPA * rx, y + height, x + width - rx, y + height);\n        ctx.lineTo(x + rx, y + height);\n        ctx.bezierCurveTo(x + rx - KAPPA * rx, y + height, x, y + height - ry + KAPPA * ry, x, y + height - ry);\n        ctx.lineTo(x, y + ry);\n        ctx.bezierCurveTo(x, y + ry - KAPPA * ry, x + rx - KAPPA * rx, y, x + rx, y);\n        ctx.closePath();\n      }\n    }\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n  getMarkers() {\n    return null;\n  }\n}\nclass CircleElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'circle';\n  }\n  path(ctx) {\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n    var r = this.getAttribute('r').getPixels();\n    if (ctx && r > 0) {\n      ctx.beginPath();\n      ctx.arc(cx, cy, r, 0, Math.PI * 2, false);\n      ctx.closePath();\n    }\n    return new BoundingBox(cx - r, cy - r, cx + r, cy + r);\n  }\n  getMarkers() {\n    return null;\n  }\n}\nclass EllipseElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'ellipse';\n  }\n  path(ctx) {\n    var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n    var rx = this.getAttribute('rx').getPixels('x');\n    var ry = this.getAttribute('ry').getPixels('y');\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n    if (ctx && rx > 0 && ry > 0) {\n      ctx.beginPath();\n      ctx.moveTo(cx + rx, cy);\n      ctx.bezierCurveTo(cx + rx, cy + KAPPA * ry, cx + KAPPA * rx, cy + ry, cx, cy + ry);\n      ctx.bezierCurveTo(cx - KAPPA * rx, cy + ry, cx - rx, cy + KAPPA * ry, cx - rx, cy);\n      ctx.bezierCurveTo(cx - rx, cy - KAPPA * ry, cx - KAPPA * rx, cy - ry, cx, cy - ry);\n      ctx.bezierCurveTo(cx + KAPPA * rx, cy - ry, cx + rx, cy - KAPPA * ry, cx + rx, cy);\n      ctx.closePath();\n    }\n    return new BoundingBox(cx - rx, cy - ry, cx + rx, cy + ry);\n  }\n  getMarkers() {\n    return null;\n  }\n}\nclass LineElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'line';\n  }\n  getPoints() {\n    return [new Point(this.getAttribute('x1').getPixels('x'), this.getAttribute('y1').getPixels('y')), new Point(this.getAttribute('x2').getPixels('x'), this.getAttribute('y2').getPixels('y'))];\n  }\n  path(ctx) {\n    var [{\n      x: x0,\n      y: y0\n    }, {\n      x: x1,\n      y: y1\n    }] = this.getPoints();\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n      ctx.lineTo(x1, y1);\n    }\n    return new BoundingBox(x0, y0, x1, y1);\n  }\n  getMarkers() {\n    var [p0, p1] = this.getPoints();\n    var a = p0.angleTo(p1);\n    return [[p0, a], [p1, a]];\n  }\n}\nclass PolylineElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'polyline';\n    this.points = [];\n    this.points = Point.parsePath(this.getAttribute('points').getString());\n  }\n  path(ctx) {\n    var {\n      points\n    } = this;\n    var [{\n      x: x0,\n      y: y0\n    }] = points;\n    var boundingBox = new BoundingBox(x0, y0);\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n    }\n    points.forEach(_ref => {\n      var {\n        x,\n        y\n      } = _ref;\n      boundingBox.addPoint(x, y);\n      if (ctx) {\n        ctx.lineTo(x, y);\n      }\n    });\n    return boundingBox;\n  }\n  getMarkers() {\n    var {\n      points\n    } = this;\n    var lastIndex = points.length - 1;\n    var markers = [];\n    points.forEach((point, i) => {\n      if (i === lastIndex) {\n        return;\n      }\n      markers.push([point, point.angleTo(points[i + 1])]);\n    });\n    if (markers.length > 0) {\n      markers.push([points[points.length - 1], markers[markers.length - 1][1]]);\n    }\n    return markers;\n  }\n}\nclass PolygonElement extends PolylineElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'polygon';\n  }\n  path(ctx) {\n    var boundingBox = super.path(ctx);\n    var [{\n      x,\n      y\n    }] = this.points;\n    if (ctx) {\n      ctx.lineTo(x, y);\n      ctx.closePath();\n    }\n    return boundingBox;\n  }\n}\nclass PatternElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'pattern';\n  }\n  createPattern(ctx, _, parentOpacityProp) {\n    var width = this.getStyle('width').getPixels('x', true);\n    var height = this.getStyle('height').getPixels('y', true); // render me using a temporary svg element\n\n    var patternSvg = new SVGElement(this.document, null);\n    patternSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    patternSvg.attributes.width = new Property(this.document, 'width', \"\".concat(width, \"px\"));\n    patternSvg.attributes.height = new Property(this.document, 'height', \"\".concat(height, \"px\"));\n    patternSvg.attributes.transform = new Property(this.document, 'transform', this.getAttribute('patternTransform').getValue());\n    patternSvg.children = this.children;\n    var patternCanvas = this.document.createCanvas(width, height);\n    var patternCtx = patternCanvas.getContext('2d');\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n    if (xAttr.hasValue() && yAttr.hasValue()) {\n      patternCtx.translate(xAttr.getPixels('x', true), yAttr.getPixels('y', true));\n    }\n    if (parentOpacityProp.hasValue()) {\n      this.styles['fill-opacity'] = parentOpacityProp;\n    } else {\n      Reflect.deleteProperty(this.styles, 'fill-opacity');\n    } // render 3x3 grid so when we transform there's no white space on edges\n\n    for (var x = -1; x <= 1; x++) {\n      for (var y = -1; y <= 1; y++) {\n        patternCtx.save();\n        patternSvg.attributes.x = new Property(this.document, 'x', x * patternCanvas.width);\n        patternSvg.attributes.y = new Property(this.document, 'y', y * patternCanvas.height);\n        patternSvg.render(patternCtx);\n        patternCtx.restore();\n      }\n    }\n    var pattern = ctx.createPattern(patternCanvas, 'repeat');\n    return pattern;\n  }\n}\nclass MarkerElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'marker';\n  }\n  render(ctx, point, angle) {\n    if (!point) {\n      return;\n    }\n    var {\n      x,\n      y\n    } = point;\n    var orient = this.getAttribute('orient').getString('auto');\n    var markerUnits = this.getAttribute('markerUnits').getString('strokeWidth');\n    ctx.translate(x, y);\n    if (orient === 'auto') {\n      ctx.rotate(angle);\n    }\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(ctx.lineWidth, ctx.lineWidth);\n    }\n    ctx.save(); // render me using a temporary svg element\n\n    var markerSvg = new SVGElement(this.document, null);\n    markerSvg.type = this.type;\n    markerSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    markerSvg.attributes.refX = new Property(this.document, 'refX', this.getAttribute('refX').getValue());\n    markerSvg.attributes.refY = new Property(this.document, 'refY', this.getAttribute('refY').getValue());\n    markerSvg.attributes.width = new Property(this.document, 'width', this.getAttribute('markerWidth').getValue());\n    markerSvg.attributes.height = new Property(this.document, 'height', this.getAttribute('markerHeight').getValue());\n    markerSvg.attributes.overflow = new Property(this.document, 'overflow', this.getAttribute('overflow').getValue());\n    markerSvg.attributes.fill = new Property(this.document, 'fill', this.getAttribute('fill').getColor('black'));\n    markerSvg.attributes.stroke = new Property(this.document, 'stroke', this.getAttribute('stroke').getValue('none'));\n    markerSvg.children = this.children;\n    markerSvg.render(ctx);\n    ctx.restore();\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(1 / ctx.lineWidth, 1 / ctx.lineWidth);\n    }\n    if (orient === 'auto') {\n      ctx.rotate(-angle);\n    }\n    ctx.translate(-x, -y);\n  }\n}\nclass DefsElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'defs';\n  }\n  render() {// NOOP\n  }\n}\nclass GElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'g';\n  }\n  getBoundingBox(ctx) {\n    var boundingBox = new BoundingBox();\n    this.children.forEach(child => {\n      boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n    });\n    return boundingBox;\n  }\n}\nclass GradientElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.attributesToInherit = ['gradientUnits'];\n    this.stops = [];\n    var {\n      stops,\n      children\n    } = this;\n    children.forEach(child => {\n      if (child.type === 'stop') {\n        stops.push(child);\n      }\n    });\n  }\n  getGradientUnits() {\n    return this.getAttribute('gradientUnits').getString('objectBoundingBox');\n  }\n  createGradient(ctx, element, parentOpacityProp) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n    var stopsContainer = this;\n    if (this.getHrefAttribute().hasValue()) {\n      stopsContainer = this.getHrefAttribute().getDefinition();\n      this.inheritStopContainer(stopsContainer);\n    }\n    var {\n      stops\n    } = stopsContainer;\n    var gradient = this.getGradient(ctx, element);\n    if (!gradient) {\n      return this.addParentOpacity(parentOpacityProp, stops[stops.length - 1].color);\n    }\n    stops.forEach(stop => {\n      gradient.addColorStop(stop.offset, this.addParentOpacity(parentOpacityProp, stop.color));\n    });\n    if (this.getAttribute('gradientTransform').hasValue()) {\n      // render as transformed pattern on temporary canvas\n      var {\n        document\n      } = this;\n      var {\n        MAX_VIRTUAL_PIXELS,\n        viewPort\n      } = document.screen;\n      var [rootView] = viewPort.viewPorts;\n      var rect = new RectElement(document, null);\n      rect.attributes.x = new Property(document, 'x', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.y = new Property(document, 'y', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.width = new Property(document, 'width', MAX_VIRTUAL_PIXELS);\n      rect.attributes.height = new Property(document, 'height', MAX_VIRTUAL_PIXELS);\n      var group = new GElement(document, null);\n      group.attributes.transform = new Property(document, 'transform', this.getAttribute('gradientTransform').getValue());\n      group.children = [rect];\n      var patternSvg = new SVGElement(document, null);\n      patternSvg.attributes.x = new Property(document, 'x', 0);\n      patternSvg.attributes.y = new Property(document, 'y', 0);\n      patternSvg.attributes.width = new Property(document, 'width', rootView.width);\n      patternSvg.attributes.height = new Property(document, 'height', rootView.height);\n      patternSvg.children = [group];\n      var patternCanvas = document.createCanvas(rootView.width, rootView.height);\n      var patternCtx = patternCanvas.getContext('2d');\n      patternCtx.fillStyle = gradient;\n      patternSvg.render(patternCtx);\n      return patternCtx.createPattern(patternCanvas, 'no-repeat');\n    }\n    return gradient;\n  }\n  inheritStopContainer(stopsContainer) {\n    this.attributesToInherit.forEach(attributeToInherit => {\n      if (!this.getAttribute(attributeToInherit).hasValue() && stopsContainer.getAttribute(attributeToInherit).hasValue()) {\n        this.getAttribute(attributeToInherit, true).setValue(stopsContainer.getAttribute(attributeToInherit).getValue());\n      }\n    });\n  }\n  addParentOpacity(parentOpacityProp, color) {\n    if (parentOpacityProp.hasValue()) {\n      var colorProp = new Property(this.document, 'color', color);\n      return colorProp.addOpacity(parentOpacityProp).getColor();\n    }\n    return color;\n  }\n}\nclass LinearGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'linearGradient';\n    this.attributesToInherit.push('x1', 'y1', 'x2', 'y2');\n  }\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = isBoundingBoxUnits ? element.getBoundingBox(ctx) : null;\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n    if (!this.getAttribute('x1').hasValue() && !this.getAttribute('y1').hasValue() && !this.getAttribute('x2').hasValue() && !this.getAttribute('y2').hasValue()) {\n      this.getAttribute('x1', true).setValue(0);\n      this.getAttribute('y1', true).setValue(0);\n      this.getAttribute('x2', true).setValue(1);\n      this.getAttribute('y2', true).setValue(0);\n    }\n    var x1 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x1').getNumber() : this.getAttribute('x1').getPixels('x');\n    var y1 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y1').getNumber() : this.getAttribute('y1').getPixels('y');\n    var x2 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x2').getNumber() : this.getAttribute('x2').getPixels('x');\n    var y2 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y2').getNumber() : this.getAttribute('y2').getPixels('y');\n    if (x1 === x2 && y1 === y2) {\n      return null;\n    }\n    return ctx.createLinearGradient(x1, y1, x2, y2);\n  }\n}\nclass RadialGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'radialGradient';\n    this.attributesToInherit.push('cx', 'cy', 'r', 'fx', 'fy', 'fr');\n  }\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = element.getBoundingBox(ctx);\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n    if (!this.getAttribute('cx').hasValue()) {\n      this.getAttribute('cx', true).setValue('50%');\n    }\n    if (!this.getAttribute('cy').hasValue()) {\n      this.getAttribute('cy', true).setValue('50%');\n    }\n    if (!this.getAttribute('r').hasValue()) {\n      this.getAttribute('r', true).setValue('50%');\n    }\n    var cx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('cx').getNumber() : this.getAttribute('cx').getPixels('x');\n    var cy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('cy').getNumber() : this.getAttribute('cy').getPixels('y');\n    var fx = cx;\n    var fy = cy;\n    if (this.getAttribute('fx').hasValue()) {\n      fx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('fx').getNumber() : this.getAttribute('fx').getPixels('x');\n    }\n    if (this.getAttribute('fy').hasValue()) {\n      fy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('fy').getNumber() : this.getAttribute('fy').getPixels('y');\n    }\n    var r = isBoundingBoxUnits ? (boundingBox.width + boundingBox.height) / 2.0 * this.getAttribute('r').getNumber() : this.getAttribute('r').getPixels();\n    var fr = this.getAttribute('fr').getPixels();\n    return ctx.createRadialGradient(fx, fy, fr, cx, cy, r);\n  }\n}\nclass StopElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'stop';\n    var offset = Math.max(0, Math.min(1, this.getAttribute('offset').getNumber()));\n    var stopOpacity = this.getStyle('stop-opacity');\n    var stopColor = this.getStyle('stop-color', true);\n    if (stopColor.getString() === '') {\n      stopColor.setValue('#000');\n    }\n    if (stopOpacity.hasValue()) {\n      stopColor = stopColor.addOpacity(stopOpacity);\n    }\n    this.offset = offset;\n    this.color = stopColor.getColor();\n  }\n}\nclass AnimateElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'animate';\n    this.duration = 0;\n    this.initialValue = null;\n    this.initialUnits = '';\n    this.removed = false;\n    this.frozen = false;\n    document.screen.animations.push(this);\n    this.begin = this.getAttribute('begin').getMilliseconds();\n    this.maxDuration = this.begin + this.getAttribute('dur').getMilliseconds();\n    this.from = this.getAttribute('from');\n    this.to = this.getAttribute('to');\n    this.values = new Property(document, 'values', null);\n    var valuesAttr = this.getAttribute('values');\n    if (valuesAttr.hasValue()) {\n      this.values.setValue(valuesAttr.getString().split(';'));\n    }\n  }\n  getProperty() {\n    var attributeType = this.getAttribute('attributeType').getString();\n    var attributeName = this.getAttribute('attributeName').getString();\n    if (attributeType === 'CSS') {\n      return this.parent.getStyle(attributeName, true);\n    }\n    return this.parent.getAttribute(attributeName, true);\n  }\n  calcValue() {\n    var {\n      initialUnits\n    } = this;\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var newValue = from.getNumber() + (to.getNumber() - from.getNumber()) * progress;\n    if (initialUnits === '%') {\n      newValue *= 100.0; // numValue() returns 0-1 whereas properties are 0-100\n    }\n    return \"\".concat(newValue).concat(initialUnits);\n  }\n  update(delta) {\n    var {\n      parent\n    } = this;\n    var prop = this.getProperty(); // set initial value\n\n    if (!this.initialValue) {\n      this.initialValue = prop.getString();\n      this.initialUnits = prop.getUnits();\n    } // if we're past the end time\n\n    if (this.duration > this.maxDuration) {\n      var fill = this.getAttribute('fill').getString('remove'); // loop for indefinitely repeating animations\n\n      if (this.getAttribute('repeatCount').getString() === 'indefinite' || this.getAttribute('repeatDur').getString() === 'indefinite') {\n        this.duration = 0;\n      } else if (fill === 'freeze' && !this.frozen) {\n        this.frozen = true;\n        parent.animationFrozen = true;\n        parent.animationFrozenValue = prop.getString();\n      } else if (fill === 'remove' && !this.removed) {\n        this.removed = true;\n        prop.setValue(parent.animationFrozen ? parent.animationFrozenValue : this.initialValue);\n        return true;\n      }\n      return false;\n    }\n    this.duration += delta; // if we're past the begin time\n\n    var updated = false;\n    if (this.begin < this.duration) {\n      var newValue = this.calcValue(); // tween\n\n      var typeAttr = this.getAttribute('type');\n      if (typeAttr.hasValue()) {\n        // for transform, etc.\n        var type = typeAttr.getString();\n        newValue = \"\".concat(type, \"(\").concat(newValue, \")\");\n      }\n      prop.setValue(newValue);\n      updated = true;\n    }\n    return updated;\n  }\n  getProgress() {\n    var {\n      document,\n      values\n    } = this;\n    var result = {\n      progress: (this.duration - this.begin) / (this.maxDuration - this.begin)\n    };\n    if (values.hasValue()) {\n      var p = result.progress * (values.getValue().length - 1);\n      var lb = Math.floor(p);\n      var ub = Math.ceil(p);\n      result.from = new Property(document, 'from', parseFloat(values.getValue()[lb]));\n      result.to = new Property(document, 'to', parseFloat(values.getValue()[ub]));\n      result.progress = (p - lb) / (ub - lb);\n    } else {\n      result.from = this.from;\n      result.to = this.to;\n    }\n    return result;\n  }\n}\nclass AnimateColorElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateColor';\n  }\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress();\n    var colorFrom = new RGBColor(from.getColor());\n    var colorTo = new RGBColor(to.getColor());\n    if (colorFrom.ok && colorTo.ok) {\n      // tween color linearly\n      var r = colorFrom.r + (colorTo.r - colorFrom.r) * progress;\n      var g = colorFrom.g + (colorTo.g - colorFrom.g) * progress;\n      var b = colorFrom.b + (colorTo.b - colorFrom.b) * progress; // ? alpha\n\n      return \"rgb(\".concat(Math.floor(r), \", \").concat(Math.floor(g), \", \").concat(Math.floor(b), \")\");\n    }\n    return this.getAttribute('from').getColor();\n  }\n}\nclass AnimateTransformElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateTransform';\n  }\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var transformFrom = toNumbers(from.getString());\n    var transformTo = toNumbers(to.getString());\n    var newValue = transformFrom.map((from, i) => {\n      var to = transformTo[i];\n      return from + (to - from) * progress;\n    }).join(' ');\n    return newValue;\n  }\n}\nclass FontElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font';\n    this.glyphs = Object.create(null);\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    var {\n      definitions\n    } = document;\n    var {\n      children\n    } = this;\n    for (var child of children) {\n      switch (child.type) {\n        case 'font-face':\n          {\n            this.fontFace = child;\n            var fontFamilyStyle = child.getStyle('font-family');\n            if (fontFamilyStyle.hasValue()) {\n              definitions[fontFamilyStyle.getString()] = this;\n            }\n            break;\n          }\n        case 'missing-glyph':\n          this.missingGlyph = child;\n          break;\n        case 'glyph':\n          {\n            var glyph = child;\n            if (glyph.arabicForm) {\n              this.isRTL = true;\n              this.isArabic = true;\n              if (typeof this.glyphs[glyph.unicode] === 'undefined') {\n                this.glyphs[glyph.unicode] = Object.create(null);\n              }\n              this.glyphs[glyph.unicode][glyph.arabicForm] = glyph;\n            } else {\n              this.glyphs[glyph.unicode] = glyph;\n            }\n            break;\n          }\n      }\n    }\n  }\n  render() {// NO RENDER\n  }\n}\nclass FontFaceElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font-face';\n    this.ascent = this.getAttribute('ascent').getNumber();\n    this.descent = this.getAttribute('descent').getNumber();\n    this.unitsPerEm = this.getAttribute('units-per-em').getNumber();\n  }\n}\nclass MissingGlyphElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'missing-glyph';\n    this.horizAdvX = 0;\n  }\n}\nclass TRefElement extends TextElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'tref';\n  }\n  getText() {\n    var element = this.getHrefAttribute().getDefinition();\n    if (element) {\n      var firstChild = element.children[0];\n      if (firstChild) {\n        return firstChild.getText();\n      }\n    }\n    return '';\n  }\n}\nclass AElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'a';\n    var {\n      childNodes\n    } = node;\n    var firstChild = childNodes[0];\n    var hasText = childNodes.length > 0 && Array.from(childNodes).every(node => node.nodeType === 3);\n    this.hasText = hasText;\n    this.text = hasText ? this.getTextFromNode(firstChild) : '';\n  }\n  getText() {\n    return this.text;\n  }\n  renderChildren(ctx) {\n    if (this.hasText) {\n      // render as text element\n      super.renderChildren(ctx);\n      var {\n        document,\n        x,\n        y\n      } = this;\n      var {\n        mouse\n      } = document.screen;\n      var fontSize = new Property(document, 'fontSize', Font.parse(document.ctx.font).fontSize); // Do not calc bounding box if mouse is not working.\n\n      if (mouse.isWorking()) {\n        mouse.checkBoundingBox(this, new BoundingBox(x, y - fontSize.getPixels('y'), x + this.measureText(ctx), y));\n      }\n    } else if (this.children.length > 0) {\n      // render as temporary group\n      var g = new GElement(this.document, null);\n      g.children = this.children;\n      g.parent = this;\n      g.render(ctx);\n    }\n  }\n  onClick() {\n    var {\n      window\n    } = this.document;\n    if (window) {\n      window.open(this.getHrefAttribute().getString());\n    }\n  }\n  onMouseMove() {\n    var ctx = this.document.ctx;\n    ctx.canvas.style.cursor = 'pointer';\n  }\n}\nfunction ownKeys$2(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread$2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys$2(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys$2(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nclass TextPathElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'textPath';\n    this.textWidth = 0;\n    this.textHeight = 0;\n    this.pathLength = -1;\n    this.glyphInfo = null;\n    this.letterSpacingCache = [];\n    this.measuresCache = new Map([['', 0]]);\n    var pathElement = this.getHrefAttribute().getDefinition();\n    this.text = this.getTextFromNode();\n    this.dataArray = this.parsePathData(pathElement);\n  }\n  getText() {\n    return this.text;\n  }\n  path(ctx) {\n    var {\n      dataArray\n    } = this;\n    if (ctx) {\n      ctx.beginPath();\n    }\n    dataArray.forEach(_ref => {\n      var {\n        type,\n        points\n      } = _ref;\n      switch (type) {\n        case PathParser.LINE_TO:\n          if (ctx) {\n            ctx.lineTo(points[0], points[1]);\n          }\n          break;\n        case PathParser.MOVE_TO:\n          if (ctx) {\n            ctx.moveTo(points[0], points[1]);\n          }\n          break;\n        case PathParser.CURVE_TO:\n          if (ctx) {\n            ctx.bezierCurveTo(points[0], points[1], points[2], points[3], points[4], points[5]);\n          }\n          break;\n        case PathParser.QUAD_TO:\n          if (ctx) {\n            ctx.quadraticCurveTo(points[0], points[1], points[2], points[3]);\n          }\n          break;\n        case PathParser.ARC:\n          {\n            var [cx, cy, rx, ry, theta, dTheta, psi, fs] = points;\n            var r = rx > ry ? rx : ry;\n            var scaleX = rx > ry ? 1 : rx / ry;\n            var scaleY = rx > ry ? ry / rx : 1;\n            if (ctx) {\n              ctx.translate(cx, cy);\n              ctx.rotate(psi);\n              ctx.scale(scaleX, scaleY);\n              ctx.arc(0, 0, r, theta, theta + dTheta, Boolean(1 - fs));\n              ctx.scale(1 / scaleX, 1 / scaleY);\n              ctx.rotate(-psi);\n              ctx.translate(-cx, -cy);\n            }\n            break;\n          }\n        case PathParser.CLOSE_PATH:\n          if (ctx) {\n            ctx.closePath();\n          }\n          break;\n      }\n    });\n  }\n  renderChildren(ctx) {\n    this.setTextData(ctx);\n    ctx.save();\n    var textDecoration = this.parent.getStyle('text-decoration').getString();\n    var fontSize = this.getFontSize();\n    var {\n      glyphInfo\n    } = this;\n    var fill = ctx.fillStyle;\n    if (textDecoration === 'underline') {\n      ctx.beginPath();\n    }\n    glyphInfo.forEach((glyph, i) => {\n      var {\n        p0,\n        p1,\n        rotation,\n        text: partialText\n      } = glyph;\n      ctx.save();\n      ctx.translate(p0.x, p0.y);\n      ctx.rotate(rotation);\n      if (ctx.fillStyle) {\n        ctx.fillText(partialText, 0, 0);\n      }\n      if (ctx.strokeStyle) {\n        ctx.strokeText(partialText, 0, 0);\n      }\n      ctx.restore();\n      if (textDecoration === 'underline') {\n        if (i === 0) {\n          ctx.moveTo(p0.x, p0.y + fontSize / 8);\n        }\n        ctx.lineTo(p1.x, p1.y + fontSize / 5);\n      } // // To assist with debugging visually, uncomment following\n      //\n      // ctx.beginPath();\n      // if (i % 2)\n      // \tctx.strokeStyle = 'red';\n      // else\n      // \tctx.strokeStyle = 'green';\n      // ctx.moveTo(p0.x, p0.y);\n      // ctx.lineTo(p1.x, p1.y);\n      // ctx.stroke();\n      // ctx.closePath();\n    });\n    if (textDecoration === 'underline') {\n      ctx.lineWidth = fontSize / 20;\n      ctx.strokeStyle = fill;\n      ctx.stroke();\n      ctx.closePath();\n    }\n    ctx.restore();\n  }\n  getLetterSpacingAt() {\n    var idx = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return this.letterSpacingCache[idx] || 0;\n  }\n  findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, inputOffset, dy, c, charI) {\n    var offset = inputOffset;\n    var glyphWidth = this.measureText(ctx, c);\n    if (c === ' ' && anchor === 'justify' && textFullWidth < fullPathWidth) {\n      glyphWidth += (fullPathWidth - textFullWidth) / spacesNumber;\n    }\n    if (charI > -1) {\n      offset += this.getLetterSpacingAt(charI);\n    }\n    var splineStep = this.textHeight / 20;\n    var p0 = this.getEquidistantPointOnPath(offset, splineStep, 0);\n    var p1 = this.getEquidistantPointOnPath(offset + glyphWidth, splineStep, 0);\n    var segment = {\n      p0,\n      p1\n    };\n    var rotation = p0 && p1 ? Math.atan2(p1.y - p0.y, p1.x - p0.x) : 0;\n    if (dy) {\n      var dyX = Math.cos(Math.PI / 2 + rotation) * dy;\n      var dyY = Math.cos(-rotation) * dy;\n      segment.p0 = _objectSpread$2(_objectSpread$2({}, p0), {}, {\n        x: p0.x + dyX,\n        y: p0.y + dyY\n      });\n      segment.p1 = _objectSpread$2(_objectSpread$2({}, p1), {}, {\n        x: p1.x + dyX,\n        y: p1.y + dyY\n      });\n    }\n    offset += glyphWidth;\n    return {\n      offset,\n      segment,\n      rotation\n    };\n  }\n  measureText(ctx, text) {\n    var {\n      measuresCache\n    } = this;\n    var targetText = text || this.getText();\n    if (measuresCache.has(targetText)) {\n      return measuresCache.get(targetText);\n    }\n    var measure = this.measureTargetText(ctx, targetText);\n    measuresCache.set(targetText, measure);\n    return measure;\n  } // This method supposes what all custom fonts already loaded.\n  // If some font will be loaded after this method call, <textPath> will not be rendered correctly.\n  // You need to call this method manually to update glyphs cache.\n\n  setTextData(ctx) {\n    if (this.glyphInfo) {\n      return;\n    }\n    var renderText = this.getText();\n    var chars = renderText.split('');\n    var spacesNumber = renderText.split(' ').length - 1;\n    var dx = this.parent.getAttribute('dx').split().map(_ => _.getPixels('x'));\n    var dy = this.parent.getAttribute('dy').getPixels('y');\n    var anchor = this.parent.getStyle('text-anchor').getString('start');\n    var thisSpacing = this.getStyle('letter-spacing');\n    var parentSpacing = this.parent.getStyle('letter-spacing');\n    var letterSpacing = 0;\n    if (!thisSpacing.hasValue() || thisSpacing.getValue() === 'inherit') {\n      letterSpacing = parentSpacing.getPixels();\n    } else if (thisSpacing.hasValue()) {\n      if (thisSpacing.getValue() !== 'initial' && thisSpacing.getValue() !== 'unset') {\n        letterSpacing = thisSpacing.getPixels();\n      }\n    } // fill letter-spacing cache\n\n    var letterSpacingCache = [];\n    var textLen = renderText.length;\n    this.letterSpacingCache = letterSpacingCache;\n    for (var i = 0; i < textLen; i++) {\n      letterSpacingCache.push(typeof dx[i] !== 'undefined' ? dx[i] : letterSpacing);\n    }\n    var dxSum = letterSpacingCache.reduce((acc, cur, i) => i === 0 ? 0 : acc + cur || 0, 0);\n    var textWidth = this.measureText(ctx);\n    var textFullWidth = Math.max(textWidth + dxSum, 0);\n    this.textWidth = textWidth;\n    this.textHeight = this.getFontSize();\n    this.glyphInfo = [];\n    var fullPathWidth = this.getPathLength();\n    var startOffset = this.getStyle('startOffset').getNumber(0) * fullPathWidth;\n    var offset = 0;\n    if (anchor === 'middle' || anchor === 'center') {\n      offset = -textFullWidth / 2;\n    }\n    if (anchor === 'end' || anchor === 'right') {\n      offset = -textFullWidth;\n    }\n    offset += startOffset;\n    chars.forEach((char, i) => {\n      // Find such segment what distance between p0 and p1 is approx. width of glyph\n      var {\n        offset: nextOffset,\n        segment,\n        rotation\n      } = this.findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, offset, dy, char, i);\n      offset = nextOffset;\n      if (!segment.p0 || !segment.p1) {\n        return;\n      } // const width = this.getLineLength(\n      // \tsegment.p0.x,\n      // \tsegment.p0.y,\n      // \tsegment.p1.x,\n      // \tsegment.p1.y\n      // );\n      // Note: Since glyphs are rendered one at a time, any kerning pair data built into the font will not be used.\n      // Can foresee having a rough pair table built in that the developer can override as needed.\n      // Or use \"dx\" attribute of the <text> node as a naive replacement\n      // const kern = 0;\n      // placeholder for future implementation\n      // const midpoint = this.getPointOnLine(\n      // \tkern + width / 2.0,\n      // \tsegment.p0.x, segment.p0.y, segment.p1.x, segment.p1.y\n      // );\n\n      this.glyphInfo.push({\n        // transposeX: midpoint.x,\n        // transposeY: midpoint.y,\n        text: chars[i],\n        p0: segment.p0,\n        p1: segment.p1,\n        rotation\n      });\n    });\n  }\n  parsePathData(path) {\n    this.pathLength = -1; // reset path length\n\n    if (!path) {\n      return [];\n    }\n    var pathCommands = [];\n    var {\n      pathParser\n    } = path;\n    pathParser.reset(); // convert l, H, h, V, and v to L\n\n    while (!pathParser.isEnd()) {\n      var {\n        current\n      } = pathParser;\n      var startX = current ? current.x : 0;\n      var startY = current ? current.y : 0;\n      var command = pathParser.next();\n      var nextCommandType = command.type;\n      var points = [];\n      switch (command.type) {\n        case PathParser.MOVE_TO:\n          this.pathM(pathParser, points);\n          break;\n        case PathParser.LINE_TO:\n          nextCommandType = this.pathL(pathParser, points);\n          break;\n        case PathParser.HORIZ_LINE_TO:\n          nextCommandType = this.pathH(pathParser, points);\n          break;\n        case PathParser.VERT_LINE_TO:\n          nextCommandType = this.pathV(pathParser, points);\n          break;\n        case PathParser.CURVE_TO:\n          this.pathC(pathParser, points);\n          break;\n        case PathParser.SMOOTH_CURVE_TO:\n          nextCommandType = this.pathS(pathParser, points);\n          break;\n        case PathParser.QUAD_TO:\n          this.pathQ(pathParser, points);\n          break;\n        case PathParser.SMOOTH_QUAD_TO:\n          nextCommandType = this.pathT(pathParser, points);\n          break;\n        case PathParser.ARC:\n          points = this.pathA(pathParser);\n          break;\n        case PathParser.CLOSE_PATH:\n          PathElement.pathZ(pathParser);\n          break;\n      }\n      if (command.type !== PathParser.CLOSE_PATH) {\n        pathCommands.push({\n          type: nextCommandType,\n          points,\n          start: {\n            x: startX,\n            y: startY\n          },\n          pathLength: this.calcLength(startX, startY, nextCommandType, points)\n        });\n      } else {\n        pathCommands.push({\n          type: PathParser.CLOSE_PATH,\n          points: [],\n          pathLength: 0\n        });\n      }\n    }\n    return pathCommands;\n  }\n  pathM(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathM(pathParser).point;\n    points.push(x, y);\n  }\n  pathL(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathL(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n  pathH(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathH(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n  pathV(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathV(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n  pathC(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n  pathS(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.CURVE_TO;\n  }\n  pathQ(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n  pathT(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.QUAD_TO;\n  }\n  pathA(pathParser) {\n    var {\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser);\n    if (sweepFlag === 0 && ad > 0) {\n      ad -= 2 * Math.PI;\n    }\n    if (sweepFlag === 1 && ad < 0) {\n      ad += 2 * Math.PI;\n    }\n    return [centp.x, centp.y, rX, rY, a1, ad, xAxisRotation, sweepFlag];\n  }\n  calcLength(x, y, commandType, points) {\n    var len = 0;\n    var p1 = null;\n    var p2 = null;\n    var t = 0;\n    switch (commandType) {\n      case PathParser.LINE_TO:\n        return this.getLineLength(x, y, points[0], points[1]);\n      case PathParser.CURVE_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnCubicBezier(0, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnCubicBezier(t, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n        return len;\n      case PathParser.QUAD_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnQuadraticBezier(0, x, y, points[0], points[1], points[2], points[3]);\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnQuadraticBezier(t, x, y, points[0], points[1], points[2], points[3]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n        return len;\n      case PathParser.ARC:\n        {\n          // Approximates by breaking curve into line segments\n          len = 0.0;\n          var start = points[4]; // 4 = theta\n\n          var dTheta = points[5]; // 5 = dTheta\n\n          var end = points[4] + dTheta;\n          var inc = Math.PI / 180.0; // 1 degree resolution\n\n          if (Math.abs(start - end) < inc) {\n            inc = Math.abs(start - end);\n          } // Note: for purpose of calculating arc length, not going to worry about rotating X-axis by angle psi\n\n          p1 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], start, 0);\n          if (dTheta < 0) {\n            // clockwise\n            for (t = start - inc; t > end; t -= inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          } else {\n            // counter-clockwise\n            for (t = start + inc; t < end; t += inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          }\n          p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], end, 0);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          return len;\n        }\n    }\n    return 0;\n  }\n  getPointOnLine(dist, p1x, p1y, p2x, p2y) {\n    var fromX = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : p1x;\n    var fromY = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : p1y;\n    var m = (p2y - p1y) / (p2x - p1x + PSEUDO_ZERO);\n    var run = Math.sqrt(dist * dist / (1 + m * m));\n    if (p2x < p1x) {\n      run *= -1;\n    }\n    var rise = m * run;\n    var pt = null;\n    if (p2x === p1x) {\n      // vertical line\n      pt = {\n        x: fromX,\n        y: fromY + rise\n      };\n    } else if ((fromY - p1y) / (fromX - p1x + PSEUDO_ZERO) === m) {\n      pt = {\n        x: fromX + run,\n        y: fromY + rise\n      };\n    } else {\n      var ix = 0;\n      var iy = 0;\n      var len = this.getLineLength(p1x, p1y, p2x, p2y);\n      if (len < PSEUDO_ZERO) {\n        return null;\n      }\n      var u = (fromX - p1x) * (p2x - p1x) + (fromY - p1y) * (p2y - p1y);\n      u /= len * len;\n      ix = p1x + u * (p2x - p1x);\n      iy = p1y + u * (p2y - p1y);\n      var pRise = this.getLineLength(fromX, fromY, ix, iy);\n      var pRun = Math.sqrt(dist * dist - pRise * pRise);\n      run = Math.sqrt(pRun * pRun / (1 + m * m));\n      if (p2x < p1x) {\n        run *= -1;\n      }\n      rise = m * run;\n      pt = {\n        x: ix + run,\n        y: iy + rise\n      };\n    }\n    return pt;\n  }\n  getPointOnPath(distance) {\n    var fullLen = this.getPathLength();\n    var cumulativePathLength = 0;\n    var p = null;\n    if (distance < -0.00005 || distance - 0.00005 > fullLen) {\n      return null;\n    }\n    var {\n      dataArray\n    } = this;\n    for (var command of dataArray) {\n      if (command && (command.pathLength < 0.00005 || cumulativePathLength + command.pathLength + 0.00005 < distance)) {\n        cumulativePathLength += command.pathLength;\n        continue;\n      }\n      var delta = distance - cumulativePathLength;\n      var currentT = 0;\n      switch (command.type) {\n        case PathParser.LINE_TO:\n          p = this.getPointOnLine(delta, command.start.x, command.start.y, command.points[0], command.points[1], command.start.x, command.start.y);\n          break;\n        case PathParser.ARC:\n          {\n            var start = command.points[4]; // 4 = theta\n\n            var dTheta = command.points[5]; // 5 = dTheta\n\n            var end = command.points[4] + dTheta;\n            currentT = start + delta / command.pathLength * dTheta;\n            if (dTheta < 0 && currentT < end || dTheta >= 0 && currentT > end) {\n              break;\n            }\n            p = this.getPointOnEllipticalArc(command.points[0], command.points[1], command.points[2], command.points[3], currentT, command.points[6]);\n            break;\n          }\n        case PathParser.CURVE_TO:\n          currentT = delta / command.pathLength;\n          if (currentT > 1) {\n            currentT = 1;\n          }\n          p = this.getPointOnCubicBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3], command.points[4], command.points[5]);\n          break;\n        case PathParser.QUAD_TO:\n          currentT = delta / command.pathLength;\n          if (currentT > 1) {\n            currentT = 1;\n          }\n          p = this.getPointOnQuadraticBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3]);\n          break;\n      }\n      if (p) {\n        return p;\n      }\n      break;\n    }\n    return null;\n  }\n  getLineLength(x1, y1, x2, y2) {\n    return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n  }\n  getPathLength() {\n    if (this.pathLength === -1) {\n      this.pathLength = this.dataArray.reduce((length, command) => command.pathLength > 0 ? length + command.pathLength : length, 0);\n    }\n    return this.pathLength;\n  }\n  getPointOnCubicBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y, p4x, p4y) {\n    var x = p4x * CB1(pct) + p3x * CB2(pct) + p2x * CB3(pct) + p1x * CB4(pct);\n    var y = p4y * CB1(pct) + p3y * CB2(pct) + p2y * CB3(pct) + p1y * CB4(pct);\n    return {\n      x,\n      y\n    };\n  }\n  getPointOnQuadraticBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y) {\n    var x = p3x * QB1(pct) + p2x * QB2(pct) + p1x * QB3(pct);\n    var y = p3y * QB1(pct) + p2y * QB2(pct) + p1y * QB3(pct);\n    return {\n      x,\n      y\n    };\n  }\n  getPointOnEllipticalArc(cx, cy, rx, ry, theta, psi) {\n    var cosPsi = Math.cos(psi);\n    var sinPsi = Math.sin(psi);\n    var pt = {\n      x: rx * Math.cos(theta),\n      y: ry * Math.sin(theta)\n    };\n    return {\n      x: cx + (pt.x * cosPsi - pt.y * sinPsi),\n      y: cy + (pt.x * sinPsi + pt.y * cosPsi)\n    };\n  } // TODO need some optimisations. possibly build cache only for curved segments?\n\n  buildEquidistantCache(inputStep, inputPrecision) {\n    var fullLen = this.getPathLength();\n    var precision = inputPrecision || 0.25; // accuracy vs performance\n\n    var step = inputStep || fullLen / 100;\n    if (!this.equidistantCache || this.equidistantCache.step !== step || this.equidistantCache.precision !== precision) {\n      // Prepare cache\n      this.equidistantCache = {\n        step,\n        precision,\n        points: []\n      }; // Calculate points\n\n      var s = 0;\n      for (var l = 0; l <= fullLen; l += precision) {\n        var p0 = this.getPointOnPath(l);\n        var p1 = this.getPointOnPath(l + precision);\n        if (!p0 || !p1) {\n          continue;\n        }\n        s += this.getLineLength(p0.x, p0.y, p1.x, p1.y);\n        if (s >= step) {\n          this.equidistantCache.points.push({\n            x: p0.x,\n            y: p0.y,\n            distance: l\n          });\n          s -= step;\n        }\n      }\n    }\n  }\n  getEquidistantPointOnPath(targetDistance, step, precision) {\n    this.buildEquidistantCache(step, precision);\n    if (targetDistance < 0 || targetDistance - this.getPathLength() > 0.00005) {\n      return null;\n    }\n    var idx = Math.round(targetDistance / this.getPathLength() * (this.equidistantCache.points.length - 1));\n    return this.equidistantCache.points[idx] || null;\n  }\n}\nvar dataUriRegex = /^\\s*data:(([^/,;]+\\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;\nclass ImageElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'image';\n    this.loaded = false;\n    var href = this.getHrefAttribute().getString();\n    if (!href) {\n      return;\n    }\n    var isSvg = href.endsWith('.svg') || /^\\s*data:image\\/svg\\+xml/i.test(href);\n    document.images.push(this);\n    if (!isSvg) {\n      void this.loadImage(href);\n    } else {\n      void this.loadSvg(href);\n    }\n    this.isSvg = isSvg;\n  }\n  loadImage(href) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        var image = yield _this.document.createImage(href);\n        _this.image = image;\n      } catch (err) {\n        console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n      }\n      _this.loaded = true;\n    })();\n  }\n  loadSvg(href) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      var match = dataUriRegex.exec(href);\n      if (match) {\n        var data = match[5];\n        if (match[4] === 'base64') {\n          _this2.image = atob(data);\n        } else {\n          _this2.image = decodeURIComponent(data);\n        }\n      } else {\n        try {\n          var response = yield _this2.document.fetch(href);\n          var svg = yield response.text();\n          _this2.image = svg;\n        } catch (err) {\n          console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n        }\n      }\n      _this2.loaded = true;\n    })();\n  }\n  renderChildren(ctx) {\n    var {\n      document,\n      image,\n      loaded\n    } = this;\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n    if (!loaded || !image || !width || !height) {\n      return;\n    }\n    ctx.save();\n    ctx.translate(x, y);\n    if (this.isSvg) {\n      var subDocument = document.canvg.forkString(ctx, this.image, {\n        ignoreMouse: true,\n        ignoreAnimation: true,\n        ignoreDimensions: true,\n        ignoreClear: true,\n        offsetX: 0,\n        offsetY: 0,\n        scaleWidth: width,\n        scaleHeight: height\n      });\n      subDocument.document.documentElement.parent = this;\n      void subDocument.render();\n    } else {\n      var _image = this.image;\n      document.setViewBox({\n        ctx,\n        aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n        width,\n        desiredWidth: _image.width,\n        height,\n        desiredHeight: _image.height\n      });\n      if (this.loaded) {\n        if (typeof _image.complete === 'undefined' || _image.complete) {\n          ctx.drawImage(_image, 0, 0);\n        }\n      }\n    }\n    ctx.restore();\n  }\n  getBoundingBox() {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n}\nclass SymbolElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'symbol';\n  }\n  render(_) {// NO RENDER\n  }\n}\nclass SVGFontLoader {\n  constructor(document) {\n    this.document = document;\n    this.loaded = false;\n    document.fonts.push(this);\n  }\n  load(fontFamily, url) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        var {\n          document\n        } = _this;\n        var svgDocument = yield document.canvg.parser.load(url);\n        var fonts = svgDocument.getElementsByTagName('font');\n        Array.from(fonts).forEach(fontNode => {\n          var font = document.createElement(fontNode);\n          document.definitions[fontFamily] = font;\n        });\n      } catch (err) {\n        console.error(\"Error while loading font \\\"\".concat(url, \"\\\":\"), err);\n      }\n      _this.loaded = true;\n    })();\n  }\n}\nclass StyleElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'style';\n    var css = compressSpaces(Array.from(node.childNodes) // NEED TEST\n    .map(_ => _.textContent).join('').replace(/(\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*+\\/)|(^[\\s]*\\/\\/.*)/gm, '') // remove comments\n    .replace(/@import.*;/g, '') // remove imports\n    );\n    var cssDefs = css.split('}');\n    cssDefs.forEach(_ => {\n      var def = _.trim();\n      if (!def) {\n        return;\n      }\n      var cssParts = def.split('{');\n      var cssClasses = cssParts[0].split(',');\n      var cssProps = cssParts[1].split(';');\n      cssClasses.forEach(_ => {\n        var cssClass = _.trim();\n        if (!cssClass) {\n          return;\n        }\n        var props = document.styles[cssClass] || {};\n        cssProps.forEach(cssProp => {\n          var prop = cssProp.indexOf(':');\n          var name = cssProp.substr(0, prop).trim();\n          var value = cssProp.substr(prop + 1, cssProp.length - prop).trim();\n          if (name && value) {\n            props[name] = new Property(document, name, value);\n          }\n        });\n        document.styles[cssClass] = props;\n        document.stylesSpecificity[cssClass] = getSelectorSpecificity(cssClass);\n        if (cssClass === '@font-face') {\n          //  && !nodeEnv\n          var fontFamily = props['font-family'].getString().replace(/\"|'/g, '');\n          var srcs = props.src.getString().split(',');\n          srcs.forEach(src => {\n            if (src.indexOf('format(\"svg\")') > 0) {\n              var url = parseExternalUrl(src);\n              if (url) {\n                void new SVGFontLoader(document).load(fontFamily, url);\n              }\n            }\n          });\n        }\n      });\n    });\n  }\n}\nStyleElement.parseExternalUrl = parseExternalUrl;\nclass UseElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'use';\n  }\n  setContext(ctx) {\n    super.setContext(ctx);\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n    if (xAttr.hasValue()) {\n      ctx.translate(xAttr.getPixels('x'), 0);\n    }\n    if (yAttr.hasValue()) {\n      ctx.translate(0, yAttr.getPixels('y'));\n    }\n  }\n  path(ctx) {\n    var {\n      element\n    } = this;\n    if (element) {\n      element.path(ctx);\n    }\n  }\n  renderChildren(ctx) {\n    var {\n      document,\n      element\n    } = this;\n    if (element) {\n      var tempSvg = element;\n      if (element.type === 'symbol') {\n        // render me using a temporary svg element in symbol cases (http://www.w3.org/TR/SVG/struct.html#UseElement)\n        tempSvg = new SVGElement(document, null);\n        tempSvg.attributes.viewBox = new Property(document, 'viewBox', element.getAttribute('viewBox').getString());\n        tempSvg.attributes.preserveAspectRatio = new Property(document, 'preserveAspectRatio', element.getAttribute('preserveAspectRatio').getString());\n        tempSvg.attributes.overflow = new Property(document, 'overflow', element.getAttribute('overflow').getString());\n        tempSvg.children = element.children; // element is still the parent of the children\n\n        element.styles.opacity = new Property(document, 'opacity', this.calculateOpacity());\n      }\n      if (tempSvg.type === 'svg') {\n        var widthStyle = this.getStyle('width', false, true);\n        var heightStyle = this.getStyle('height', false, true); // if symbol or svg, inherit width/height from me\n\n        if (widthStyle.hasValue()) {\n          tempSvg.attributes.width = new Property(document, 'width', widthStyle.getString());\n        }\n        if (heightStyle.hasValue()) {\n          tempSvg.attributes.height = new Property(document, 'height', heightStyle.getString());\n        }\n      }\n      var oldParent = tempSvg.parent;\n      tempSvg.parent = this;\n      tempSvg.render(ctx);\n      tempSvg.parent = oldParent;\n    }\n  }\n  getBoundingBox(ctx) {\n    var {\n      element\n    } = this;\n    if (element) {\n      return element.getBoundingBox(ctx);\n    }\n    return null;\n  }\n  elementTransform() {\n    var {\n      document,\n      element\n    } = this;\n    return Transform.fromElement(document, element);\n  }\n  get element() {\n    if (!this.cachedElement) {\n      this.cachedElement = this.getHrefAttribute().getDefinition();\n    }\n    return this.cachedElement;\n  }\n}\nfunction imGet(img, x, y, width, _height, rgba) {\n  return img[y * width * 4 + x * 4 + rgba];\n}\nfunction imSet(img, x, y, width, _height, rgba, val) {\n  img[y * width * 4 + x * 4 + rgba] = val;\n}\nfunction m(matrix, i, v) {\n  var mi = matrix[i];\n  return mi * v;\n}\nfunction c(a, m1, m2, m3) {\n  return m1 + Math.cos(a) * m2 + Math.sin(a) * m3;\n}\nclass FeColorMatrixElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feColorMatrix';\n    var matrix = toNumbers(this.getAttribute('values').getString());\n    switch (this.getAttribute('type').getString('matrix')) {\n      // http://www.w3.org/TR/SVG/filters.html#feColorMatrixElement\n      case 'saturate':\n        {\n          var s = matrix[0];\n          /* eslint-disable array-element-newline */\n\n          matrix = [0.213 + 0.787 * s, 0.715 - 0.715 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 + 0.285 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 - 0.715 * s, 0.072 + 0.928 * s, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n      case 'hueRotate':\n        {\n          var a = matrix[0] * Math.PI / 180.0;\n          /* eslint-disable array-element-newline */\n\n          matrix = [c(a, 0.213, 0.787, -0.213), c(a, 0.715, -0.715, -0.715), c(a, 0.072, -0.072, 0.928), 0, 0, c(a, 0.213, -0.213, 0.143), c(a, 0.715, 0.285, 0.140), c(a, 0.072, -0.072, -0.283), 0, 0, c(a, 0.213, -0.213, -0.787), c(a, 0.715, -0.715, 0.715), c(a, 0.072, 0.928, 0.072), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n      case 'luminanceToAlpha':\n        /* eslint-disable array-element-newline */\n        matrix = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2125, 0.7154, 0.0721, 0, 0, 0, 0, 0, 0, 1];\n        /* eslint-enable array-element-newline */\n\n        break;\n    }\n    this.matrix = matrix;\n    this.includeOpacity = this.getAttribute('includeOpacity').hasValue();\n  }\n  apply(ctx, _x, _y, width, height) {\n    // assuming x==0 && y==0 for now\n    var {\n      includeOpacity,\n      matrix\n    } = this;\n    var srcData = ctx.getImageData(0, 0, width, height);\n    for (var y = 0; y < height; y++) {\n      for (var x = 0; x < width; x++) {\n        var r = imGet(srcData.data, x, y, width, height, 0);\n        var g = imGet(srcData.data, x, y, width, height, 1);\n        var b = imGet(srcData.data, x, y, width, height, 2);\n        var a = imGet(srcData.data, x, y, width, height, 3);\n        var nr = m(matrix, 0, r) + m(matrix, 1, g) + m(matrix, 2, b) + m(matrix, 3, a) + m(matrix, 4, 1);\n        var ng = m(matrix, 5, r) + m(matrix, 6, g) + m(matrix, 7, b) + m(matrix, 8, a) + m(matrix, 9, 1);\n        var nb = m(matrix, 10, r) + m(matrix, 11, g) + m(matrix, 12, b) + m(matrix, 13, a) + m(matrix, 14, 1);\n        var na = m(matrix, 15, r) + m(matrix, 16, g) + m(matrix, 17, b) + m(matrix, 18, a) + m(matrix, 19, 1);\n        if (includeOpacity) {\n          nr = 0;\n          ng = 0;\n          nb = 0;\n          na *= a / 255;\n        }\n        imSet(srcData.data, x, y, width, height, 0, nr);\n        imSet(srcData.data, x, y, width, height, 1, ng);\n        imSet(srcData.data, x, y, width, height, 2, nb);\n        imSet(srcData.data, x, y, width, height, 3, na);\n      }\n    }\n    ctx.clearRect(0, 0, width, height);\n    ctx.putImageData(srcData, 0, 0);\n  }\n}\nclass MaskElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'mask';\n  }\n  apply(ctx, element) {\n    var {\n      document\n    } = this; // render as temp svg\n\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n    if (!width && !height) {\n      var boundingBox = new BoundingBox();\n      this.children.forEach(child => {\n        boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n      });\n      x = Math.floor(boundingBox.x1);\n      y = Math.floor(boundingBox.y1);\n      width = Math.floor(boundingBox.width);\n      height = Math.floor(boundingBox.height);\n    }\n    var ignoredStyles = this.removeStyles(element, MaskElement.ignoreStyles);\n    var maskCanvas = document.createCanvas(x + width, y + height);\n    var maskCtx = maskCanvas.getContext('2d');\n    document.screen.setDefaults(maskCtx);\n    this.renderChildren(maskCtx); // convert mask to alpha with a fake node\n    // TODO: refactor out apply from feColorMatrix\n\n    new FeColorMatrixElement(document, {\n      nodeType: 1,\n      childNodes: [],\n      attributes: [{\n        nodeName: 'type',\n        value: 'luminanceToAlpha'\n      }, {\n        nodeName: 'includeOpacity',\n        value: 'true'\n      }]\n    }).apply(maskCtx, 0, 0, x + width, y + height);\n    var tmpCanvas = document.createCanvas(x + width, y + height);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    element.render(tmpCtx);\n    tmpCtx.globalCompositeOperation = 'destination-in';\n    tmpCtx.fillStyle = maskCtx.createPattern(maskCanvas, 'no-repeat');\n    tmpCtx.fillRect(0, 0, x + width, y + height);\n    ctx.fillStyle = tmpCtx.createPattern(tmpCanvas, 'no-repeat');\n    ctx.fillRect(0, 0, x + width, y + height); // reassign mask\n\n    this.restoreStyles(element, ignoredStyles);\n  }\n  render(_) {// NO RENDER\n  }\n}\nMaskElement.ignoreStyles = ['mask', 'transform', 'clip-path'];\nvar noop = () => {// NOOP\n};\nclass ClipPathElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'clipPath';\n  }\n  apply(ctx) {\n    var {\n      document\n    } = this;\n    var contextProto = Reflect.getPrototypeOf(ctx);\n    var {\n      beginPath,\n      closePath\n    } = ctx;\n    if (contextProto) {\n      contextProto.beginPath = noop;\n      contextProto.closePath = noop;\n    }\n    Reflect.apply(beginPath, ctx, []);\n    this.children.forEach(child => {\n      if (typeof child.path === 'undefined') {\n        return;\n      }\n      var transform = typeof child.elementTransform !== 'undefined' ? child.elementTransform() : null; // handle <use />\n\n      if (!transform) {\n        transform = Transform.fromElement(document, child);\n      }\n      if (transform) {\n        transform.apply(ctx);\n      }\n      child.path(ctx);\n      if (contextProto) {\n        contextProto.closePath = closePath;\n      }\n      if (transform) {\n        transform.unapply(ctx);\n      }\n    });\n    Reflect.apply(closePath, ctx, []);\n    ctx.clip();\n    if (contextProto) {\n      contextProto.beginPath = beginPath;\n      contextProto.closePath = closePath;\n    }\n  }\n  render(_) {// NO RENDER\n  }\n}\nclass FilterElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'filter';\n  }\n  apply(ctx, element) {\n    // render as temp svg\n    var {\n      document,\n      children\n    } = this;\n    var boundingBox = element.getBoundingBox(ctx);\n    if (!boundingBox) {\n      return;\n    }\n    var px = 0;\n    var py = 0;\n    children.forEach(child => {\n      var efd = child.extraFilterDistance || 0;\n      px = Math.max(px, efd);\n      py = Math.max(py, efd);\n    });\n    var width = Math.floor(boundingBox.width);\n    var height = Math.floor(boundingBox.height);\n    var tmpCanvasWidth = width + 2 * px;\n    var tmpCanvasHeight = height + 2 * py;\n    if (tmpCanvasWidth < 1 || tmpCanvasHeight < 1) {\n      return;\n    }\n    var x = Math.floor(boundingBox.x);\n    var y = Math.floor(boundingBox.y);\n    var ignoredStyles = this.removeStyles(element, FilterElement.ignoreStyles);\n    var tmpCanvas = document.createCanvas(tmpCanvasWidth, tmpCanvasHeight);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    tmpCtx.translate(-x + px, -y + py);\n    element.render(tmpCtx); // apply filters\n\n    children.forEach(child => {\n      if (typeof child.apply === 'function') {\n        child.apply(tmpCtx, 0, 0, tmpCanvasWidth, tmpCanvasHeight);\n      }\n    }); // render on me\n\n    ctx.drawImage(tmpCanvas, 0, 0, tmpCanvasWidth, tmpCanvasHeight, x - px, y - py, tmpCanvasWidth, tmpCanvasHeight);\n    this.restoreStyles(element, ignoredStyles);\n  }\n  render(_) {// NO RENDER\n  }\n}\nFilterElement.ignoreStyles = ['filter', 'transform', 'clip-path'];\nclass FeDropShadowElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feDropShadow';\n    this.addStylesFromStyleDefinition();\n  }\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n}\nclass FeMorphologyElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feMorphology';\n  }\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n}\nclass FeCompositeElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feComposite';\n  }\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n}\nclass FeGaussianBlurElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feGaussianBlur';\n    this.blurRadius = Math.floor(this.getAttribute('stdDeviation').getNumber());\n    this.extraFilterDistance = this.blurRadius;\n  }\n  apply(ctx, x, y, width, height) {\n    var {\n      document,\n      blurRadius\n    } = this;\n    var body = document.window ? document.window.document.body : null;\n    var canvas = ctx.canvas; // StackBlur requires canvas be on document\n\n    canvas.id = document.getUniqueId();\n    if (body) {\n      canvas.style.display = 'none';\n      body.appendChild(canvas);\n    }\n    canvasRGBA(canvas, x, y, width, height, blurRadius);\n    if (body) {\n      body.removeChild(canvas);\n    }\n  }\n}\nclass TitleElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'title';\n  }\n}\nclass DescElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'desc';\n  }\n}\nvar elements = {\n  'svg': SVGElement,\n  'rect': RectElement,\n  'circle': CircleElement,\n  'ellipse': EllipseElement,\n  'line': LineElement,\n  'polyline': PolylineElement,\n  'polygon': PolygonElement,\n  'path': PathElement,\n  'pattern': PatternElement,\n  'marker': MarkerElement,\n  'defs': DefsElement,\n  'linearGradient': LinearGradientElement,\n  'radialGradient': RadialGradientElement,\n  'stop': StopElement,\n  'animate': AnimateElement,\n  'animateColor': AnimateColorElement,\n  'animateTransform': AnimateTransformElement,\n  'font': FontElement,\n  'font-face': FontFaceElement,\n  'missing-glyph': MissingGlyphElement,\n  'glyph': GlyphElement,\n  'text': TextElement,\n  'tspan': TSpanElement,\n  'tref': TRefElement,\n  'a': AElement,\n  'textPath': TextPathElement,\n  'image': ImageElement,\n  'g': GElement,\n  'symbol': SymbolElement,\n  'style': StyleElement,\n  'use': UseElement,\n  'mask': MaskElement,\n  'clipPath': ClipPathElement,\n  'filter': FilterElement,\n  'feDropShadow': FeDropShadowElement,\n  'feMorphology': FeMorphologyElement,\n  'feComposite': FeCompositeElement,\n  'feColorMatrix': FeColorMatrixElement,\n  'feGaussianBlur': FeGaussianBlurElement,\n  'title': TitleElement,\n  'desc': DescElement\n};\nfunction ownKeys$1(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread$1(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys$1(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys$1(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction createCanvas(width, height) {\n  var canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\nfunction createImage(_x) {\n  return _createImage.apply(this, arguments);\n}\nfunction _createImage() {\n  _createImage = _asyncToGenerator(function* (src) {\n    var anonymousCrossOrigin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var image = document.createElement('img');\n    if (anonymousCrossOrigin) {\n      image.crossOrigin = 'Anonymous';\n    }\n    return new Promise((resolve, reject) => {\n      image.onload = () => {\n        resolve(image);\n      };\n      image.onerror = (_event, _source, _lineno, _colno, error) => {\n        reject(error);\n      };\n      image.src = src;\n    });\n  });\n  return _createImage.apply(this, arguments);\n}\nclass Document {\n  constructor(canvg) {\n    var {\n      rootEmSize = 12,\n      emSize = 12,\n      createCanvas = Document.createCanvas,\n      createImage = Document.createImage,\n      anonymousCrossOrigin\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.canvg = canvg;\n    this.definitions = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.images = [];\n    this.fonts = [];\n    this.emSizeStack = [];\n    this.uniqueId = 0;\n    this.screen = canvg.screen;\n    this.rootEmSize = rootEmSize;\n    this.emSize = emSize;\n    this.createCanvas = createCanvas;\n    this.createImage = this.bindCreateImage(createImage, anonymousCrossOrigin);\n    this.screen.wait(this.isImagesLoaded.bind(this));\n    this.screen.wait(this.isFontsLoaded.bind(this));\n  }\n  bindCreateImage(createImage, anonymousCrossOrigin) {\n    if (typeof anonymousCrossOrigin === 'boolean') {\n      return (source, forceAnonymousCrossOrigin) => createImage(source, typeof forceAnonymousCrossOrigin === 'boolean' ? forceAnonymousCrossOrigin : anonymousCrossOrigin);\n    }\n    return createImage;\n  }\n  get window() {\n    return this.screen.window;\n  }\n  get fetch() {\n    return this.screen.fetch;\n  }\n  get ctx() {\n    return this.screen.ctx;\n  }\n  get emSize() {\n    var {\n      emSizeStack\n    } = this;\n    return emSizeStack[emSizeStack.length - 1];\n  }\n  set emSize(value) {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.push(value);\n  }\n  popEmSize() {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.pop();\n  }\n  getUniqueId() {\n    return \"canvg\".concat(++this.uniqueId);\n  }\n  isImagesLoaded() {\n    return this.images.every(_ => _.loaded);\n  }\n  isFontsLoaded() {\n    return this.fonts.every(_ => _.loaded);\n  }\n  createDocumentElement(document) {\n    var documentElement = this.createElement(document.documentElement);\n    documentElement.root = true;\n    documentElement.addStylesFromStyleDefinition();\n    this.documentElement = documentElement;\n    return documentElement;\n  }\n  createElement(node) {\n    var elementType = node.nodeName.replace(/^[^:]+:/, '');\n    var ElementType = Document.elementTypes[elementType];\n    if (typeof ElementType !== 'undefined') {\n      return new ElementType(this, node);\n    }\n    return new UnknownElement(this, node);\n  }\n  createTextNode(node) {\n    return new TextNode(this, node);\n  }\n  setViewBox(config) {\n    this.screen.setViewBox(_objectSpread$1({\n      document: this\n    }, config));\n  }\n}\nDocument.createCanvas = createCanvas;\nDocument.createImage = createImage;\nDocument.elementTypes = elements;\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\n/**\r\n * SVG renderer on canvas.\r\n */\n\nclass Canvg {\n  /**\r\n   * Main constructor.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG Document.\r\n   * @param options - Rendering options.\r\n   */\n  constructor(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.parser = new Parser(options);\n    this.screen = new Screen(ctx, options);\n    this.options = options;\n    var document = new Document(this, options);\n    var documentElement = document.createDocumentElement(svg);\n    this.document = document;\n    this.documentElement = documentElement;\n  }\n  /**\r\n   * Create Canvg instance from SVG source string or URL.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n  static from(ctx, svg) {\n    var _arguments = arguments;\n    return _asyncToGenerator(function* () {\n      var options = _arguments.length > 2 && _arguments[2] !== undefined ? _arguments[2] : {};\n      var parser = new Parser(options);\n      var svgDocument = yield parser.parse(svg);\n      return new Canvg(ctx, svgDocument, options);\n    })();\n  }\n  /**\r\n   * Create Canvg instance from SVG source string.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n  static fromString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var parser = new Parser(options);\n    var svgDocument = parser.parseFromString(svg);\n    return new Canvg(ctx, svgDocument, options);\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n  fork(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.from(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n  forkString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.fromString(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Document is ready promise.\r\n   * @returns Ready promise.\r\n   */\n\n  ready() {\n    return this.screen.ready();\n  }\n  /**\r\n   * Document is ready value.\r\n   * @returns Is ready or not.\r\n   */\n\n  isReady() {\n    return this.screen.isReady();\n  }\n  /**\r\n   * Render only first frame, ignoring animations and mouse.\r\n   * @param options - Rendering options.\r\n   */\n\n  render() {\n    var _arguments2 = arguments,\n      _this = this;\n    return _asyncToGenerator(function* () {\n      var options = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : {};\n      _this.start(_objectSpread({\n        enableRedraw: true,\n        ignoreAnimation: true,\n        ignoreMouse: true\n      }, options));\n      yield _this.ready();\n      _this.stop();\n    })();\n  }\n  /**\r\n   * Start rendering.\r\n   * @param options - Render options.\r\n   */\n\n  start() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var {\n      documentElement,\n      screen,\n      options: baseOptions\n    } = this;\n    screen.start(documentElement, _objectSpread(_objectSpread({\n      enableRedraw: true\n    }, baseOptions), options));\n  }\n  /**\r\n   * Stop rendering.\r\n   */\n\n  stop() {\n    this.screen.stop();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.documentElement.resize(width, height, preserveAspectRatio);\n  }\n}\nexport { AElement, AnimateColorElement, AnimateElement, AnimateTransformElement, BoundingBox, CB1, CB2, CB3, CB4, Canvg, CircleElement, ClipPathElement, DefsElement, DescElement, Document, Element, EllipseElement, FeColorMatrixElement, FeCompositeElement, FeDropShadowElement, FeGaussianBlurElement, FeMorphologyElement, FilterElement, Font, FontElement, FontFaceElement, GElement, GlyphElement, GradientElement, ImageElement, LineElement, LinearGradientElement, MarkerElement, MaskElement, Matrix, MissingGlyphElement, Mouse, PSEUDO_ZERO, Parser, PathElement, PathParser, PatternElement, Point, PolygonElement, PolylineElement, Property, QB1, QB2, QB3, RadialGradientElement, RectElement, RenderedElement, Rotate, SVGElement, SVGFontLoader, Scale, Screen, Skew, SkewX, SkewY, StopElement, StyleElement, SymbolElement, TRefElement, TSpanElement, TextElement, TextPathElement, TitleElement, Transform, Translate, UnknownElement, UseElement, ViewPort, compressSpaces, Canvg as default, getSelectorSpecificity, normalizeAttributeName, normalizeColor, parseExternalUrl, index as presets, toNumbers, trimLeft, trimRight, vectorMagnitude, vectorsAngle, vectorsRatio };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import 'core-js/modules/es.promise.js';\nimport _asyncToGenerator from '@babel/runtime/helpers/asyncToGenerator';\nimport 'core-js/modules/es.string.match.js';\nimport 'core-js/modules/es.string.replace.js';\nimport 'core-js/modules/es.string.starts-with.js';\nimport 'core-js/modules/es.array.iterator.js';\nimport 'core-js/modules/web.dom-collections.iterator.js';\nimport _defineProperty from '@babel/runtime/helpers/defineProperty';\nimport 'core-js/modules/es.array.reduce.js';\nimport 'core-js/modules/es.string.ends-with.js';\nimport 'core-js/modules/es.string.split.js';\nimport requestAnimationFrame from 'raf';\nimport 'core-js/modules/es.string.trim.js';\nimport RGBColor from 'rgbcolor';\nimport 'core-js/modules/es.array.index-of.js';\nimport 'core-js/modules/es.string.includes.js';\nimport 'core-js/modules/es.array.reverse.js';\nimport { SVGPathData } from 'svg-pathdata';\nimport 'core-js/modules/es.regexp.to-string.js';\nimport { canvasRGBA } from 'stackblur-canvas';\n\n/**\r\n * Options preset for `OffscreenCanvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @returns Preset object.\r\n */\nfunction offscreen() {\n  var {\n    DOMParser: DOMParserFallback\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var preset = {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser: DOMParserFallback,\n\n    createCanvas(width, height) {\n      return new OffscreenCanvas(width, height);\n    },\n\n    createImage(url) {\n      return _asyncToGenerator(function* () {\n        var response = yield fetch(url);\n        var blob = yield response.blob();\n        var img = yield createImageBitmap(blob);\n        return img;\n      })();\n    }\n\n  };\n\n  if (typeof DOMParser !== 'undefined' || typeof DOMParserFallback === 'undefined') {\n    Reflect.deleteProperty(preset, 'DOMParser');\n  }\n\n  return preset;\n}\n\n/**\r\n * Options preset for `node-canvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @param config.canvas - `node-canvas` exports.\r\n * @param config.fetch - WHATWG-compatible `fetch` function.\r\n * @returns Preset object.\r\n */\nfunction node(_ref) {\n  var {\n    DOMParser,\n    canvas,\n    fetch\n  } = _ref;\n  return {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser,\n    fetch,\n    createCanvas: canvas.createCanvas,\n    createImage: canvas.loadImage\n  };\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\toffscreen: offscreen,\n\tnode: node\n});\n\n/**\r\n * HTML-safe compress white-spaces.\r\n * @param str - String to compress.\r\n * @returns String.\r\n */\nfunction compressSpaces(str) {\n  return str.replace(/(?!\\u3000)\\s+/gm, ' ');\n}\n/**\r\n * HTML-safe left trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimLeft(str) {\n  return str.replace(/^[\\n \\t]+/, '');\n}\n/**\r\n * HTML-safe right trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimRight(str) {\n  return str.replace(/[\\n \\t]+$/, '');\n}\n/**\r\n * String to numbers array.\r\n * @param str - Numbers string.\r\n * @returns Numbers array.\r\n */\n\nfunction toNumbers(str) {\n  var matches = (str || '').match(/-?(\\d+(?:\\.\\d*(?:[eE][+-]?\\d+)?)?|\\.\\d+)(?=\\D|$)/gm) || [];\n  return matches.map(parseFloat);\n} // Microsoft Edge fix\n\nvar allUppercase = /^[A-Z-]+$/;\n/**\r\n * Normalize attribute name.\r\n * @param name - Attribute name.\r\n * @returns Normalized attribute name.\r\n */\n\nfunction normalizeAttributeName(name) {\n  if (allUppercase.test(name)) {\n    return name.toLowerCase();\n  }\n\n  return name;\n}\n/**\r\n * Parse external URL.\r\n * @param url - CSS url string.\r\n * @returns Parsed URL.\r\n */\n\nfunction parseExternalUrl(url) {\n  //                      single quotes [2]\n  //                      v         double quotes [3]\n  //                      v         v         no quotes [4]\n  //                      v         v         v\n  var urlMatch = /url\\(('([^']+)'|\"([^\"]+)\"|([^'\")]+))\\)/.exec(url) || [];\n  return urlMatch[2] || urlMatch[3] || urlMatch[4];\n}\n/**\r\n * Transform floats to integers in rgb colors.\r\n * @param color - Color to normalize.\r\n * @returns Normalized color.\r\n */\n\nfunction normalizeColor(color) {\n  if (!color.startsWith('rgb')) {\n    return color;\n  }\n\n  var rgbParts = 3;\n  var normalizedColor = color.replace(/\\d+(\\.\\d+)?/g, (num, isFloat) => rgbParts-- && isFloat ? String(Math.round(parseFloat(num))) : num);\n  return normalizedColor;\n}\n\n// slightly modified version of https://github.com/keeganstreet/specificity/blob/master/specificity.js\nvar attributeRegex = /(\\[[^\\]]+\\])/g;\nvar idRegex = /(#[^\\s+>~.[:]+)/g;\nvar classRegex = /(\\.[^\\s+>~.[:]+)/g;\nvar pseudoElementRegex = /(::[^\\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi;\nvar pseudoClassWithBracketsRegex = /(:[\\w-]+\\([^)]*\\))/gi;\nvar pseudoClassRegex = /(:[^\\s+>~.[:]+)/g;\nvar elementRegex = /([^\\s+>~.[:]+)/g;\n\nfunction findSelectorMatch(selector, regex) {\n  var matches = regex.exec(selector);\n\n  if (!matches) {\n    return [selector, 0];\n  }\n\n  return [selector.replace(regex, ' '), matches.length];\n}\n/**\r\n * Measure selector specificity.\r\n * @param selector - Selector to measure.\r\n * @returns Specificity.\r\n */\n\n\nfunction getSelectorSpecificity(selector) {\n  var specificity = [0, 0, 0];\n  var currentSelector = selector.replace(/:not\\(([^)]*)\\)/g, '     $1 ').replace(/{[\\s\\S]*/gm, ' ');\n  var delta = 0;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, attributeRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, idRegex);\n  specificity[0] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, classRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoElementRegex);\n  specificity[2] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassWithBracketsRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassRegex);\n  specificity[1] += delta;\n  currentSelector = currentSelector.replace(/[*\\s+>~]/g, ' ').replace(/[#.]/g, ' ');\n  [currentSelector, delta] = findSelectorMatch(currentSelector, elementRegex); // lgtm [js/useless-assignment-to-local]\n\n  specificity[2] += delta;\n  return specificity.join('');\n}\n\nvar PSEUDO_ZERO = .00000001;\n/**\r\n * Vector magnitude.\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorMagnitude(v) {\n  return Math.sqrt(Math.pow(v[0], 2) + Math.pow(v[1], 2));\n}\n/**\r\n * Ratio between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsRatio(u, v) {\n  return (u[0] * v[0] + u[1] * v[1]) / (vectorMagnitude(u) * vectorMagnitude(v));\n}\n/**\r\n * Angle between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsAngle(u, v) {\n  return (u[0] * v[1] < u[1] * v[0] ? -1 : 1) * Math.acos(vectorsRatio(u, v));\n}\nfunction CB1(t) {\n  return t * t * t;\n}\nfunction CB2(t) {\n  return 3 * t * t * (1 - t);\n}\nfunction CB3(t) {\n  return 3 * t * (1 - t) * (1 - t);\n}\nfunction CB4(t) {\n  return (1 - t) * (1 - t) * (1 - t);\n}\nfunction QB1(t) {\n  return t * t;\n}\nfunction QB2(t) {\n  return 2 * t * (1 - t);\n}\nfunction QB3(t) {\n  return (1 - t) * (1 - t);\n}\n\nclass Property {\n  constructor(document, name, value) {\n    this.document = document;\n    this.name = name;\n    this.value = value;\n    this.isNormalizedColor = false;\n  }\n\n  static empty(document) {\n    return new Property(document, 'EMPTY', '');\n  }\n\n  split() {\n    var separator = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ' ';\n    var {\n      document,\n      name\n    } = this;\n    return compressSpaces(this.getString()).trim().split(separator).map(value => new Property(document, name, value));\n  }\n\n  hasValue(zeroIsValue) {\n    var {\n      value\n    } = this;\n    return value !== null && value !== '' && (zeroIsValue || value !== 0) && typeof value !== 'undefined';\n  }\n\n  isString(regexp) {\n    var {\n      value\n    } = this;\n    var result = typeof value === 'string';\n\n    if (!result || !regexp) {\n      return result;\n    }\n\n    return regexp.test(value);\n  }\n\n  isUrlDefinition() {\n    return this.isString(/^url\\(/);\n  }\n\n  isPixels() {\n    if (!this.hasValue()) {\n      return false;\n    }\n\n    var asString = this.getString();\n\n    switch (true) {\n      case asString.endsWith('px'):\n      case /^[0-9]+$/.test(asString):\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  setValue(value) {\n    this.value = value;\n    return this;\n  }\n\n  getValue(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return this.value;\n    }\n\n    return def;\n  }\n\n  getNumber(def) {\n    if (!this.hasValue()) {\n      if (typeof def === 'undefined') {\n        return 0;\n      }\n\n      return parseFloat(def);\n    }\n\n    var {\n      value\n    } = this;\n    var n = parseFloat(value);\n\n    if (this.isString(/%$/)) {\n      n /= 100.0;\n    }\n\n    return n;\n  }\n\n  getString(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return typeof this.value === 'undefined' ? '' : String(this.value);\n    }\n\n    return String(def);\n  }\n\n  getColor(def) {\n    var color = this.getString(def);\n\n    if (this.isNormalizedColor) {\n      return color;\n    }\n\n    this.isNormalizedColor = true;\n    color = normalizeColor(color);\n    this.value = color;\n    return color;\n  }\n\n  getDpi() {\n    return 96.0; // TODO: compute?\n  }\n\n  getRem() {\n    return this.document.rootEmSize;\n  }\n\n  getEm() {\n    return this.document.emSize;\n  }\n\n  getUnits() {\n    return this.getString().replace(/[0-9.-]/g, '');\n  }\n\n  getPixels(axisOrIsFontSize) {\n    var processPercent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    var [axis, isFontSize] = typeof axisOrIsFontSize === 'boolean' ? [undefined, axisOrIsFontSize] : [axisOrIsFontSize];\n    var {\n      viewPort\n    } = this.document.screen;\n\n    switch (true) {\n      case this.isString(/vmin$/):\n        return this.getNumber() / 100.0 * Math.min(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vmax$/):\n        return this.getNumber() / 100.0 * Math.max(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vw$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('x');\n\n      case this.isString(/vh$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('y');\n\n      case this.isString(/rem$/):\n        return this.getNumber() * this.getRem();\n\n      case this.isString(/em$/):\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/ex$/):\n        return this.getNumber() * this.getEm() / 2.0;\n\n      case this.isString(/px$/):\n        return this.getNumber();\n\n      case this.isString(/pt$/):\n        return this.getNumber() * this.getDpi() * (1.0 / 72.0);\n\n      case this.isString(/pc$/):\n        return this.getNumber() * 15;\n\n      case this.isString(/cm$/):\n        return this.getNumber() * this.getDpi() / 2.54;\n\n      case this.isString(/mm$/):\n        return this.getNumber() * this.getDpi() / 25.4;\n\n      case this.isString(/in$/):\n        return this.getNumber() * this.getDpi();\n\n      case this.isString(/%$/) && isFontSize:\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/%$/):\n        return this.getNumber() * viewPort.computeSize(axis);\n\n      default:\n        {\n          var n = this.getNumber();\n\n          if (processPercent && n < 1.0) {\n            return n * viewPort.computeSize(axis);\n          }\n\n          return n;\n        }\n    }\n  }\n\n  getMilliseconds() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    if (this.isString(/ms$/)) {\n      return this.getNumber();\n    }\n\n    return this.getNumber() * 1000;\n  }\n\n  getRadians() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    switch (true) {\n      case this.isString(/deg$/):\n        return this.getNumber() * (Math.PI / 180.0);\n\n      case this.isString(/grad$/):\n        return this.getNumber() * (Math.PI / 200.0);\n\n      case this.isString(/rad$/):\n        return this.getNumber();\n\n      default:\n        return this.getNumber() * (Math.PI / 180.0);\n    }\n  }\n\n  getDefinition() {\n    var asString = this.getString();\n    var name = /#([^)'\"]+)/.exec(asString);\n\n    if (name) {\n      name = name[1];\n    }\n\n    if (!name) {\n      name = asString;\n    }\n\n    return this.document.definitions[name];\n  }\n\n  getFillStyleDefinition(element, opacity) {\n    var def = this.getDefinition();\n\n    if (!def) {\n      return null;\n    } // gradient\n\n\n    if (typeof def.createGradient === 'function') {\n      return def.createGradient(this.document.ctx, element, opacity);\n    } // pattern\n\n\n    if (typeof def.createPattern === 'function') {\n      if (def.getHrefAttribute().hasValue()) {\n        var patternTransform = def.getAttribute('patternTransform');\n        def = def.getHrefAttribute().getDefinition();\n\n        if (patternTransform.hasValue()) {\n          def.getAttribute('patternTransform', true).setValue(patternTransform.value);\n        }\n      }\n\n      return def.createPattern(this.document.ctx, element, opacity);\n    }\n\n    return null;\n  }\n\n  getTextBaseline() {\n    if (!this.hasValue()) {\n      return null;\n    }\n\n    return Property.textBaselineMapping[this.getString()];\n  }\n\n  addOpacity(opacity) {\n    var value = this.getColor();\n    var len = value.length;\n    var commas = 0; // Simulate old RGBColor version, which can't parse rgba.\n\n    for (var i = 0; i < len; i++) {\n      if (value[i] === ',') {\n        commas++;\n      }\n\n      if (commas === 3) {\n        break;\n      }\n    }\n\n    if (opacity.hasValue() && this.isString() && commas !== 3) {\n      var color = new RGBColor(value);\n\n      if (color.ok) {\n        color.alpha = opacity.getNumber();\n        value = color.toRGBA();\n      }\n    }\n\n    return new Property(this.document, this.name, value);\n  }\n\n}\nProperty.textBaselineMapping = {\n  'baseline': 'alphabetic',\n  'before-edge': 'top',\n  'text-before-edge': 'top',\n  'middle': 'middle',\n  'central': 'middle',\n  'after-edge': 'bottom',\n  'text-after-edge': 'bottom',\n  'ideographic': 'ideographic',\n  'alphabetic': 'alphabetic',\n  'hanging': 'hanging',\n  'mathematical': 'alphabetic'\n};\n\nclass ViewPort {\n  constructor() {\n    this.viewPorts = [];\n  }\n\n  clear() {\n    this.viewPorts = [];\n  }\n\n  setCurrent(width, height) {\n    this.viewPorts.push({\n      width,\n      height\n    });\n  }\n\n  removeCurrent() {\n    this.viewPorts.pop();\n  }\n\n  getCurrent() {\n    var {\n      viewPorts\n    } = this;\n    return viewPorts[viewPorts.length - 1];\n  }\n\n  get width() {\n    return this.getCurrent().width;\n  }\n\n  get height() {\n    return this.getCurrent().height;\n  }\n\n  computeSize(d) {\n    if (typeof d === 'number') {\n      return d;\n    }\n\n    if (d === 'x') {\n      return this.width;\n    }\n\n    if (d === 'y') {\n      return this.height;\n    }\n\n    return Math.sqrt(Math.pow(this.width, 2) + Math.pow(this.height, 2)) / Math.sqrt(2);\n  }\n\n}\n\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n\n  static parse(point) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var [x = defaultValue, y = defaultValue] = toNumbers(point);\n    return new Point(x, y);\n  }\n\n  static parseScale(scale) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var [x = defaultValue, y = x] = toNumbers(scale);\n    return new Point(x, y);\n  }\n\n  static parsePath(path) {\n    var points = toNumbers(path);\n    var len = points.length;\n    var pathPoints = [];\n\n    for (var i = 0; i < len; i += 2) {\n      pathPoints.push(new Point(points[i], points[i + 1]));\n    }\n\n    return pathPoints;\n  }\n\n  angleTo(point) {\n    return Math.atan2(point.y - this.y, point.x - this.x);\n  }\n\n  applyTransform(transform) {\n    var {\n      x,\n      y\n    } = this;\n    var xp = x * transform[0] + y * transform[2] + transform[4];\n    var yp = x * transform[1] + y * transform[3] + transform[5];\n    this.x = xp;\n    this.y = yp;\n  }\n\n}\n\nclass Mouse {\n  constructor(screen) {\n    this.screen = screen;\n    this.working = false;\n    this.events = [];\n    this.eventElements = []; // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onClick = this.onClick.bind(this); // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onMouseMove = this.onMouseMove.bind(this);\n  }\n\n  isWorking() {\n    return this.working;\n  }\n\n  start() {\n    if (this.working) {\n      return;\n    }\n\n    var {\n      screen,\n      onClick,\n      onMouseMove\n    } = this;\n    var canvas = screen.ctx.canvas;\n    canvas.onclick = onClick;\n    canvas.onmousemove = onMouseMove;\n    this.working = true;\n  }\n\n  stop() {\n    if (!this.working) {\n      return;\n    }\n\n    var canvas = this.screen.ctx.canvas;\n    this.working = false;\n    canvas.onclick = null;\n    canvas.onmousemove = null;\n  }\n\n  hasEvents() {\n    return this.working && this.events.length > 0;\n  }\n\n  runEvents() {\n    if (!this.working) {\n      return;\n    }\n\n    var {\n      screen: document,\n      events,\n      eventElements\n    } = this;\n    var {\n      style\n    } = document.ctx.canvas;\n\n    if (style) {\n      style.cursor = '';\n    }\n\n    events.forEach((_ref, i) => {\n      var {\n        run\n      } = _ref;\n      var element = eventElements[i];\n\n      while (element) {\n        run(element);\n        element = element.parent;\n      }\n    }); // done running, clear\n\n    this.events = [];\n    this.eventElements = [];\n  }\n\n  checkPath(element, ctx) {\n    if (!this.working || !ctx) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref2, i) => {\n      var {\n        x,\n        y\n      } = _ref2;\n\n      if (!eventElements[i] && ctx.isPointInPath && ctx.isPointInPath(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  checkBoundingBox(element, boundingBox) {\n    if (!this.working || !boundingBox) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref3, i) => {\n      var {\n        x,\n        y\n      } = _ref3;\n\n      if (!eventElements[i] && boundingBox.isPointInBox(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  mapXY(x, y) {\n    var {\n      window,\n      ctx\n    } = this.screen;\n    var point = new Point(x, y);\n    var element = ctx.canvas;\n\n    while (element) {\n      point.x -= element.offsetLeft;\n      point.y -= element.offsetTop;\n      element = element.offsetParent;\n    }\n\n    if (window.scrollX) {\n      point.x += window.scrollX;\n    }\n\n    if (window.scrollY) {\n      point.y += window.scrollY;\n    }\n\n    return point;\n  }\n\n  onClick(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onclick',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onClick) {\n          eventTarget.onClick();\n        }\n      }\n\n    });\n  }\n\n  onMouseMove(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onmousemove',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onMouseMove) {\n          eventTarget.onMouseMove();\n        }\n      }\n\n    });\n  }\n\n}\n\nvar defaultWindow = typeof window !== 'undefined' ? window : null;\nvar defaultFetch$1 = typeof fetch !== 'undefined' ? fetch.bind(undefined) // `fetch` depends on context: `someObject.fetch(...)` will throw error.\n: null;\nclass Screen {\n  constructor(ctx) {\n    var {\n      fetch = defaultFetch$1,\n      window = defaultWindow\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.ctx = ctx;\n    this.FRAMERATE = 30;\n    this.MAX_VIRTUAL_PIXELS = 30000;\n    this.CLIENT_WIDTH = 800;\n    this.CLIENT_HEIGHT = 600;\n    this.viewPort = new ViewPort();\n    this.mouse = new Mouse(this);\n    this.animations = [];\n    this.waits = [];\n    this.frameDuration = 0;\n    this.isReadyLock = false;\n    this.isFirstRender = true;\n    this.intervalId = null;\n    this.window = window;\n    this.fetch = fetch;\n  }\n\n  wait(checker) {\n    this.waits.push(checker);\n  }\n\n  ready() {\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    if (!this.readyPromise) {\n      return Promise.resolve();\n    }\n\n    return this.readyPromise;\n  }\n\n  isReady() {\n    if (this.isReadyLock) {\n      return true;\n    }\n\n    var isReadyLock = this.waits.every(_ => _());\n\n    if (isReadyLock) {\n      this.waits = [];\n\n      if (this.resolveReady) {\n        this.resolveReady();\n      }\n    }\n\n    this.isReadyLock = isReadyLock;\n    return isReadyLock;\n  }\n\n  setDefaults(ctx) {\n    // initial values and defaults\n    ctx.strokeStyle = 'rgba(0,0,0,0)';\n    ctx.lineCap = 'butt';\n    ctx.lineJoin = 'miter';\n    ctx.miterLimit = 4;\n  }\n\n  setViewBox(_ref) {\n    var {\n      document,\n      ctx,\n      aspectRatio,\n      width,\n      desiredWidth,\n      height,\n      desiredHeight,\n      minX = 0,\n      minY = 0,\n      refX,\n      refY,\n      clip = false,\n      clipX = 0,\n      clipY = 0\n    } = _ref;\n    // aspect ratio - http://www.w3.org/TR/SVG/coords.html#PreserveAspectRatioAttribute\n    var cleanAspectRatio = compressSpaces(aspectRatio).replace(/^defer\\s/, ''); // ignore defer\n\n    var [aspectRatioAlign, aspectRatioMeetOrSlice] = cleanAspectRatio.split(' ');\n    var align = aspectRatioAlign || 'xMidYMid';\n    var meetOrSlice = aspectRatioMeetOrSlice || 'meet'; // calculate scale\n\n    var scaleX = width / desiredWidth;\n    var scaleY = height / desiredHeight;\n    var scaleMin = Math.min(scaleX, scaleY);\n    var scaleMax = Math.max(scaleX, scaleY);\n    var finalDesiredWidth = desiredWidth;\n    var finalDesiredHeight = desiredHeight;\n\n    if (meetOrSlice === 'meet') {\n      finalDesiredWidth *= scaleMin;\n      finalDesiredHeight *= scaleMin;\n    }\n\n    if (meetOrSlice === 'slice') {\n      finalDesiredWidth *= scaleMax;\n      finalDesiredHeight *= scaleMax;\n    }\n\n    var refXProp = new Property(document, 'refX', refX);\n    var refYProp = new Property(document, 'refY', refY);\n    var hasRefs = refXProp.hasValue() && refYProp.hasValue();\n\n    if (hasRefs) {\n      ctx.translate(-scaleMin * refXProp.getPixels('x'), -scaleMin * refYProp.getPixels('y'));\n    }\n\n    if (clip) {\n      var scaledClipX = scaleMin * clipX;\n      var scaledClipY = scaleMin * clipY;\n      ctx.beginPath();\n      ctx.moveTo(scaledClipX, scaledClipY);\n      ctx.lineTo(width, scaledClipY);\n      ctx.lineTo(width, height);\n      ctx.lineTo(scaledClipX, height);\n      ctx.closePath();\n      ctx.clip();\n    }\n\n    if (!hasRefs) {\n      var isMeetMinY = meetOrSlice === 'meet' && scaleMin === scaleY;\n      var isSliceMaxY = meetOrSlice === 'slice' && scaleMax === scaleY;\n      var isMeetMinX = meetOrSlice === 'meet' && scaleMin === scaleX;\n      var isSliceMaxX = meetOrSlice === 'slice' && scaleMax === scaleX;\n\n      if (align.startsWith('xMid') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width / 2.0 - finalDesiredWidth / 2.0, 0);\n      }\n\n      if (align.endsWith('YMid') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height / 2.0 - finalDesiredHeight / 2.0);\n      }\n\n      if (align.startsWith('xMax') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width - finalDesiredWidth, 0);\n      }\n\n      if (align.endsWith('YMax') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height - finalDesiredHeight);\n      }\n    } // scale\n\n\n    switch (true) {\n      case align === 'none':\n        ctx.scale(scaleX, scaleY);\n        break;\n\n      case meetOrSlice === 'meet':\n        ctx.scale(scaleMin, scaleMin);\n        break;\n\n      case meetOrSlice === 'slice':\n        ctx.scale(scaleMax, scaleMax);\n        break;\n    } // translate\n\n\n    ctx.translate(-minX, -minY);\n  }\n\n  start(element) {\n    var {\n      enableRedraw = false,\n      ignoreMouse = false,\n      ignoreAnimation = false,\n      ignoreDimensions = false,\n      ignoreClear = false,\n      forceRedraw,\n      scaleWidth,\n      scaleHeight,\n      offsetX,\n      offsetY\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var {\n      FRAMERATE,\n      mouse\n    } = this;\n    var frameDuration = 1000 / FRAMERATE;\n    this.frameDuration = frameDuration;\n    this.readyPromise = new Promise(resolve => {\n      this.resolveReady = resolve;\n    });\n\n    if (this.isReady()) {\n      this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n    }\n\n    if (!enableRedraw) {\n      return;\n    }\n\n    var now = Date.now();\n    var then = now;\n    var delta = 0;\n\n    var tick = () => {\n      now = Date.now();\n      delta = now - then;\n\n      if (delta >= frameDuration) {\n        then = now - delta % frameDuration;\n\n        if (this.shouldUpdate(ignoreAnimation, forceRedraw)) {\n          this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n          mouse.runEvents();\n        }\n      }\n\n      this.intervalId = requestAnimationFrame(tick);\n    };\n\n    if (!ignoreMouse) {\n      mouse.start();\n    }\n\n    this.intervalId = requestAnimationFrame(tick);\n  }\n\n  stop() {\n    if (this.intervalId) {\n      requestAnimationFrame.cancel(this.intervalId);\n      this.intervalId = null;\n    }\n\n    this.mouse.stop();\n  }\n\n  shouldUpdate(ignoreAnimation, forceRedraw) {\n    // need update from animations?\n    if (!ignoreAnimation) {\n      var {\n        frameDuration\n      } = this;\n      var shouldUpdate = this.animations.reduce((shouldUpdate, animation) => animation.update(frameDuration) || shouldUpdate, false);\n\n      if (shouldUpdate) {\n        return true;\n      }\n    } // need update from redraw?\n\n\n    if (typeof forceRedraw === 'function' && forceRedraw()) {\n      return true;\n    }\n\n    if (!this.isReadyLock && this.isReady()) {\n      return true;\n    } // need update from mouse events?\n\n\n    if (this.mouse.hasEvents()) {\n      return true;\n    }\n\n    return false;\n  }\n\n  render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY) {\n    var {\n      CLIENT_WIDTH,\n      CLIENT_HEIGHT,\n      viewPort,\n      ctx,\n      isFirstRender\n    } = this;\n    var canvas = ctx.canvas;\n    viewPort.clear();\n\n    if (canvas.width && canvas.height) {\n      viewPort.setCurrent(canvas.width, canvas.height);\n    } else {\n      viewPort.setCurrent(CLIENT_WIDTH, CLIENT_HEIGHT);\n    }\n\n    var widthStyle = element.getStyle('width');\n    var heightStyle = element.getStyle('height');\n\n    if (!ignoreDimensions && (isFirstRender || typeof scaleWidth !== 'number' && typeof scaleHeight !== 'number')) {\n      // set canvas size\n      if (widthStyle.hasValue()) {\n        canvas.width = widthStyle.getPixels('x');\n\n        if (canvas.style) {\n          canvas.style.width = \"\".concat(canvas.width, \"px\");\n        }\n      }\n\n      if (heightStyle.hasValue()) {\n        canvas.height = heightStyle.getPixels('y');\n\n        if (canvas.style) {\n          canvas.style.height = \"\".concat(canvas.height, \"px\");\n        }\n      }\n    }\n\n    var cWidth = canvas.clientWidth || canvas.width;\n    var cHeight = canvas.clientHeight || canvas.height;\n\n    if (ignoreDimensions && widthStyle.hasValue() && heightStyle.hasValue()) {\n      cWidth = widthStyle.getPixels('x');\n      cHeight = heightStyle.getPixels('y');\n    }\n\n    viewPort.setCurrent(cWidth, cHeight);\n\n    if (typeof offsetX === 'number') {\n      element.getAttribute('x', true).setValue(offsetX);\n    }\n\n    if (typeof offsetY === 'number') {\n      element.getAttribute('y', true).setValue(offsetY);\n    }\n\n    if (typeof scaleWidth === 'number' || typeof scaleHeight === 'number') {\n      var viewBox = toNumbers(element.getAttribute('viewBox').getString());\n      var xRatio = 0;\n      var yRatio = 0;\n\n      if (typeof scaleWidth === 'number') {\n        var _widthStyle = element.getStyle('width');\n\n        if (_widthStyle.hasValue()) {\n          xRatio = _widthStyle.getPixels('x') / scaleWidth;\n        } else if (!isNaN(viewBox[2])) {\n          xRatio = viewBox[2] / scaleWidth;\n        }\n      }\n\n      if (typeof scaleHeight === 'number') {\n        var _heightStyle = element.getStyle('height');\n\n        if (_heightStyle.hasValue()) {\n          yRatio = _heightStyle.getPixels('y') / scaleHeight;\n        } else if (!isNaN(viewBox[3])) {\n          yRatio = viewBox[3] / scaleHeight;\n        }\n      }\n\n      if (!xRatio) {\n        xRatio = yRatio;\n      }\n\n      if (!yRatio) {\n        yRatio = xRatio;\n      }\n\n      element.getAttribute('width', true).setValue(scaleWidth);\n      element.getAttribute('height', true).setValue(scaleHeight);\n      var transformStyle = element.getStyle('transform', true, true);\n      transformStyle.setValue(\"\".concat(transformStyle.getString(), \" scale(\").concat(1.0 / xRatio, \", \").concat(1.0 / yRatio, \")\"));\n    } // clear and render\n\n\n    if (!ignoreClear) {\n      ctx.clearRect(0, 0, cWidth, cHeight);\n    }\n\n    element.render(ctx);\n\n    if (isFirstRender) {\n      this.isFirstRender = false;\n    }\n  }\n\n}\nScreen.defaultWindow = defaultWindow;\nScreen.defaultFetch = defaultFetch$1;\n\nvar {\n  defaultFetch\n} = Screen;\nvar DefaultDOMParser = typeof DOMParser !== 'undefined' ? DOMParser : null;\nclass Parser {\n  constructor() {\n    var {\n      fetch = defaultFetch,\n      DOMParser = DefaultDOMParser\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.fetch = fetch;\n    this.DOMParser = DOMParser;\n  }\n\n  parse(resource) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (resource.startsWith('<')) {\n        return _this.parseFromString(resource);\n      }\n\n      return _this.load(resource);\n    })();\n  }\n\n  parseFromString(xml) {\n    var parser = new this.DOMParser();\n\n    try {\n      return this.checkDocument(parser.parseFromString(xml, 'image/svg+xml'));\n    } catch (err) {\n      return this.checkDocument(parser.parseFromString(xml, 'text/xml'));\n    }\n  }\n\n  checkDocument(document) {\n    var parserError = document.getElementsByTagName('parsererror')[0];\n\n    if (parserError) {\n      throw new Error(parserError.textContent);\n    }\n\n    return document;\n  }\n\n  load(url) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var response = yield _this2.fetch(url);\n      var xml = yield response.text();\n      return _this2.parseFromString(xml);\n    })();\n  }\n\n}\n\nclass Translate {\n  constructor(_, point) {\n    this.type = 'translate';\n    this.point = null;\n    this.point = Point.parse(point);\n  }\n\n  apply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(x || 0.0, y || 0.0);\n  }\n\n  unapply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(-1.0 * x || 0.0, -1.0 * y || 0.0);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.point;\n    point.applyTransform([1, 0, 0, 1, x || 0.0, y || 0.0]);\n  }\n\n}\n\nclass Rotate {\n  constructor(document, rotate, transformOrigin) {\n    this.type = 'rotate';\n    this.angle = null;\n    this.originX = null;\n    this.originY = null;\n    this.cx = 0;\n    this.cy = 0;\n    var numbers = toNumbers(rotate);\n    this.angle = new Property(document, 'angle', numbers[0]);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n    this.cx = numbers[1] || 0;\n    this.cy = numbers[2] || 0;\n  }\n\n  apply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(-1.0 * angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      cx,\n      cy,\n      angle\n    } = this;\n    var rad = angle.getRadians();\n    point.applyTransform([1, 0, 0, 1, cx || 0.0, cy || 0.0 // this.p.y\n    ]);\n    point.applyTransform([Math.cos(rad), Math.sin(rad), -Math.sin(rad), Math.cos(rad), 0, 0]);\n    point.applyTransform([1, 0, 0, 1, -cx || 0.0, -cy || 0.0 // -this.p.y\n    ]);\n  }\n\n}\n\nclass Scale {\n  constructor(_, scale, transformOrigin) {\n    this.type = 'scale';\n    this.scale = null;\n    this.originX = null;\n    this.originY = null;\n    var scaleSize = Point.parseScale(scale); // Workaround for node-canvas\n\n    if (scaleSize.x === 0 || scaleSize.y === 0) {\n      scaleSize.x = PSEUDO_ZERO;\n      scaleSize.y = PSEUDO_ZERO;\n    }\n\n    this.scale = scaleSize;\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(x, y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(1.0 / x, 1.0 / y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.scale;\n    point.applyTransform([x || 0.0, 0, 0, y || 0.0, 0, 0]);\n  }\n\n}\n\nclass Matrix {\n  constructor(_, matrix, transformOrigin) {\n    this.type = 'matrix';\n    this.matrix = [];\n    this.originX = null;\n    this.originY = null;\n    this.matrix = toNumbers(matrix);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var a = matrix[0];\n    var b = matrix[2];\n    var c = matrix[4];\n    var d = matrix[1];\n    var e = matrix[3];\n    var f = matrix[5];\n    var g = 0.0;\n    var h = 0.0;\n    var i = 1.0;\n    var det = 1 / (a * (e * i - f * h) - b * (d * i - f * g) + c * (d * h - e * g));\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(det * (e * i - f * h), det * (f * g - d * i), det * (c * h - b * i), det * (a * i - c * g), det * (b * f - c * e), det * (c * d - a * f));\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    point.applyTransform(this.matrix);\n  }\n\n}\n\nclass Skew extends Matrix {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skew';\n    this.angle = null;\n    this.angle = new Property(document, 'angle', skew);\n  }\n\n}\n\nclass SkewX extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewX';\n    this.matrix = [1, 0, Math.tan(this.angle.getRadians()), 1, 0, 0];\n  }\n\n}\n\nclass SkewY extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewY';\n    this.matrix = [1, Math.tan(this.angle.getRadians()), 0, 1, 0, 0];\n  }\n\n}\n\nfunction parseTransforms(transform) {\n  return compressSpaces(transform).trim().replace(/\\)([a-zA-Z])/g, ') $1').replace(/\\)(\\s?,\\s?)/g, ') ').split(/\\s(?=[a-z])/);\n}\n\nfunction parseTransform(transform) {\n  var [type, value] = transform.split('(');\n  return [type.trim(), value.trim().replace(')', '')];\n}\n\nclass Transform {\n  constructor(document, transform, transformOrigin) {\n    this.document = document;\n    this.transforms = [];\n    var data = parseTransforms(transform);\n    data.forEach(transform => {\n      if (transform === 'none') {\n        return;\n      }\n\n      var [type, value] = parseTransform(transform);\n      var TransformType = Transform.transformTypes[type];\n\n      if (typeof TransformType !== 'undefined') {\n        this.transforms.push(new TransformType(this.document, value, transformOrigin));\n      }\n    });\n  }\n\n  static fromElement(document, element) {\n    var transformStyle = element.getStyle('transform', false, true);\n    var [transformOriginXProperty, transformOriginYProperty = transformOriginXProperty] = element.getStyle('transform-origin', false, true).split();\n    var transformOrigin = [transformOriginXProperty, transformOriginYProperty];\n\n    if (transformStyle.hasValue()) {\n      return new Transform(document, transformStyle.getString(), transformOrigin);\n    }\n\n    return null;\n  }\n\n  apply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].apply(ctx);\n    }\n  }\n\n  unapply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = len - 1; i >= 0; i--) {\n      transforms[i].unapply(ctx);\n    }\n  } // TODO: applyToPoint unused ... remove?\n\n\n  applyToPoint(point) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].applyToPoint(point);\n    }\n  }\n\n}\nTransform.transformTypes = {\n  translate: Translate,\n  rotate: Rotate,\n  scale: Scale,\n  matrix: Matrix,\n  skewX: SkewX,\n  skewY: SkewY\n};\n\nclass Element {\n  constructor(document, node) {\n    var captureTextNodes = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.document = document;\n    this.node = node;\n    this.captureTextNodes = captureTextNodes;\n    this.attributes = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.animationFrozen = false;\n    this.animationFrozenValue = '';\n    this.parent = null;\n    this.children = [];\n\n    if (!node || node.nodeType !== 1) {\n      // ELEMENT_NODE\n      return;\n    } // add attributes\n\n\n    Array.from(node.attributes).forEach(attribute => {\n      var nodeName = normalizeAttributeName(attribute.nodeName);\n      this.attributes[nodeName] = new Property(document, nodeName, attribute.value);\n    });\n    this.addStylesFromStyleDefinition(); // add inline styles\n\n    if (this.getAttribute('style').hasValue()) {\n      var styles = this.getAttribute('style').getString().split(';').map(_ => _.trim());\n      styles.forEach(style => {\n        if (!style) {\n          return;\n        }\n\n        var [name, value] = style.split(':').map(_ => _.trim());\n        this.styles[name] = new Property(document, name, value);\n      });\n    }\n\n    var {\n      definitions\n    } = document;\n    var id = this.getAttribute('id'); // add id\n\n    if (id.hasValue()) {\n      if (!definitions[id.getString()]) {\n        definitions[id.getString()] = this;\n      }\n    }\n\n    Array.from(node.childNodes).forEach(childNode => {\n      if (childNode.nodeType === 1) {\n        this.addChild(childNode); // ELEMENT_NODE\n      } else if (captureTextNodes && (childNode.nodeType === 3 || childNode.nodeType === 4)) {\n        var textNode = document.createTextNode(childNode);\n\n        if (textNode.getText().length > 0) {\n          this.addChild(textNode); // TEXT_NODE\n        }\n      }\n    });\n  }\n\n  getAttribute(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var attr = this.attributes[name];\n\n    if (!attr && createIfNotExists) {\n      var _attr = new Property(this.document, name, '');\n\n      this.attributes[name] = _attr;\n      return _attr;\n    }\n\n    return attr || Property.empty(this.document);\n  }\n\n  getHrefAttribute() {\n    for (var key in this.attributes) {\n      if (key === 'href' || key.endsWith(':href')) {\n        return this.attributes[key];\n      }\n    }\n\n    return Property.empty(this.document);\n  }\n\n  getStyle(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var skipAncestors = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var style = this.styles[name];\n\n    if (style) {\n      return style;\n    }\n\n    var attr = this.getAttribute(name);\n\n    if (attr !== null && attr !== void 0 && attr.hasValue()) {\n      this.styles[name] = attr; // move up to me to cache\n\n      return attr;\n    }\n\n    if (!skipAncestors) {\n      var {\n        parent\n      } = this;\n\n      if (parent) {\n        var parentStyle = parent.getStyle(name);\n\n        if (parentStyle !== null && parentStyle !== void 0 && parentStyle.hasValue()) {\n          return parentStyle;\n        }\n      }\n    }\n\n    if (createIfNotExists) {\n      var _style = new Property(this.document, name, '');\n\n      this.styles[name] = _style;\n      return _style;\n    }\n\n    return style || Property.empty(this.document);\n  }\n\n  render(ctx) {\n    // don't render display=none\n    // don't render visibility=hidden\n    if (this.getStyle('display').getString() === 'none' || this.getStyle('visibility').getString() === 'hidden') {\n      return;\n    }\n\n    ctx.save();\n\n    if (this.getStyle('mask').hasValue()) {\n      // mask\n      var mask = this.getStyle('mask').getDefinition();\n\n      if (mask) {\n        this.applyEffects(ctx);\n        mask.apply(ctx, this);\n      }\n    } else if (this.getStyle('filter').getValue('none') !== 'none') {\n      // filter\n      var filter = this.getStyle('filter').getDefinition();\n\n      if (filter) {\n        this.applyEffects(ctx);\n        filter.apply(ctx, this);\n      }\n    } else {\n      this.setContext(ctx);\n      this.renderChildren(ctx);\n      this.clearContext(ctx);\n    }\n\n    ctx.restore();\n  }\n\n  setContext(_) {// NO RENDER\n  }\n\n  applyEffects(ctx) {\n    // transform\n    var transform = Transform.fromElement(this.document, this);\n\n    if (transform) {\n      transform.apply(ctx);\n    } // clip\n\n\n    var clipPathStyleProp = this.getStyle('clip-path', false, true);\n\n    if (clipPathStyleProp.hasValue()) {\n      var clip = clipPathStyleProp.getDefinition();\n\n      if (clip) {\n        clip.apply(ctx);\n      }\n    }\n  }\n\n  clearContext(_) {// NO RENDER\n  }\n\n  renderChildren(ctx) {\n    this.children.forEach(child => {\n      child.render(ctx);\n    });\n  }\n\n  addChild(childNode) {\n    var child = childNode instanceof Element ? childNode : this.document.createElement(childNode);\n    child.parent = this;\n\n    if (!Element.ignoreChildTypes.includes(child.type)) {\n      this.children.push(child);\n    }\n  }\n\n  matchesSelector(selector) {\n    var _node$getAttribute;\n\n    var {\n      node\n    } = this;\n\n    if (typeof node.matches === 'function') {\n      return node.matches(selector);\n    }\n\n    var styleClasses = (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'class');\n\n    if (!styleClasses || styleClasses === '') {\n      return false;\n    }\n\n    return styleClasses.split(' ').some(styleClass => \".\".concat(styleClass) === selector);\n  }\n\n  addStylesFromStyleDefinition() {\n    var {\n      styles,\n      stylesSpecificity\n    } = this.document;\n\n    for (var selector in styles) {\n      if (!selector.startsWith('@') && this.matchesSelector(selector)) {\n        var style = styles[selector];\n        var specificity = stylesSpecificity[selector];\n\n        if (style) {\n          for (var name in style) {\n            var existingSpecificity = this.stylesSpecificity[name];\n\n            if (typeof existingSpecificity === 'undefined') {\n              existingSpecificity = '000';\n            }\n\n            if (specificity >= existingSpecificity) {\n              this.styles[name] = style[name];\n              this.stylesSpecificity[name] = specificity;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  removeStyles(element, ignoreStyles) {\n    var toRestore = ignoreStyles.reduce((toRestore, name) => {\n      var styleProp = element.getStyle(name);\n\n      if (!styleProp.hasValue()) {\n        return toRestore;\n      }\n\n      var value = styleProp.getString();\n      styleProp.setValue('');\n      return [...toRestore, [name, value]];\n    }, []);\n    return toRestore;\n  }\n\n  restoreStyles(element, styles) {\n    styles.forEach(_ref => {\n      var [name, value] = _ref;\n      element.getStyle(name, true).setValue(value);\n    });\n  }\n\n  isFirstChild() {\n    var _this$parent;\n\n    return ((_this$parent = this.parent) === null || _this$parent === void 0 ? void 0 : _this$parent.children.indexOf(this)) === 0;\n  }\n\n}\nElement.ignoreChildTypes = ['title'];\n\nclass UnknownElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n  }\n\n}\n\nfunction wrapFontFamily(fontFamily) {\n  var trimmed = fontFamily.trim();\n  return /^('|\")/.test(trimmed) ? trimmed : \"\\\"\".concat(trimmed, \"\\\"\");\n}\n\nfunction prepareFontFamily(fontFamily) {\n  return typeof process === 'undefined' ? fontFamily : fontFamily.trim().split(',').map(wrapFontFamily).join(',');\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-style\r\n * @param fontStyle\r\n * @returns CSS font style.\r\n */\n\n\nfunction prepareFontStyle(fontStyle) {\n  if (!fontStyle) {\n    return '';\n  }\n\n  var targetFontStyle = fontStyle.trim().toLowerCase();\n\n  switch (targetFontStyle) {\n    case 'normal':\n    case 'italic':\n    case 'oblique':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontStyle;\n\n    default:\n      if (/^oblique\\s+(-|)\\d+deg$/.test(targetFontStyle)) {\n        return targetFontStyle;\n      }\n\n      return '';\n  }\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight\r\n * @param fontWeight\r\n * @returns CSS font weight.\r\n */\n\n\nfunction prepareFontWeight(fontWeight) {\n  if (!fontWeight) {\n    return '';\n  }\n\n  var targetFontWeight = fontWeight.trim().toLowerCase();\n\n  switch (targetFontWeight) {\n    case 'normal':\n    case 'bold':\n    case 'lighter':\n    case 'bolder':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontWeight;\n\n    default:\n      if (/^[\\d.]+$/.test(targetFontWeight)) {\n        return targetFontWeight;\n      }\n\n      return '';\n  }\n}\n\nclass Font {\n  constructor(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit) {\n    var inheritFont = inherit ? typeof inherit === 'string' ? Font.parse(inherit) : inherit : {};\n    this.fontFamily = fontFamily || inheritFont.fontFamily;\n    this.fontSize = fontSize || inheritFont.fontSize;\n    this.fontStyle = fontStyle || inheritFont.fontStyle;\n    this.fontWeight = fontWeight || inheritFont.fontWeight;\n    this.fontVariant = fontVariant || inheritFont.fontVariant;\n  }\n\n  static parse() {\n    var font = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var inherit = arguments.length > 1 ? arguments[1] : undefined;\n    var fontStyle = '';\n    var fontVariant = '';\n    var fontWeight = '';\n    var fontSize = '';\n    var fontFamily = '';\n    var parts = compressSpaces(font).trim().split(' ');\n    var set = {\n      fontSize: false,\n      fontStyle: false,\n      fontWeight: false,\n      fontVariant: false\n    };\n    parts.forEach(part => {\n      switch (true) {\n        case !set.fontStyle && Font.styles.includes(part):\n          if (part !== 'inherit') {\n            fontStyle = part;\n          }\n\n          set.fontStyle = true;\n          break;\n\n        case !set.fontVariant && Font.variants.includes(part):\n          if (part !== 'inherit') {\n            fontVariant = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          break;\n\n        case !set.fontWeight && Font.weights.includes(part):\n          if (part !== 'inherit') {\n            fontWeight = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          break;\n\n        case !set.fontSize:\n          if (part !== 'inherit') {\n            [fontSize] = part.split('/');\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          set.fontSize = true;\n          break;\n\n        default:\n          if (part !== 'inherit') {\n            fontFamily += part;\n          }\n\n      }\n    });\n    return new Font(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit);\n  }\n\n  toString() {\n    return [prepareFontStyle(this.fontStyle), this.fontVariant, prepareFontWeight(this.fontWeight), this.fontSize, // Wrap fontFamily only on nodejs and only for canvas.ctx\n    prepareFontFamily(this.fontFamily)].join(' ').trim();\n  }\n\n}\nFont.styles = 'normal|italic|oblique|inherit';\nFont.variants = 'normal|small-caps|inherit';\nFont.weights = 'normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit';\n\nclass BoundingBox {\n  constructor() {\n    var x1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Number.NaN;\n    var y1 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.NaN;\n    var x2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.NaN;\n    var y2 = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Number.NaN;\n    this.x1 = x1;\n    this.y1 = y1;\n    this.x2 = x2;\n    this.y2 = y2;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  get x() {\n    return this.x1;\n  }\n\n  get y() {\n    return this.y1;\n  }\n\n  get width() {\n    return this.x2 - this.x1;\n  }\n\n  get height() {\n    return this.y2 - this.y1;\n  }\n\n  addPoint(x, y) {\n    if (typeof x !== 'undefined') {\n      if (isNaN(this.x1) || isNaN(this.x2)) {\n        this.x1 = x;\n        this.x2 = x;\n      }\n\n      if (x < this.x1) {\n        this.x1 = x;\n      }\n\n      if (x > this.x2) {\n        this.x2 = x;\n      }\n    }\n\n    if (typeof y !== 'undefined') {\n      if (isNaN(this.y1) || isNaN(this.y2)) {\n        this.y1 = y;\n        this.y2 = y;\n      }\n\n      if (y < this.y1) {\n        this.y1 = y;\n      }\n\n      if (y > this.y2) {\n        this.y2 = y;\n      }\n    }\n  }\n\n  addX(x) {\n    this.addPoint(x, null);\n  }\n\n  addY(y) {\n    this.addPoint(null, y);\n  }\n\n  addBoundingBox(boundingBox) {\n    if (!boundingBox) {\n      return;\n    }\n\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = boundingBox;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  sumCubic(t, p0, p1, p2, p3) {\n    return Math.pow(1 - t, 3) * p0 + 3 * Math.pow(1 - t, 2) * t * p1 + 3 * (1 - t) * Math.pow(t, 2) * p2 + Math.pow(t, 3) * p3;\n  }\n\n  bezierCurveAdd(forX, p0, p1, p2, p3) {\n    var b = 6 * p0 - 12 * p1 + 6 * p2;\n    var a = -3 * p0 + 9 * p1 - 9 * p2 + 3 * p3;\n    var c = 3 * p1 - 3 * p0;\n\n    if (a === 0) {\n      if (b === 0) {\n        return;\n      }\n\n      var t = -c / b;\n\n      if (0 < t && t < 1) {\n        if (forX) {\n          this.addX(this.sumCubic(t, p0, p1, p2, p3));\n        } else {\n          this.addY(this.sumCubic(t, p0, p1, p2, p3));\n        }\n      }\n\n      return;\n    }\n\n    var b2ac = Math.pow(b, 2) - 4 * c * a;\n\n    if (b2ac < 0) {\n      return;\n    }\n\n    var t1 = (-b + Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t1 && t1 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t1, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t1, p0, p1, p2, p3));\n      }\n    }\n\n    var t2 = (-b - Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t2 && t2 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t2, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t2, p0, p1, p2, p3));\n      }\n    }\n  } // from http://blog.hackers-cafe.net/2009/06/how-to-calculate-bezier-curves-bounding.html\n\n\n  addBezierCurve(p0x, p0y, p1x, p1y, p2x, p2y, p3x, p3y) {\n    this.addPoint(p0x, p0y);\n    this.addPoint(p3x, p3y);\n    this.bezierCurveAdd(true, p0x, p1x, p2x, p3x);\n    this.bezierCurveAdd(false, p0y, p1y, p2y, p3y);\n  }\n\n  addQuadraticCurve(p0x, p0y, p1x, p1y, p2x, p2y) {\n    var cp1x = p0x + 2 / 3 * (p1x - p0x); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp1y = p0y + 2 / 3 * (p1y - p0y); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp2x = cp1x + 1 / 3 * (p2x - p0x); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    var cp2y = cp1y + 1 / 3 * (p2y - p0y); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    this.addBezierCurve(p0x, p0y, cp1x, cp2x, cp1y, cp2y, p2x, p2y);\n  }\n\n  isPointInBox(x, y) {\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = this;\n    return x1 <= x && x <= x2 && y1 <= y && y <= y2;\n  }\n\n}\n\nclass PathParser extends SVGPathData {\n  constructor(path) {\n    super(path // Fix spaces after signs.\n    .replace(/([+\\-.])\\s+/gm, '$1') // Remove invalid part.\n    .replace(/[^MmZzLlHhVvCcSsQqTtAae\\d\\s.,+-].*/g, ''));\n    this.control = null;\n    this.start = null;\n    this.current = null;\n    this.command = null;\n    this.commands = this.commands;\n    this.i = -1;\n    this.previousCommand = null;\n    this.points = [];\n    this.angles = [];\n  }\n\n  reset() {\n    this.i = -1;\n    this.command = null;\n    this.previousCommand = null;\n    this.start = new Point(0, 0);\n    this.control = new Point(0, 0);\n    this.current = new Point(0, 0);\n    this.points = [];\n    this.angles = [];\n  }\n\n  isEnd() {\n    var {\n      i,\n      commands\n    } = this;\n    return i >= commands.length - 1;\n  }\n\n  next() {\n    var command = this.commands[++this.i];\n    this.previousCommand = this.command;\n    this.command = command;\n    return command;\n  }\n\n  getPoint() {\n    var xProp = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'x';\n    var yProp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'y';\n    var point = new Point(this.command[xProp], this.command[yProp]);\n    return this.makeAbsolute(point);\n  }\n\n  getAsControlPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.control = point;\n    return point;\n  }\n\n  getAsCurrentPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.current = point;\n    return point;\n  }\n\n  getReflectedControlPoint() {\n    var previousCommand = this.previousCommand.type;\n\n    if (previousCommand !== SVGPathData.CURVE_TO && previousCommand !== SVGPathData.SMOOTH_CURVE_TO && previousCommand !== SVGPathData.QUAD_TO && previousCommand !== SVGPathData.SMOOTH_QUAD_TO) {\n      return this.current;\n    } // reflect point\n\n\n    var {\n      current: {\n        x: cx,\n        y: cy\n      },\n      control: {\n        x: ox,\n        y: oy\n      }\n    } = this;\n    var point = new Point(2 * cx - ox, 2 * cy - oy);\n    return point;\n  }\n\n  makeAbsolute(point) {\n    if (this.command.relative) {\n      var {\n        x,\n        y\n      } = this.current;\n      point.x += x;\n      point.y += y;\n    }\n\n    return point;\n  }\n\n  addMarker(point, from, priorTo) {\n    var {\n      points,\n      angles\n    } = this; // if the last angle isn't filled in because we didn't have this point yet ...\n\n    if (priorTo && angles.length > 0 && !angles[angles.length - 1]) {\n      angles[angles.length - 1] = points[points.length - 1].angleTo(priorTo);\n    }\n\n    this.addMarkerAngle(point, from ? from.angleTo(point) : null);\n  }\n\n  addMarkerAngle(point, angle) {\n    this.points.push(point);\n    this.angles.push(angle);\n  }\n\n  getMarkerPoints() {\n    return this.points;\n  }\n\n  getMarkerAngles() {\n    var {\n      angles\n    } = this;\n    var len = angles.length;\n\n    for (var i = 0; i < len; i++) {\n      if (!angles[i]) {\n        for (var j = i + 1; j < len; j++) {\n          if (angles[j]) {\n            angles[i] = angles[j];\n            break;\n          }\n        }\n      }\n    }\n\n    return angles;\n  }\n\n}\n\nclass RenderedElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.modifiedEmSizeStack = false;\n  }\n\n  calculateOpacity() {\n    var opacity = 1.0; // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n\n    var element = this;\n\n    while (element) {\n      var opacityStyle = element.getStyle('opacity', false, true); // no ancestors on style call\n\n      if (opacityStyle.hasValue(true)) {\n        opacity *= opacityStyle.getNumber();\n      }\n\n      element = element.parent;\n    }\n\n    return opacity;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!fromMeasure) {\n      // causes stack overflow when measuring text with gradients\n      // fill\n      var fillStyleProp = this.getStyle('fill');\n      var fillOpacityStyleProp = this.getStyle('fill-opacity');\n      var strokeStyleProp = this.getStyle('stroke');\n      var strokeOpacityProp = this.getStyle('stroke-opacity');\n\n      if (fillStyleProp.isUrlDefinition()) {\n        var fillStyle = fillStyleProp.getFillStyleDefinition(this, fillOpacityStyleProp);\n\n        if (fillStyle) {\n          ctx.fillStyle = fillStyle;\n        }\n      } else if (fillStyleProp.hasValue()) {\n        if (fillStyleProp.getString() === 'currentColor') {\n          fillStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _fillStyle = fillStyleProp.getColor();\n\n        if (_fillStyle !== 'inherit') {\n          ctx.fillStyle = _fillStyle === 'none' ? 'rgba(0,0,0,0)' : _fillStyle;\n        }\n      }\n\n      if (fillOpacityStyleProp.hasValue()) {\n        var _fillStyle2 = new Property(this.document, 'fill', ctx.fillStyle).addOpacity(fillOpacityStyleProp).getColor();\n\n        ctx.fillStyle = _fillStyle2;\n      } // stroke\n\n\n      if (strokeStyleProp.isUrlDefinition()) {\n        var strokeStyle = strokeStyleProp.getFillStyleDefinition(this, strokeOpacityProp);\n\n        if (strokeStyle) {\n          ctx.strokeStyle = strokeStyle;\n        }\n      } else if (strokeStyleProp.hasValue()) {\n        if (strokeStyleProp.getString() === 'currentColor') {\n          strokeStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _strokeStyle = strokeStyleProp.getString();\n\n        if (_strokeStyle !== 'inherit') {\n          ctx.strokeStyle = _strokeStyle === 'none' ? 'rgba(0,0,0,0)' : _strokeStyle;\n        }\n      }\n\n      if (strokeOpacityProp.hasValue()) {\n        var _strokeStyle2 = new Property(this.document, 'stroke', ctx.strokeStyle).addOpacity(strokeOpacityProp).getString();\n\n        ctx.strokeStyle = _strokeStyle2;\n      }\n\n      var strokeWidthStyleProp = this.getStyle('stroke-width');\n\n      if (strokeWidthStyleProp.hasValue()) {\n        var newLineWidth = strokeWidthStyleProp.getPixels();\n        ctx.lineWidth = !newLineWidth ? PSEUDO_ZERO // browsers don't respect 0 (or node-canvas? :-)\n        : newLineWidth;\n      }\n\n      var strokeLinecapStyleProp = this.getStyle('stroke-linecap');\n      var strokeLinejoinStyleProp = this.getStyle('stroke-linejoin');\n      var strokeMiterlimitProp = this.getStyle('stroke-miterlimit'); // NEED TEST\n      // const pointOrderStyleProp = this.getStyle('paint-order');\n\n      var strokeDasharrayStyleProp = this.getStyle('stroke-dasharray');\n      var strokeDashoffsetProp = this.getStyle('stroke-dashoffset');\n\n      if (strokeLinecapStyleProp.hasValue()) {\n        ctx.lineCap = strokeLinecapStyleProp.getString();\n      }\n\n      if (strokeLinejoinStyleProp.hasValue()) {\n        ctx.lineJoin = strokeLinejoinStyleProp.getString();\n      }\n\n      if (strokeMiterlimitProp.hasValue()) {\n        ctx.miterLimit = strokeMiterlimitProp.getNumber();\n      } // NEED TEST\n      // if (pointOrderStyleProp.hasValue()) {\n      // \t// ?\n      // \tctx.paintOrder = pointOrderStyleProp.getValue();\n      // }\n\n\n      if (strokeDasharrayStyleProp.hasValue() && strokeDasharrayStyleProp.getString() !== 'none') {\n        var gaps = toNumbers(strokeDasharrayStyleProp.getString());\n\n        if (typeof ctx.setLineDash !== 'undefined') {\n          ctx.setLineDash(gaps);\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDash !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDash = gaps;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDash !== 'undefined' && !(gaps.length === 1 && gaps[0] === 0)) {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDash = gaps;\n            }\n\n        var offset = strokeDashoffsetProp.getPixels();\n\n        if (typeof ctx.lineDashOffset !== 'undefined') {\n          ctx.lineDashOffset = offset;\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDashOffset !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDashOffset = offset;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDashOffset !== 'undefined') {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDashOffset = offset;\n            }\n      }\n    } // font\n\n\n    this.modifiedEmSizeStack = false;\n\n    if (typeof ctx.font !== 'undefined') {\n      var fontStyleProp = this.getStyle('font');\n      var fontStyleStyleProp = this.getStyle('font-style');\n      var fontVariantStyleProp = this.getStyle('font-variant');\n      var fontWeightStyleProp = this.getStyle('font-weight');\n      var fontSizeStyleProp = this.getStyle('font-size');\n      var fontFamilyStyleProp = this.getStyle('font-family');\n      var font = new Font(fontStyleStyleProp.getString(), fontVariantStyleProp.getString(), fontWeightStyleProp.getString(), fontSizeStyleProp.hasValue() ? \"\".concat(fontSizeStyleProp.getPixels(true), \"px\") : '', fontFamilyStyleProp.getString(), Font.parse(fontStyleProp.getString(), ctx.font));\n      fontStyleStyleProp.setValue(font.fontStyle);\n      fontVariantStyleProp.setValue(font.fontVariant);\n      fontWeightStyleProp.setValue(font.fontWeight);\n      fontSizeStyleProp.setValue(font.fontSize);\n      fontFamilyStyleProp.setValue(font.fontFamily);\n      ctx.font = font.toString();\n\n      if (fontSizeStyleProp.isPixels()) {\n        this.document.emSize = fontSizeStyleProp.getPixels();\n        this.modifiedEmSizeStack = true;\n      }\n    }\n\n    if (!fromMeasure) {\n      // effects\n      this.applyEffects(ctx); // opacity\n\n      ctx.globalAlpha = this.calculateOpacity();\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n\n    if (this.modifiedEmSizeStack) {\n      this.document.popEmSize();\n    }\n  }\n\n}\n\nclass PathElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'path';\n    this.pathParser = null;\n    this.pathParser = new PathParser(this.getAttribute('d').getString());\n  }\n\n  path(ctx) {\n    var {\n      pathParser\n    } = this;\n    var boundingBox = new BoundingBox();\n    pathParser.reset();\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    while (!pathParser.isEnd()) {\n      switch (pathParser.next().type) {\n        case PathParser.MOVE_TO:\n          this.pathM(ctx, boundingBox);\n          break;\n\n        case PathParser.LINE_TO:\n          this.pathL(ctx, boundingBox);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          this.pathH(ctx, boundingBox);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          this.pathV(ctx, boundingBox);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          this.pathS(ctx, boundingBox);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          this.pathT(ctx, boundingBox);\n          break;\n\n        case PathParser.ARC:\n          this.pathA(ctx, boundingBox);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          this.pathZ(ctx, boundingBox);\n          break;\n      }\n    }\n\n    return boundingBox;\n  }\n\n  getBoundingBox(_) {\n    return this.path();\n  }\n\n  getMarkers() {\n    var {\n      pathParser\n    } = this;\n    var points = pathParser.getMarkerPoints();\n    var angles = pathParser.getMarkerAngles();\n    var markers = points.map((point, i) => [point, angles[i]]);\n    return markers;\n  }\n\n  renderChildren(ctx) {\n    this.path(ctx);\n    this.document.screen.mouse.checkPath(this, ctx);\n    var fillRuleStyleProp = this.getStyle('fill-rule');\n\n    if (ctx.fillStyle !== '') {\n      if (fillRuleStyleProp.getString('inherit') !== 'inherit') {\n        ctx.fill(fillRuleStyleProp.getString());\n      } else {\n        ctx.fill();\n      }\n    }\n\n    if (ctx.strokeStyle !== '') {\n      if (this.getAttribute('vector-effect').getString() === 'non-scaling-stroke') {\n        ctx.save();\n        ctx.setTransform(1, 0, 0, 1, 0, 0);\n        ctx.stroke();\n        ctx.restore();\n      } else {\n        ctx.stroke();\n      }\n    }\n\n    var markers = this.getMarkers();\n\n    if (markers) {\n      var markersLastIndex = markers.length - 1;\n      var markerStartStyleProp = this.getStyle('marker-start');\n      var markerMidStyleProp = this.getStyle('marker-mid');\n      var markerEndStyleProp = this.getStyle('marker-end');\n\n      if (markerStartStyleProp.isUrlDefinition()) {\n        var marker = markerStartStyleProp.getDefinition();\n        var [point, angle] = markers[0];\n        marker.render(ctx, point, angle);\n      }\n\n      if (markerMidStyleProp.isUrlDefinition()) {\n        var _marker = markerMidStyleProp.getDefinition();\n\n        for (var i = 1; i < markersLastIndex; i++) {\n          var [_point, _angle] = markers[i];\n\n          _marker.render(ctx, _point, _angle);\n        }\n      }\n\n      if (markerEndStyleProp.isUrlDefinition()) {\n        var _marker2 = markerEndStyleProp.getDefinition();\n\n        var [_point2, _angle2] = markers[markersLastIndex];\n\n        _marker2.render(ctx, _point2, _angle2);\n      }\n    }\n  }\n\n  static pathM(pathParser) {\n    var point = pathParser.getAsCurrentPoint();\n    pathParser.start = pathParser.current;\n    return {\n      point\n    };\n  }\n\n  pathM(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      point\n    } = PathElement.pathM(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.moveTo(x, y);\n    }\n  }\n\n  static pathL(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point\n    };\n  }\n\n  pathL(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathL(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathH(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point((command.relative ? current.x : 0) + command.x, current.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathH(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathH(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathV(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point(current.x, (command.relative ? current.y : 0) + command.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathV(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathV(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathC(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getPoint('x1', 'y1');\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathC(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathS(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getReflectedControlPoint();\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathS(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathQ(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getAsControlPoint('x1', 'y1');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathQ(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathT(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getReflectedControlPoint();\n    pathParser.control = controlPoint;\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathT(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathA(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var {\n      rX,\n      rY,\n      xRot,\n      lArcFlag,\n      sweepFlag\n    } = command;\n    var xAxisRotation = xRot * (Math.PI / 180.0);\n    var currentPoint = pathParser.getAsCurrentPoint(); // Conversion from endpoint to center parameterization\n    // http://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes\n    // x1', y1'\n\n    var currp = new Point(Math.cos(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.sin(xAxisRotation) * (current.y - currentPoint.y) / 2.0, -Math.sin(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * (current.y - currentPoint.y) / 2.0); // adjust radii\n\n    var l = Math.pow(currp.x, 2) / Math.pow(rX, 2) + Math.pow(currp.y, 2) / Math.pow(rY, 2);\n\n    if (l > 1) {\n      rX *= Math.sqrt(l);\n      rY *= Math.sqrt(l);\n    } // cx', cy'\n\n\n    var s = (lArcFlag === sweepFlag ? -1 : 1) * Math.sqrt((Math.pow(rX, 2) * Math.pow(rY, 2) - Math.pow(rX, 2) * Math.pow(currp.y, 2) - Math.pow(rY, 2) * Math.pow(currp.x, 2)) / (Math.pow(rX, 2) * Math.pow(currp.y, 2) + Math.pow(rY, 2) * Math.pow(currp.x, 2)));\n\n    if (isNaN(s)) {\n      s = 0;\n    }\n\n    var cpp = new Point(s * rX * currp.y / rY, s * -rY * currp.x / rX); // cx, cy\n\n    var centp = new Point((current.x + currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * cpp.x - Math.sin(xAxisRotation) * cpp.y, (current.y + currentPoint.y) / 2.0 + Math.sin(xAxisRotation) * cpp.x + Math.cos(xAxisRotation) * cpp.y); // initial angle\n\n    var a1 = vectorsAngle([1, 0], [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY]); // θ1\n    // angle delta\n\n    var u = [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY];\n    var v = [(-currp.x - cpp.x) / rX, (-currp.y - cpp.y) / rY];\n    var ad = vectorsAngle(u, v); // Δθ\n\n    if (vectorsRatio(u, v) <= -1) {\n      ad = Math.PI;\n    }\n\n    if (vectorsRatio(u, v) >= 1) {\n      ad = 0;\n    }\n\n    return {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    };\n  }\n\n  pathA(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser); // for markers\n\n    var dir = 1 - sweepFlag ? 1.0 : -1.0;\n    var ah = a1 + dir * (ad / 2.0);\n    var halfWay = new Point(centp.x + rX * Math.cos(ah), centp.y + rY * Math.sin(ah));\n    pathParser.addMarkerAngle(halfWay, ah - dir * Math.PI / 2);\n    pathParser.addMarkerAngle(currentPoint, ah - dir * Math.PI);\n    boundingBox.addPoint(currentPoint.x, currentPoint.y); // TODO: this is too naive, make it better\n\n    if (ctx && !isNaN(a1) && !isNaN(ad)) {\n      var r = rX > rY ? rX : rY;\n      var sx = rX > rY ? 1 : rX / rY;\n      var sy = rX > rY ? rY / rX : 1;\n      ctx.translate(centp.x, centp.y);\n      ctx.rotate(xAxisRotation);\n      ctx.scale(sx, sy);\n      ctx.arc(0, 0, r, a1, a1 + ad, Boolean(1 - sweepFlag));\n      ctx.scale(1 / sx, 1 / sy);\n      ctx.rotate(-xAxisRotation);\n      ctx.translate(-centp.x, -centp.y);\n    }\n  }\n\n  static pathZ(pathParser) {\n    pathParser.current = pathParser.start;\n  }\n\n  pathZ(ctx, boundingBox) {\n    PathElement.pathZ(this.pathParser);\n\n    if (ctx) {\n      // only close path if it is not a straight line\n      if (boundingBox.x1 !== boundingBox.x2 && boundingBox.y1 !== boundingBox.y2) {\n        ctx.closePath();\n      }\n    }\n  }\n\n}\n\nclass GlyphElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'glyph';\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    this.unicode = this.getAttribute('unicode').getString();\n    this.arabicForm = this.getAttribute('arabic-form').getString();\n  }\n\n}\n\nclass TextElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TextElement ? true : captureTextNodes);\n    this.type = 'text';\n    this.x = 0;\n    this.y = 0;\n    this.measureCache = -1;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.setContext(ctx, fromMeasure);\n    var textBaseline = this.getStyle('dominant-baseline').getTextBaseline() || this.getStyle('alignment-baseline').getTextBaseline();\n\n    if (textBaseline) {\n      ctx.textBaseline = textBaseline;\n    }\n  }\n\n  initializeCoordinates() {\n    this.x = 0;\n    this.y = 0;\n    this.leafTexts = [];\n    this.textChunkStart = 0;\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n  }\n\n  getBoundingBox(ctx) {\n    if (this.type !== 'text') {\n      return this.getTElementBoundingBox(ctx);\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx);\n    var boundingBox = null; // then calculate bounding box\n\n    this.children.forEach((_, i) => {\n      var childBoundingBox = this.getChildBoundingBox(ctx, this, this, i);\n\n      if (!boundingBox) {\n        boundingBox = childBoundingBox;\n      } else {\n        boundingBox.addBoundingBox(childBoundingBox);\n      }\n    });\n    return boundingBox;\n  }\n\n  getFontSize() {\n    var {\n      document,\n      parent\n    } = this;\n    var inheritFontSize = Font.parse(document.ctx.font).fontSize;\n    var fontSize = parent.getStyle('font-size').getNumber(inheritFontSize);\n    return fontSize;\n  }\n\n  getTElementBoundingBox(ctx) {\n    var fontSize = this.getFontSize();\n    return new BoundingBox(this.x, this.y - fontSize, this.x + this.measureText(ctx), this.y);\n  }\n\n  getGlyph(font, text, i) {\n    var char = text[i];\n    var glyph = null;\n\n    if (font.isArabic) {\n      var len = text.length;\n      var prevChar = text[i - 1];\n      var nextChar = text[i + 1];\n      var arabicForm = 'isolated';\n\n      if ((i === 0 || prevChar === ' ') && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'terminal';\n      }\n\n      if (i > 0 && prevChar !== ' ' && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'medial';\n      }\n\n      if (i > 0 && prevChar !== ' ' && (i === len - 1 || nextChar === ' ')) {\n        arabicForm = 'initial';\n      }\n\n      if (typeof font.glyphs[char] !== 'undefined') {\n        // NEED TEST\n        var maybeGlyph = font.glyphs[char];\n        glyph = maybeGlyph instanceof GlyphElement ? maybeGlyph : maybeGlyph[arabicForm];\n      }\n    } else {\n      glyph = font.glyphs[char];\n    }\n\n    if (!glyph) {\n      glyph = font.missingGlyph;\n    }\n\n    return glyph;\n  }\n\n  getText() {\n    return '';\n  }\n\n  getTextFromNode(node) {\n    var textNode = node || this.node;\n    var childNodes = Array.from(textNode.parentNode.childNodes);\n    var index = childNodes.indexOf(textNode);\n    var lastIndex = childNodes.length - 1;\n    var text = compressSpaces( // textNode.value\n    // || textNode.text\n    textNode.textContent || '');\n\n    if (index === 0) {\n      text = trimLeft(text);\n    }\n\n    if (index === lastIndex) {\n      text = trimRight(text);\n    }\n\n    return text;\n  }\n\n  renderChildren(ctx) {\n    if (this.type !== 'text') {\n      this.renderTElementChildren(ctx);\n      return;\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx); // then render\n\n    this.children.forEach((_, i) => {\n      this.renderChild(ctx, this, this, i);\n    });\n    var {\n      mouse\n    } = this.document.screen; // Do not calc bounding box if mouse is not working.\n\n    if (mouse.isWorking()) {\n      mouse.checkBoundingBox(this, this.getBoundingBox(ctx));\n    }\n  }\n\n  renderTElementChildren(ctx) {\n    var {\n      document,\n      parent\n    } = this;\n    var renderText = this.getText();\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var {\n        unitsPerEm\n      } = customFont.fontFace;\n      var ctxFont = Font.parse(document.ctx.font);\n      var fontSize = parent.getStyle('font-size').getNumber(ctxFont.fontSize);\n      var fontStyle = parent.getStyle('font-style').getString(ctxFont.fontStyle);\n      var scale = fontSize / unitsPerEm;\n      var text = customFont.isRTL ? renderText.split('').reverse().join('') : renderText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        ctx.translate(this.x, this.y);\n        ctx.scale(scale, -scale);\n        var lw = ctx.lineWidth;\n        ctx.lineWidth = ctx.lineWidth * unitsPerEm / fontSize;\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, .4, 1, 0, 0);\n        }\n\n        glyph.render(ctx);\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, -.4, 1, 0, 0);\n        }\n\n        ctx.lineWidth = lw;\n        ctx.scale(1 / scale, -1 / scale);\n        ctx.translate(-this.x, -this.y);\n        this.x += fontSize * (glyph.horizAdvX || customFont.horizAdvX) / unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          this.x += dx[i];\n        }\n      }\n\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = this; // NEED TEST\n    // if (ctx.paintOrder === 'stroke') {\n    // \tif (ctx.strokeStyle) {\n    // \t\tctx.strokeText(renderText, x, y);\n    // \t}\n    // \tif (ctx.fillStyle) {\n    // \t\tctx.fillText(renderText, x, y);\n    // \t}\n    // } else {\n\n    if (ctx.fillStyle) {\n      ctx.fillText(renderText, x, y);\n    }\n\n    if (ctx.strokeStyle) {\n      ctx.strokeText(renderText, x, y);\n    } // }\n\n  }\n\n  applyAnchoring() {\n    if (this.textChunkStart >= this.leafTexts.length) {\n      return;\n    } // This is basically the \"Apply anchoring\" part of https://www.w3.org/TR/SVG2/text.html#TextLayoutAlgorithm.\n    // The difference is that we apply the anchoring as soon as a chunk is finished. This saves some extra looping.\n    // Vertical text is not supported.\n\n\n    var firstElement = this.leafTexts[this.textChunkStart];\n    var textAnchor = firstElement.getStyle('text-anchor').getString('start');\n    var isRTL = false; // we treat RTL like LTR\n\n    var shift = 0;\n\n    if (textAnchor === 'start' && !isRTL || textAnchor === 'end' && isRTL) {\n      shift = firstElement.x - this.minX;\n    } else if (textAnchor === 'end' && !isRTL || textAnchor === 'start' && isRTL) {\n      shift = firstElement.x - this.maxX;\n    } else {\n      shift = firstElement.x - (this.minX + this.maxX) / 2;\n    }\n\n    for (var i = this.textChunkStart; i < this.leafTexts.length; i++) {\n      this.leafTexts[i].x += shift;\n    } // start new chunk\n\n\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n    this.textChunkStart = this.leafTexts.length;\n  }\n\n  adjustChildCoordinatesRecursive(ctx) {\n    this.children.forEach((_, i) => {\n      this.adjustChildCoordinatesRecursiveCore(ctx, this, this, i);\n    });\n    this.applyAnchoring();\n  }\n\n  adjustChildCoordinatesRecursiveCore(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (child.children.length > 0) {\n      child.children.forEach((_, i) => {\n        textParent.adjustChildCoordinatesRecursiveCore(ctx, textParent, child, i);\n      });\n    } else {\n      // only leafs are relevant\n      this.adjustChildCoordinates(ctx, textParent, parent, i);\n    }\n  }\n\n  adjustChildCoordinates(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (typeof child.measureText !== 'function') {\n      return child;\n    }\n\n    ctx.save();\n    child.setContext(ctx, true);\n    var xAttr = child.getAttribute('x');\n    var yAttr = child.getAttribute('y');\n    var dxAttr = child.getAttribute('dx');\n    var dyAttr = child.getAttribute('dy');\n    var customFont = child.getStyle('font-family').getDefinition();\n    var isRTL = Boolean(customFont) && customFont.isRTL;\n\n    if (i === 0) {\n      // First children inherit attributes from parent(s). Positional attributes\n      // are only inherited from a parent to it's first child.\n      if (!xAttr.hasValue()) {\n        xAttr.setValue(child.getInheritedAttribute('x'));\n      }\n\n      if (!yAttr.hasValue()) {\n        yAttr.setValue(child.getInheritedAttribute('y'));\n      }\n\n      if (!dxAttr.hasValue()) {\n        dxAttr.setValue(child.getInheritedAttribute('dx'));\n      }\n\n      if (!dyAttr.hasValue()) {\n        dyAttr.setValue(child.getInheritedAttribute('dy'));\n      }\n    }\n\n    var width = child.measureText(ctx);\n\n    if (isRTL) {\n      textParent.x -= width;\n    }\n\n    if (xAttr.hasValue()) {\n      // an \"x\" attribute marks the start of a new chunk\n      textParent.applyAnchoring();\n      child.x = xAttr.getPixels('x');\n\n      if (dxAttr.hasValue()) {\n        child.x += dxAttr.getPixels('x');\n      }\n    } else {\n      if (dxAttr.hasValue()) {\n        textParent.x += dxAttr.getPixels('x');\n      }\n\n      child.x = textParent.x;\n    }\n\n    textParent.x = child.x;\n\n    if (!isRTL) {\n      textParent.x += width;\n    }\n\n    if (yAttr.hasValue()) {\n      child.y = yAttr.getPixels('y');\n\n      if (dyAttr.hasValue()) {\n        child.y += dyAttr.getPixels('y');\n      }\n    } else {\n      if (dyAttr.hasValue()) {\n        textParent.y += dyAttr.getPixels('y');\n      }\n\n      child.y = textParent.y;\n    }\n\n    textParent.y = child.y; // update the current chunk and it's bounds\n\n    textParent.leafTexts.push(child);\n    textParent.minX = Math.min(textParent.minX, child.x, child.x + width);\n    textParent.maxX = Math.max(textParent.maxX, child.x, child.x + width);\n    child.clearContext(ctx);\n    ctx.restore();\n    return child;\n  }\n\n  getChildBoundingBox(ctx, textParent, parent, i) {\n    var child = parent.children[i]; // not a text node?\n\n    if (typeof child.getBoundingBox !== 'function') {\n      return null;\n    }\n\n    var boundingBox = child.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return null;\n    }\n\n    child.children.forEach((_, i) => {\n      var childBoundingBox = textParent.getChildBoundingBox(ctx, textParent, child, i);\n      boundingBox.addBoundingBox(childBoundingBox);\n    });\n    return boundingBox;\n  }\n\n  renderChild(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n    child.render(ctx);\n    child.children.forEach((_, i) => {\n      textParent.renderChild(ctx, textParent, child, i);\n    });\n  }\n\n  measureText(ctx) {\n    var {\n      measureCache\n    } = this;\n\n    if (~measureCache) {\n      return measureCache;\n    }\n\n    var renderText = this.getText();\n    var measure = this.measureTargetText(ctx, renderText);\n    this.measureCache = measure;\n    return measure;\n  }\n\n  measureTargetText(ctx, targetText) {\n    if (!targetText.length) {\n      return 0;\n    }\n\n    var {\n      parent\n    } = this;\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var fontSize = this.getFontSize();\n      var text = customFont.isRTL ? targetText.split('').reverse().join('') : targetText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n      var _measure = 0;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        _measure += (glyph.horizAdvX || customFont.horizAdvX) * fontSize / customFont.fontFace.unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          _measure += dx[i];\n        }\n      }\n\n      return _measure;\n    }\n\n    if (!ctx.measureText) {\n      return targetText.length * 10;\n    }\n\n    ctx.save();\n    this.setContext(ctx, true);\n    var {\n      width: measure\n    } = ctx.measureText(targetText);\n    this.clearContext(ctx);\n    ctx.restore();\n    return measure;\n  }\n  /**\r\n   * Inherits positional attributes from {@link TextElement} parent(s). Attributes\r\n   * are only inherited from a parent to its first child.\r\n   * @param name - The attribute name.\r\n   * @returns The attribute value or null.\r\n   */\n\n\n  getInheritedAttribute(name) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias,consistent-this\n    var current = this;\n\n    while (current instanceof TextElement && current.isFirstChild()) {\n      var parentAttr = current.parent.getAttribute(name);\n\n      if (parentAttr.hasValue(true)) {\n        return parentAttr.getValue('0');\n      }\n\n      current = current.parent;\n    }\n\n    return null;\n  }\n\n}\n\nclass TSpanElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TSpanElement ? true : captureTextNodes);\n    this.type = 'tspan'; // if this node has children, then they own the text\n\n    this.text = this.children.length > 0 ? '' : this.getTextFromNode();\n  }\n\n  getText() {\n    return this.text;\n  }\n\n}\n\nclass TextNode extends TSpanElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'textNode';\n  }\n\n}\n\nclass SVGElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'svg';\n    this.root = false;\n  }\n\n  setContext(ctx) {\n    var _this$node$parentNode;\n\n    var {\n      document\n    } = this;\n    var {\n      screen,\n      window\n    } = document;\n    var canvas = ctx.canvas;\n    screen.setDefaults(ctx);\n\n    if (canvas.style && typeof ctx.font !== 'undefined' && window && typeof window.getComputedStyle !== 'undefined') {\n      ctx.font = window.getComputedStyle(canvas).getPropertyValue('font');\n      var fontSizeProp = new Property(document, 'fontSize', Font.parse(ctx.font).fontSize);\n\n      if (fontSizeProp.hasValue()) {\n        document.rootEmSize = fontSizeProp.getPixels('y');\n        document.emSize = document.rootEmSize;\n      }\n    } // create new view port\n\n\n    if (!this.getAttribute('x').hasValue()) {\n      this.getAttribute('x', true).setValue(0);\n    }\n\n    if (!this.getAttribute('y').hasValue()) {\n      this.getAttribute('y', true).setValue(0);\n    }\n\n    var {\n      width,\n      height\n    } = screen.viewPort;\n\n    if (!this.getStyle('width').hasValue()) {\n      this.getStyle('width', true).setValue('100%');\n    }\n\n    if (!this.getStyle('height').hasValue()) {\n      this.getStyle('height', true).setValue('100%');\n    }\n\n    if (!this.getStyle('color').hasValue()) {\n      this.getStyle('color', true).setValue('black');\n    }\n\n    var refXAttr = this.getAttribute('refX');\n    var refYAttr = this.getAttribute('refY');\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var viewBox = viewBoxAttr.hasValue() ? toNumbers(viewBoxAttr.getString()) : null;\n    var clip = !this.root && this.getStyle('overflow').getValue('hidden') !== 'visible';\n    var minX = 0;\n    var minY = 0;\n    var clipX = 0;\n    var clipY = 0;\n\n    if (viewBox) {\n      minX = viewBox[0];\n      minY = viewBox[1];\n    }\n\n    if (!this.root) {\n      width = this.getStyle('width').getPixels('x');\n      height = this.getStyle('height').getPixels('y');\n\n      if (this.type === 'marker') {\n        clipX = minX;\n        clipY = minY;\n        minX = 0;\n        minY = 0;\n      }\n    }\n\n    screen.viewPort.setCurrent(width, height); // Default value of transform-origin is center only for root SVG elements\n    // https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/transform-origin\n\n    if (this.node // is not temporary SVGElement\n    && (!this.parent || ((_this$node$parentNode = this.node.parentNode) === null || _this$node$parentNode === void 0 ? void 0 : _this$node$parentNode.nodeName) === 'foreignObject') && this.getStyle('transform', false, true).hasValue() && !this.getStyle('transform-origin', false, true).hasValue()) {\n      this.getStyle('transform-origin', true, true).setValue('50% 50%');\n    }\n\n    super.setContext(ctx);\n    ctx.translate(this.getAttribute('x').getPixels('x'), this.getAttribute('y').getPixels('y'));\n\n    if (viewBox) {\n      width = viewBox[2];\n      height = viewBox[3];\n    }\n\n    document.setViewBox({\n      ctx,\n      aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n      width: screen.viewPort.width,\n      desiredWidth: width,\n      height: screen.viewPort.height,\n      desiredHeight: height,\n      minX,\n      minY,\n      refX: refXAttr.getValue(),\n      refY: refYAttr.getValue(),\n      clip,\n      clipX,\n      clipY\n    });\n\n    if (viewBox) {\n      screen.viewPort.removeCurrent();\n      screen.viewPort.setCurrent(width, height);\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n    this.document.screen.viewPort.removeCurrent();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var widthAttr = this.getAttribute('width', true);\n    var heightAttr = this.getAttribute('height', true);\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var styleAttr = this.getAttribute('style');\n    var originWidth = widthAttr.getNumber(0);\n    var originHeight = heightAttr.getNumber(0);\n\n    if (preserveAspectRatio) {\n      if (typeof preserveAspectRatio === 'string') {\n        this.getAttribute('preserveAspectRatio', true).setValue(preserveAspectRatio);\n      } else {\n        var preserveAspectRatioAttr = this.getAttribute('preserveAspectRatio');\n\n        if (preserveAspectRatioAttr.hasValue()) {\n          preserveAspectRatioAttr.setValue(preserveAspectRatioAttr.getString().replace(/^\\s*(\\S.*\\S)\\s*$/, '$1'));\n        }\n      }\n    }\n\n    widthAttr.setValue(width);\n    heightAttr.setValue(height);\n\n    if (!viewBoxAttr.hasValue()) {\n      viewBoxAttr.setValue(\"0 0 \".concat(originWidth || width, \" \").concat(originHeight || height));\n    }\n\n    if (styleAttr.hasValue()) {\n      var widthStyle = this.getStyle('width');\n      var heightStyle = this.getStyle('height');\n\n      if (widthStyle.hasValue()) {\n        widthStyle.setValue(\"\".concat(width, \"px\"));\n      }\n\n      if (heightStyle.hasValue()) {\n        heightStyle.setValue(\"\".concat(height, \"px\"));\n      }\n    }\n  }\n\n}\n\nclass RectElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'rect';\n  }\n\n  path(ctx) {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width', false, true).getPixels('x');\n    var height = this.getStyle('height', false, true).getPixels('y');\n    var rxAttr = this.getAttribute('rx');\n    var ryAttr = this.getAttribute('ry');\n    var rx = rxAttr.getPixels('x');\n    var ry = ryAttr.getPixels('y');\n\n    if (rxAttr.hasValue() && !ryAttr.hasValue()) {\n      ry = rx;\n    }\n\n    if (ryAttr.hasValue() && !rxAttr.hasValue()) {\n      rx = ry;\n    }\n\n    rx = Math.min(rx, width / 2.0);\n    ry = Math.min(ry, height / 2.0);\n\n    if (ctx) {\n      var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n      ctx.beginPath(); // always start the path so we don't fill prior paths\n\n      if (height > 0 && width > 0) {\n        ctx.moveTo(x + rx, y);\n        ctx.lineTo(x + width - rx, y);\n        ctx.bezierCurveTo(x + width - rx + KAPPA * rx, y, x + width, y + ry - KAPPA * ry, x + width, y + ry);\n        ctx.lineTo(x + width, y + height - ry);\n        ctx.bezierCurveTo(x + width, y + height - ry + KAPPA * ry, x + width - rx + KAPPA * rx, y + height, x + width - rx, y + height);\n        ctx.lineTo(x + rx, y + height);\n        ctx.bezierCurveTo(x + rx - KAPPA * rx, y + height, x, y + height - ry + KAPPA * ry, x, y + height - ry);\n        ctx.lineTo(x, y + ry);\n        ctx.bezierCurveTo(x, y + ry - KAPPA * ry, x + rx - KAPPA * rx, y, x + rx, y);\n        ctx.closePath();\n      }\n    }\n\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass CircleElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'circle';\n  }\n\n  path(ctx) {\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n    var r = this.getAttribute('r').getPixels();\n\n    if (ctx && r > 0) {\n      ctx.beginPath();\n      ctx.arc(cx, cy, r, 0, Math.PI * 2, false);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - r, cy - r, cx + r, cy + r);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass EllipseElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'ellipse';\n  }\n\n  path(ctx) {\n    var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n    var rx = this.getAttribute('rx').getPixels('x');\n    var ry = this.getAttribute('ry').getPixels('y');\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n\n    if (ctx && rx > 0 && ry > 0) {\n      ctx.beginPath();\n      ctx.moveTo(cx + rx, cy);\n      ctx.bezierCurveTo(cx + rx, cy + KAPPA * ry, cx + KAPPA * rx, cy + ry, cx, cy + ry);\n      ctx.bezierCurveTo(cx - KAPPA * rx, cy + ry, cx - rx, cy + KAPPA * ry, cx - rx, cy);\n      ctx.bezierCurveTo(cx - rx, cy - KAPPA * ry, cx - KAPPA * rx, cy - ry, cx, cy - ry);\n      ctx.bezierCurveTo(cx + KAPPA * rx, cy - ry, cx + rx, cy - KAPPA * ry, cx + rx, cy);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - rx, cy - ry, cx + rx, cy + ry);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass LineElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'line';\n  }\n\n  getPoints() {\n    return [new Point(this.getAttribute('x1').getPixels('x'), this.getAttribute('y1').getPixels('y')), new Point(this.getAttribute('x2').getPixels('x'), this.getAttribute('y2').getPixels('y'))];\n  }\n\n  path(ctx) {\n    var [{\n      x: x0,\n      y: y0\n    }, {\n      x: x1,\n      y: y1\n    }] = this.getPoints();\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n      ctx.lineTo(x1, y1);\n    }\n\n    return new BoundingBox(x0, y0, x1, y1);\n  }\n\n  getMarkers() {\n    var [p0, p1] = this.getPoints();\n    var a = p0.angleTo(p1);\n    return [[p0, a], [p1, a]];\n  }\n\n}\n\nclass PolylineElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'polyline';\n    this.points = [];\n    this.points = Point.parsePath(this.getAttribute('points').getString());\n  }\n\n  path(ctx) {\n    var {\n      points\n    } = this;\n    var [{\n      x: x0,\n      y: y0\n    }] = points;\n    var boundingBox = new BoundingBox(x0, y0);\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n    }\n\n    points.forEach(_ref => {\n      var {\n        x,\n        y\n      } = _ref;\n      boundingBox.addPoint(x, y);\n\n      if (ctx) {\n        ctx.lineTo(x, y);\n      }\n    });\n    return boundingBox;\n  }\n\n  getMarkers() {\n    var {\n      points\n    } = this;\n    var lastIndex = points.length - 1;\n    var markers = [];\n    points.forEach((point, i) => {\n      if (i === lastIndex) {\n        return;\n      }\n\n      markers.push([point, point.angleTo(points[i + 1])]);\n    });\n\n    if (markers.length > 0) {\n      markers.push([points[points.length - 1], markers[markers.length - 1][1]]);\n    }\n\n    return markers;\n  }\n\n}\n\nclass PolygonElement extends PolylineElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'polygon';\n  }\n\n  path(ctx) {\n    var boundingBox = super.path(ctx);\n    var [{\n      x,\n      y\n    }] = this.points;\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n      ctx.closePath();\n    }\n\n    return boundingBox;\n  }\n\n}\n\nclass PatternElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'pattern';\n  }\n\n  createPattern(ctx, _, parentOpacityProp) {\n    var width = this.getStyle('width').getPixels('x', true);\n    var height = this.getStyle('height').getPixels('y', true); // render me using a temporary svg element\n\n    var patternSvg = new SVGElement(this.document, null);\n    patternSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    patternSvg.attributes.width = new Property(this.document, 'width', \"\".concat(width, \"px\"));\n    patternSvg.attributes.height = new Property(this.document, 'height', \"\".concat(height, \"px\"));\n    patternSvg.attributes.transform = new Property(this.document, 'transform', this.getAttribute('patternTransform').getValue());\n    patternSvg.children = this.children;\n    var patternCanvas = this.document.createCanvas(width, height);\n    var patternCtx = patternCanvas.getContext('2d');\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue() && yAttr.hasValue()) {\n      patternCtx.translate(xAttr.getPixels('x', true), yAttr.getPixels('y', true));\n    }\n\n    if (parentOpacityProp.hasValue()) {\n      this.styles['fill-opacity'] = parentOpacityProp;\n    } else {\n      Reflect.deleteProperty(this.styles, 'fill-opacity');\n    } // render 3x3 grid so when we transform there's no white space on edges\n\n\n    for (var x = -1; x <= 1; x++) {\n      for (var y = -1; y <= 1; y++) {\n        patternCtx.save();\n        patternSvg.attributes.x = new Property(this.document, 'x', x * patternCanvas.width);\n        patternSvg.attributes.y = new Property(this.document, 'y', y * patternCanvas.height);\n        patternSvg.render(patternCtx);\n        patternCtx.restore();\n      }\n    }\n\n    var pattern = ctx.createPattern(patternCanvas, 'repeat');\n    return pattern;\n  }\n\n}\n\nclass MarkerElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'marker';\n  }\n\n  render(ctx, point, angle) {\n    if (!point) {\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = point;\n    var orient = this.getAttribute('orient').getString('auto');\n    var markerUnits = this.getAttribute('markerUnits').getString('strokeWidth');\n    ctx.translate(x, y);\n\n    if (orient === 'auto') {\n      ctx.rotate(angle);\n    }\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(ctx.lineWidth, ctx.lineWidth);\n    }\n\n    ctx.save(); // render me using a temporary svg element\n\n    var markerSvg = new SVGElement(this.document, null);\n    markerSvg.type = this.type;\n    markerSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    markerSvg.attributes.refX = new Property(this.document, 'refX', this.getAttribute('refX').getValue());\n    markerSvg.attributes.refY = new Property(this.document, 'refY', this.getAttribute('refY').getValue());\n    markerSvg.attributes.width = new Property(this.document, 'width', this.getAttribute('markerWidth').getValue());\n    markerSvg.attributes.height = new Property(this.document, 'height', this.getAttribute('markerHeight').getValue());\n    markerSvg.attributes.overflow = new Property(this.document, 'overflow', this.getAttribute('overflow').getValue());\n    markerSvg.attributes.fill = new Property(this.document, 'fill', this.getAttribute('fill').getColor('black'));\n    markerSvg.attributes.stroke = new Property(this.document, 'stroke', this.getAttribute('stroke').getValue('none'));\n    markerSvg.children = this.children;\n    markerSvg.render(ctx);\n    ctx.restore();\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(1 / ctx.lineWidth, 1 / ctx.lineWidth);\n    }\n\n    if (orient === 'auto') {\n      ctx.rotate(-angle);\n    }\n\n    ctx.translate(-x, -y);\n  }\n\n}\n\nclass DefsElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'defs';\n  }\n\n  render() {// NOOP\n  }\n\n}\n\nclass GElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'g';\n  }\n\n  getBoundingBox(ctx) {\n    var boundingBox = new BoundingBox();\n    this.children.forEach(child => {\n      boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n    });\n    return boundingBox;\n  }\n\n}\n\nclass GradientElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.attributesToInherit = ['gradientUnits'];\n    this.stops = [];\n    var {\n      stops,\n      children\n    } = this;\n    children.forEach(child => {\n      if (child.type === 'stop') {\n        stops.push(child);\n      }\n    });\n  }\n\n  getGradientUnits() {\n    return this.getAttribute('gradientUnits').getString('objectBoundingBox');\n  }\n\n  createGradient(ctx, element, parentOpacityProp) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n    var stopsContainer = this;\n\n    if (this.getHrefAttribute().hasValue()) {\n      stopsContainer = this.getHrefAttribute().getDefinition();\n      this.inheritStopContainer(stopsContainer);\n    }\n\n    var {\n      stops\n    } = stopsContainer;\n    var gradient = this.getGradient(ctx, element);\n\n    if (!gradient) {\n      return this.addParentOpacity(parentOpacityProp, stops[stops.length - 1].color);\n    }\n\n    stops.forEach(stop => {\n      gradient.addColorStop(stop.offset, this.addParentOpacity(parentOpacityProp, stop.color));\n    });\n\n    if (this.getAttribute('gradientTransform').hasValue()) {\n      // render as transformed pattern on temporary canvas\n      var {\n        document\n      } = this;\n      var {\n        MAX_VIRTUAL_PIXELS,\n        viewPort\n      } = document.screen;\n      var [rootView] = viewPort.viewPorts;\n      var rect = new RectElement(document, null);\n      rect.attributes.x = new Property(document, 'x', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.y = new Property(document, 'y', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.width = new Property(document, 'width', MAX_VIRTUAL_PIXELS);\n      rect.attributes.height = new Property(document, 'height', MAX_VIRTUAL_PIXELS);\n      var group = new GElement(document, null);\n      group.attributes.transform = new Property(document, 'transform', this.getAttribute('gradientTransform').getValue());\n      group.children = [rect];\n      var patternSvg = new SVGElement(document, null);\n      patternSvg.attributes.x = new Property(document, 'x', 0);\n      patternSvg.attributes.y = new Property(document, 'y', 0);\n      patternSvg.attributes.width = new Property(document, 'width', rootView.width);\n      patternSvg.attributes.height = new Property(document, 'height', rootView.height);\n      patternSvg.children = [group];\n      var patternCanvas = document.createCanvas(rootView.width, rootView.height);\n      var patternCtx = patternCanvas.getContext('2d');\n      patternCtx.fillStyle = gradient;\n      patternSvg.render(patternCtx);\n      return patternCtx.createPattern(patternCanvas, 'no-repeat');\n    }\n\n    return gradient;\n  }\n\n  inheritStopContainer(stopsContainer) {\n    this.attributesToInherit.forEach(attributeToInherit => {\n      if (!this.getAttribute(attributeToInherit).hasValue() && stopsContainer.getAttribute(attributeToInherit).hasValue()) {\n        this.getAttribute(attributeToInherit, true).setValue(stopsContainer.getAttribute(attributeToInherit).getValue());\n      }\n    });\n  }\n\n  addParentOpacity(parentOpacityProp, color) {\n    if (parentOpacityProp.hasValue()) {\n      var colorProp = new Property(this.document, 'color', color);\n      return colorProp.addOpacity(parentOpacityProp).getColor();\n    }\n\n    return color;\n  }\n\n}\n\nclass LinearGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'linearGradient';\n    this.attributesToInherit.push('x1', 'y1', 'x2', 'y2');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = isBoundingBoxUnits ? element.getBoundingBox(ctx) : null;\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('x1').hasValue() && !this.getAttribute('y1').hasValue() && !this.getAttribute('x2').hasValue() && !this.getAttribute('y2').hasValue()) {\n      this.getAttribute('x1', true).setValue(0);\n      this.getAttribute('y1', true).setValue(0);\n      this.getAttribute('x2', true).setValue(1);\n      this.getAttribute('y2', true).setValue(0);\n    }\n\n    var x1 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x1').getNumber() : this.getAttribute('x1').getPixels('x');\n    var y1 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y1').getNumber() : this.getAttribute('y1').getPixels('y');\n    var x2 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x2').getNumber() : this.getAttribute('x2').getPixels('x');\n    var y2 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y2').getNumber() : this.getAttribute('y2').getPixels('y');\n\n    if (x1 === x2 && y1 === y2) {\n      return null;\n    }\n\n    return ctx.createLinearGradient(x1, y1, x2, y2);\n  }\n\n}\n\nclass RadialGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'radialGradient';\n    this.attributesToInherit.push('cx', 'cy', 'r', 'fx', 'fy', 'fr');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('cx').hasValue()) {\n      this.getAttribute('cx', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('cy').hasValue()) {\n      this.getAttribute('cy', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('r').hasValue()) {\n      this.getAttribute('r', true).setValue('50%');\n    }\n\n    var cx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('cx').getNumber() : this.getAttribute('cx').getPixels('x');\n    var cy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('cy').getNumber() : this.getAttribute('cy').getPixels('y');\n    var fx = cx;\n    var fy = cy;\n\n    if (this.getAttribute('fx').hasValue()) {\n      fx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('fx').getNumber() : this.getAttribute('fx').getPixels('x');\n    }\n\n    if (this.getAttribute('fy').hasValue()) {\n      fy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('fy').getNumber() : this.getAttribute('fy').getPixels('y');\n    }\n\n    var r = isBoundingBoxUnits ? (boundingBox.width + boundingBox.height) / 2.0 * this.getAttribute('r').getNumber() : this.getAttribute('r').getPixels();\n    var fr = this.getAttribute('fr').getPixels();\n    return ctx.createRadialGradient(fx, fy, fr, cx, cy, r);\n  }\n\n}\n\nclass StopElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'stop';\n    var offset = Math.max(0, Math.min(1, this.getAttribute('offset').getNumber()));\n    var stopOpacity = this.getStyle('stop-opacity');\n    var stopColor = this.getStyle('stop-color', true);\n\n    if (stopColor.getString() === '') {\n      stopColor.setValue('#000');\n    }\n\n    if (stopOpacity.hasValue()) {\n      stopColor = stopColor.addOpacity(stopOpacity);\n    }\n\n    this.offset = offset;\n    this.color = stopColor.getColor();\n  }\n\n}\n\nclass AnimateElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'animate';\n    this.duration = 0;\n    this.initialValue = null;\n    this.initialUnits = '';\n    this.removed = false;\n    this.frozen = false;\n    document.screen.animations.push(this);\n    this.begin = this.getAttribute('begin').getMilliseconds();\n    this.maxDuration = this.begin + this.getAttribute('dur').getMilliseconds();\n    this.from = this.getAttribute('from');\n    this.to = this.getAttribute('to');\n    this.values = new Property(document, 'values', null);\n    var valuesAttr = this.getAttribute('values');\n\n    if (valuesAttr.hasValue()) {\n      this.values.setValue(valuesAttr.getString().split(';'));\n    }\n  }\n\n  getProperty() {\n    var attributeType = this.getAttribute('attributeType').getString();\n    var attributeName = this.getAttribute('attributeName').getString();\n\n    if (attributeType === 'CSS') {\n      return this.parent.getStyle(attributeName, true);\n    }\n\n    return this.parent.getAttribute(attributeName, true);\n  }\n\n  calcValue() {\n    var {\n      initialUnits\n    } = this;\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var newValue = from.getNumber() + (to.getNumber() - from.getNumber()) * progress;\n\n    if (initialUnits === '%') {\n      newValue *= 100.0; // numValue() returns 0-1 whereas properties are 0-100\n    }\n\n    return \"\".concat(newValue).concat(initialUnits);\n  }\n\n  update(delta) {\n    var {\n      parent\n    } = this;\n    var prop = this.getProperty(); // set initial value\n\n    if (!this.initialValue) {\n      this.initialValue = prop.getString();\n      this.initialUnits = prop.getUnits();\n    } // if we're past the end time\n\n\n    if (this.duration > this.maxDuration) {\n      var fill = this.getAttribute('fill').getString('remove'); // loop for indefinitely repeating animations\n\n      if (this.getAttribute('repeatCount').getString() === 'indefinite' || this.getAttribute('repeatDur').getString() === 'indefinite') {\n        this.duration = 0;\n      } else if (fill === 'freeze' && !this.frozen) {\n        this.frozen = true;\n        parent.animationFrozen = true;\n        parent.animationFrozenValue = prop.getString();\n      } else if (fill === 'remove' && !this.removed) {\n        this.removed = true;\n        prop.setValue(parent.animationFrozen ? parent.animationFrozenValue : this.initialValue);\n        return true;\n      }\n\n      return false;\n    }\n\n    this.duration += delta; // if we're past the begin time\n\n    var updated = false;\n\n    if (this.begin < this.duration) {\n      var newValue = this.calcValue(); // tween\n\n      var typeAttr = this.getAttribute('type');\n\n      if (typeAttr.hasValue()) {\n        // for transform, etc.\n        var type = typeAttr.getString();\n        newValue = \"\".concat(type, \"(\").concat(newValue, \")\");\n      }\n\n      prop.setValue(newValue);\n      updated = true;\n    }\n\n    return updated;\n  }\n\n  getProgress() {\n    var {\n      document,\n      values\n    } = this;\n    var result = {\n      progress: (this.duration - this.begin) / (this.maxDuration - this.begin)\n    };\n\n    if (values.hasValue()) {\n      var p = result.progress * (values.getValue().length - 1);\n      var lb = Math.floor(p);\n      var ub = Math.ceil(p);\n      result.from = new Property(document, 'from', parseFloat(values.getValue()[lb]));\n      result.to = new Property(document, 'to', parseFloat(values.getValue()[ub]));\n      result.progress = (p - lb) / (ub - lb);\n    } else {\n      result.from = this.from;\n      result.to = this.to;\n    }\n\n    return result;\n  }\n\n}\n\nclass AnimateColorElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateColor';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress();\n    var colorFrom = new RGBColor(from.getColor());\n    var colorTo = new RGBColor(to.getColor());\n\n    if (colorFrom.ok && colorTo.ok) {\n      // tween color linearly\n      var r = colorFrom.r + (colorTo.r - colorFrom.r) * progress;\n      var g = colorFrom.g + (colorTo.g - colorFrom.g) * progress;\n      var b = colorFrom.b + (colorTo.b - colorFrom.b) * progress; // ? alpha\n\n      return \"rgb(\".concat(Math.floor(r), \", \").concat(Math.floor(g), \", \").concat(Math.floor(b), \")\");\n    }\n\n    return this.getAttribute('from').getColor();\n  }\n\n}\n\nclass AnimateTransformElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateTransform';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var transformFrom = toNumbers(from.getString());\n    var transformTo = toNumbers(to.getString());\n    var newValue = transformFrom.map((from, i) => {\n      var to = transformTo[i];\n      return from + (to - from) * progress;\n    }).join(' ');\n    return newValue;\n  }\n\n}\n\nclass FontElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font';\n    this.glyphs = Object.create(null);\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    var {\n      definitions\n    } = document;\n    var {\n      children\n    } = this;\n\n    for (var child of children) {\n      switch (child.type) {\n        case 'font-face':\n          {\n            this.fontFace = child;\n            var fontFamilyStyle = child.getStyle('font-family');\n\n            if (fontFamilyStyle.hasValue()) {\n              definitions[fontFamilyStyle.getString()] = this;\n            }\n\n            break;\n          }\n\n        case 'missing-glyph':\n          this.missingGlyph = child;\n          break;\n\n        case 'glyph':\n          {\n            var glyph = child;\n\n            if (glyph.arabicForm) {\n              this.isRTL = true;\n              this.isArabic = true;\n\n              if (typeof this.glyphs[glyph.unicode] === 'undefined') {\n                this.glyphs[glyph.unicode] = Object.create(null);\n              }\n\n              this.glyphs[glyph.unicode][glyph.arabicForm] = glyph;\n            } else {\n              this.glyphs[glyph.unicode] = glyph;\n            }\n\n            break;\n          }\n      }\n    }\n  }\n\n  render() {// NO RENDER\n  }\n\n}\n\nclass FontFaceElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font-face';\n    this.ascent = this.getAttribute('ascent').getNumber();\n    this.descent = this.getAttribute('descent').getNumber();\n    this.unitsPerEm = this.getAttribute('units-per-em').getNumber();\n  }\n\n}\n\nclass MissingGlyphElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'missing-glyph';\n    this.horizAdvX = 0;\n  }\n\n}\n\nclass TRefElement extends TextElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'tref';\n  }\n\n  getText() {\n    var element = this.getHrefAttribute().getDefinition();\n\n    if (element) {\n      var firstChild = element.children[0];\n\n      if (firstChild) {\n        return firstChild.getText();\n      }\n    }\n\n    return '';\n  }\n\n}\n\nclass AElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'a';\n    var {\n      childNodes\n    } = node;\n    var firstChild = childNodes[0];\n    var hasText = childNodes.length > 0 && Array.from(childNodes).every(node => node.nodeType === 3);\n    this.hasText = hasText;\n    this.text = hasText ? this.getTextFromNode(firstChild) : '';\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  renderChildren(ctx) {\n    if (this.hasText) {\n      // render as text element\n      super.renderChildren(ctx);\n      var {\n        document,\n        x,\n        y\n      } = this;\n      var {\n        mouse\n      } = document.screen;\n      var fontSize = new Property(document, 'fontSize', Font.parse(document.ctx.font).fontSize); // Do not calc bounding box if mouse is not working.\n\n      if (mouse.isWorking()) {\n        mouse.checkBoundingBox(this, new BoundingBox(x, y - fontSize.getPixels('y'), x + this.measureText(ctx), y));\n      }\n    } else if (this.children.length > 0) {\n      // render as temporary group\n      var g = new GElement(this.document, null);\n      g.children = this.children;\n      g.parent = this;\n      g.render(ctx);\n    }\n  }\n\n  onClick() {\n    var {\n      window\n    } = this.document;\n\n    if (window) {\n      window.open(this.getHrefAttribute().getString());\n    }\n  }\n\n  onMouseMove() {\n    var ctx = this.document.ctx;\n    ctx.canvas.style.cursor = 'pointer';\n  }\n\n}\n\nfunction ownKeys$2(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$2(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$2(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$2(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\nclass TextPathElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'textPath';\n    this.textWidth = 0;\n    this.textHeight = 0;\n    this.pathLength = -1;\n    this.glyphInfo = null;\n    this.letterSpacingCache = [];\n    this.measuresCache = new Map([['', 0]]);\n    var pathElement = this.getHrefAttribute().getDefinition();\n    this.text = this.getTextFromNode();\n    this.dataArray = this.parsePathData(pathElement);\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  path(ctx) {\n    var {\n      dataArray\n    } = this;\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    dataArray.forEach(_ref => {\n      var {\n        type,\n        points\n      } = _ref;\n\n      switch (type) {\n        case PathParser.LINE_TO:\n          if (ctx) {\n            ctx.lineTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.MOVE_TO:\n          if (ctx) {\n            ctx.moveTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.CURVE_TO:\n          if (ctx) {\n            ctx.bezierCurveTo(points[0], points[1], points[2], points[3], points[4], points[5]);\n          }\n\n          break;\n\n        case PathParser.QUAD_TO:\n          if (ctx) {\n            ctx.quadraticCurveTo(points[0], points[1], points[2], points[3]);\n          }\n\n          break;\n\n        case PathParser.ARC:\n          {\n            var [cx, cy, rx, ry, theta, dTheta, psi, fs] = points;\n            var r = rx > ry ? rx : ry;\n            var scaleX = rx > ry ? 1 : rx / ry;\n            var scaleY = rx > ry ? ry / rx : 1;\n\n            if (ctx) {\n              ctx.translate(cx, cy);\n              ctx.rotate(psi);\n              ctx.scale(scaleX, scaleY);\n              ctx.arc(0, 0, r, theta, theta + dTheta, Boolean(1 - fs));\n              ctx.scale(1 / scaleX, 1 / scaleY);\n              ctx.rotate(-psi);\n              ctx.translate(-cx, -cy);\n            }\n\n            break;\n          }\n\n        case PathParser.CLOSE_PATH:\n          if (ctx) {\n            ctx.closePath();\n          }\n\n          break;\n      }\n    });\n  }\n\n  renderChildren(ctx) {\n    this.setTextData(ctx);\n    ctx.save();\n    var textDecoration = this.parent.getStyle('text-decoration').getString();\n    var fontSize = this.getFontSize();\n    var {\n      glyphInfo\n    } = this;\n    var fill = ctx.fillStyle;\n\n    if (textDecoration === 'underline') {\n      ctx.beginPath();\n    }\n\n    glyphInfo.forEach((glyph, i) => {\n      var {\n        p0,\n        p1,\n        rotation,\n        text: partialText\n      } = glyph;\n      ctx.save();\n      ctx.translate(p0.x, p0.y);\n      ctx.rotate(rotation);\n\n      if (ctx.fillStyle) {\n        ctx.fillText(partialText, 0, 0);\n      }\n\n      if (ctx.strokeStyle) {\n        ctx.strokeText(partialText, 0, 0);\n      }\n\n      ctx.restore();\n\n      if (textDecoration === 'underline') {\n        if (i === 0) {\n          ctx.moveTo(p0.x, p0.y + fontSize / 8);\n        }\n\n        ctx.lineTo(p1.x, p1.y + fontSize / 5);\n      } // // To assist with debugging visually, uncomment following\n      //\n      // ctx.beginPath();\n      // if (i % 2)\n      // \tctx.strokeStyle = 'red';\n      // else\n      // \tctx.strokeStyle = 'green';\n      // ctx.moveTo(p0.x, p0.y);\n      // ctx.lineTo(p1.x, p1.y);\n      // ctx.stroke();\n      // ctx.closePath();\n\n    });\n\n    if (textDecoration === 'underline') {\n      ctx.lineWidth = fontSize / 20;\n      ctx.strokeStyle = fill;\n      ctx.stroke();\n      ctx.closePath();\n    }\n\n    ctx.restore();\n  }\n\n  getLetterSpacingAt() {\n    var idx = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return this.letterSpacingCache[idx] || 0;\n  }\n\n  findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, inputOffset, dy, c, charI) {\n    var offset = inputOffset;\n    var glyphWidth = this.measureText(ctx, c);\n\n    if (c === ' ' && anchor === 'justify' && textFullWidth < fullPathWidth) {\n      glyphWidth += (fullPathWidth - textFullWidth) / spacesNumber;\n    }\n\n    if (charI > -1) {\n      offset += this.getLetterSpacingAt(charI);\n    }\n\n    var splineStep = this.textHeight / 20;\n    var p0 = this.getEquidistantPointOnPath(offset, splineStep, 0);\n    var p1 = this.getEquidistantPointOnPath(offset + glyphWidth, splineStep, 0);\n    var segment = {\n      p0,\n      p1\n    };\n    var rotation = p0 && p1 ? Math.atan2(p1.y - p0.y, p1.x - p0.x) : 0;\n\n    if (dy) {\n      var dyX = Math.cos(Math.PI / 2 + rotation) * dy;\n      var dyY = Math.cos(-rotation) * dy;\n      segment.p0 = _objectSpread$2(_objectSpread$2({}, p0), {}, {\n        x: p0.x + dyX,\n        y: p0.y + dyY\n      });\n      segment.p1 = _objectSpread$2(_objectSpread$2({}, p1), {}, {\n        x: p1.x + dyX,\n        y: p1.y + dyY\n      });\n    }\n\n    offset += glyphWidth;\n    return {\n      offset,\n      segment,\n      rotation\n    };\n  }\n\n  measureText(ctx, text) {\n    var {\n      measuresCache\n    } = this;\n    var targetText = text || this.getText();\n\n    if (measuresCache.has(targetText)) {\n      return measuresCache.get(targetText);\n    }\n\n    var measure = this.measureTargetText(ctx, targetText);\n    measuresCache.set(targetText, measure);\n    return measure;\n  } // This method supposes what all custom fonts already loaded.\n  // If some font will be loaded after this method call, <textPath> will not be rendered correctly.\n  // You need to call this method manually to update glyphs cache.\n\n\n  setTextData(ctx) {\n    if (this.glyphInfo) {\n      return;\n    }\n\n    var renderText = this.getText();\n    var chars = renderText.split('');\n    var spacesNumber = renderText.split(' ').length - 1;\n    var dx = this.parent.getAttribute('dx').split().map(_ => _.getPixels('x'));\n    var dy = this.parent.getAttribute('dy').getPixels('y');\n    var anchor = this.parent.getStyle('text-anchor').getString('start');\n    var thisSpacing = this.getStyle('letter-spacing');\n    var parentSpacing = this.parent.getStyle('letter-spacing');\n    var letterSpacing = 0;\n\n    if (!thisSpacing.hasValue() || thisSpacing.getValue() === 'inherit') {\n      letterSpacing = parentSpacing.getPixels();\n    } else if (thisSpacing.hasValue()) {\n      if (thisSpacing.getValue() !== 'initial' && thisSpacing.getValue() !== 'unset') {\n        letterSpacing = thisSpacing.getPixels();\n      }\n    } // fill letter-spacing cache\n\n\n    var letterSpacingCache = [];\n    var textLen = renderText.length;\n    this.letterSpacingCache = letterSpacingCache;\n\n    for (var i = 0; i < textLen; i++) {\n      letterSpacingCache.push(typeof dx[i] !== 'undefined' ? dx[i] : letterSpacing);\n    }\n\n    var dxSum = letterSpacingCache.reduce((acc, cur, i) => i === 0 ? 0 : acc + cur || 0, 0);\n    var textWidth = this.measureText(ctx);\n    var textFullWidth = Math.max(textWidth + dxSum, 0);\n    this.textWidth = textWidth;\n    this.textHeight = this.getFontSize();\n    this.glyphInfo = [];\n    var fullPathWidth = this.getPathLength();\n    var startOffset = this.getStyle('startOffset').getNumber(0) * fullPathWidth;\n    var offset = 0;\n\n    if (anchor === 'middle' || anchor === 'center') {\n      offset = -textFullWidth / 2;\n    }\n\n    if (anchor === 'end' || anchor === 'right') {\n      offset = -textFullWidth;\n    }\n\n    offset += startOffset;\n    chars.forEach((char, i) => {\n      // Find such segment what distance between p0 and p1 is approx. width of glyph\n      var {\n        offset: nextOffset,\n        segment,\n        rotation\n      } = this.findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, offset, dy, char, i);\n      offset = nextOffset;\n\n      if (!segment.p0 || !segment.p1) {\n        return;\n      } // const width = this.getLineLength(\n      // \tsegment.p0.x,\n      // \tsegment.p0.y,\n      // \tsegment.p1.x,\n      // \tsegment.p1.y\n      // );\n      // Note: Since glyphs are rendered one at a time, any kerning pair data built into the font will not be used.\n      // Can foresee having a rough pair table built in that the developer can override as needed.\n      // Or use \"dx\" attribute of the <text> node as a naive replacement\n      // const kern = 0;\n      // placeholder for future implementation\n      // const midpoint = this.getPointOnLine(\n      // \tkern + width / 2.0,\n      // \tsegment.p0.x, segment.p0.y, segment.p1.x, segment.p1.y\n      // );\n\n\n      this.glyphInfo.push({\n        // transposeX: midpoint.x,\n        // transposeY: midpoint.y,\n        text: chars[i],\n        p0: segment.p0,\n        p1: segment.p1,\n        rotation\n      });\n    });\n  }\n\n  parsePathData(path) {\n    this.pathLength = -1; // reset path length\n\n    if (!path) {\n      return [];\n    }\n\n    var pathCommands = [];\n    var {\n      pathParser\n    } = path;\n    pathParser.reset(); // convert l, H, h, V, and v to L\n\n    while (!pathParser.isEnd()) {\n      var {\n        current\n      } = pathParser;\n      var startX = current ? current.x : 0;\n      var startY = current ? current.y : 0;\n      var command = pathParser.next();\n      var nextCommandType = command.type;\n      var points = [];\n\n      switch (command.type) {\n        case PathParser.MOVE_TO:\n          this.pathM(pathParser, points);\n          break;\n\n        case PathParser.LINE_TO:\n          nextCommandType = this.pathL(pathParser, points);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          nextCommandType = this.pathH(pathParser, points);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          nextCommandType = this.pathV(pathParser, points);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          nextCommandType = this.pathS(pathParser, points);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          nextCommandType = this.pathT(pathParser, points);\n          break;\n\n        case PathParser.ARC:\n          points = this.pathA(pathParser);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          PathElement.pathZ(pathParser);\n          break;\n      }\n\n      if (command.type !== PathParser.CLOSE_PATH) {\n        pathCommands.push({\n          type: nextCommandType,\n          points,\n          start: {\n            x: startX,\n            y: startY\n          },\n          pathLength: this.calcLength(startX, startY, nextCommandType, points)\n        });\n      } else {\n        pathCommands.push({\n          type: PathParser.CLOSE_PATH,\n          points: [],\n          pathLength: 0\n        });\n      }\n    }\n\n    return pathCommands;\n  }\n\n  pathM(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathM(pathParser).point;\n    points.push(x, y);\n  }\n\n  pathL(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathL(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathH(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathH(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathV(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathV(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathC(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathS(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.CURVE_TO;\n  }\n\n  pathQ(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathT(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.QUAD_TO;\n  }\n\n  pathA(pathParser) {\n    var {\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser);\n\n    if (sweepFlag === 0 && ad > 0) {\n      ad -= 2 * Math.PI;\n    }\n\n    if (sweepFlag === 1 && ad < 0) {\n      ad += 2 * Math.PI;\n    }\n\n    return [centp.x, centp.y, rX, rY, a1, ad, xAxisRotation, sweepFlag];\n  }\n\n  calcLength(x, y, commandType, points) {\n    var len = 0;\n    var p1 = null;\n    var p2 = null;\n    var t = 0;\n\n    switch (commandType) {\n      case PathParser.LINE_TO:\n        return this.getLineLength(x, y, points[0], points[1]);\n\n      case PathParser.CURVE_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnCubicBezier(0, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnCubicBezier(t, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.QUAD_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnQuadraticBezier(0, x, y, points[0], points[1], points[2], points[3]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnQuadraticBezier(t, x, y, points[0], points[1], points[2], points[3]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.ARC:\n        {\n          // Approximates by breaking curve into line segments\n          len = 0.0;\n          var start = points[4]; // 4 = theta\n\n          var dTheta = points[5]; // 5 = dTheta\n\n          var end = points[4] + dTheta;\n          var inc = Math.PI / 180.0; // 1 degree resolution\n\n          if (Math.abs(start - end) < inc) {\n            inc = Math.abs(start - end);\n          } // Note: for purpose of calculating arc length, not going to worry about rotating X-axis by angle psi\n\n\n          p1 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], start, 0);\n\n          if (dTheta < 0) {\n            // clockwise\n            for (t = start - inc; t > end; t -= inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          } else {\n            // counter-clockwise\n            for (t = start + inc; t < end; t += inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          }\n\n          p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], end, 0);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          return len;\n        }\n    }\n\n    return 0;\n  }\n\n  getPointOnLine(dist, p1x, p1y, p2x, p2y) {\n    var fromX = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : p1x;\n    var fromY = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : p1y;\n    var m = (p2y - p1y) / (p2x - p1x + PSEUDO_ZERO);\n    var run = Math.sqrt(dist * dist / (1 + m * m));\n\n    if (p2x < p1x) {\n      run *= -1;\n    }\n\n    var rise = m * run;\n    var pt = null;\n\n    if (p2x === p1x) {\n      // vertical line\n      pt = {\n        x: fromX,\n        y: fromY + rise\n      };\n    } else if ((fromY - p1y) / (fromX - p1x + PSEUDO_ZERO) === m) {\n      pt = {\n        x: fromX + run,\n        y: fromY + rise\n      };\n    } else {\n      var ix = 0;\n      var iy = 0;\n      var len = this.getLineLength(p1x, p1y, p2x, p2y);\n\n      if (len < PSEUDO_ZERO) {\n        return null;\n      }\n\n      var u = (fromX - p1x) * (p2x - p1x) + (fromY - p1y) * (p2y - p1y);\n      u /= len * len;\n      ix = p1x + u * (p2x - p1x);\n      iy = p1y + u * (p2y - p1y);\n      var pRise = this.getLineLength(fromX, fromY, ix, iy);\n      var pRun = Math.sqrt(dist * dist - pRise * pRise);\n      run = Math.sqrt(pRun * pRun / (1 + m * m));\n\n      if (p2x < p1x) {\n        run *= -1;\n      }\n\n      rise = m * run;\n      pt = {\n        x: ix + run,\n        y: iy + rise\n      };\n    }\n\n    return pt;\n  }\n\n  getPointOnPath(distance) {\n    var fullLen = this.getPathLength();\n    var cumulativePathLength = 0;\n    var p = null;\n\n    if (distance < -0.00005 || distance - 0.00005 > fullLen) {\n      return null;\n    }\n\n    var {\n      dataArray\n    } = this;\n\n    for (var command of dataArray) {\n      if (command && (command.pathLength < 0.00005 || cumulativePathLength + command.pathLength + 0.00005 < distance)) {\n        cumulativePathLength += command.pathLength;\n        continue;\n      }\n\n      var delta = distance - cumulativePathLength;\n      var currentT = 0;\n\n      switch (command.type) {\n        case PathParser.LINE_TO:\n          p = this.getPointOnLine(delta, command.start.x, command.start.y, command.points[0], command.points[1], command.start.x, command.start.y);\n          break;\n\n        case PathParser.ARC:\n          {\n            var start = command.points[4]; // 4 = theta\n\n            var dTheta = command.points[5]; // 5 = dTheta\n\n            var end = command.points[4] + dTheta;\n            currentT = start + delta / command.pathLength * dTheta;\n\n            if (dTheta < 0 && currentT < end || dTheta >= 0 && currentT > end) {\n              break;\n            }\n\n            p = this.getPointOnEllipticalArc(command.points[0], command.points[1], command.points[2], command.points[3], currentT, command.points[6]);\n            break;\n          }\n\n        case PathParser.CURVE_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnCubicBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3], command.points[4], command.points[5]);\n          break;\n\n        case PathParser.QUAD_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnQuadraticBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3]);\n          break;\n      }\n\n      if (p) {\n        return p;\n      }\n\n      break;\n    }\n\n    return null;\n  }\n\n  getLineLength(x1, y1, x2, y2) {\n    return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n  }\n\n  getPathLength() {\n    if (this.pathLength === -1) {\n      this.pathLength = this.dataArray.reduce((length, command) => command.pathLength > 0 ? length + command.pathLength : length, 0);\n    }\n\n    return this.pathLength;\n  }\n\n  getPointOnCubicBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y, p4x, p4y) {\n    var x = p4x * CB1(pct) + p3x * CB2(pct) + p2x * CB3(pct) + p1x * CB4(pct);\n    var y = p4y * CB1(pct) + p3y * CB2(pct) + p2y * CB3(pct) + p1y * CB4(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnQuadraticBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y) {\n    var x = p3x * QB1(pct) + p2x * QB2(pct) + p1x * QB3(pct);\n    var y = p3y * QB1(pct) + p2y * QB2(pct) + p1y * QB3(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnEllipticalArc(cx, cy, rx, ry, theta, psi) {\n    var cosPsi = Math.cos(psi);\n    var sinPsi = Math.sin(psi);\n    var pt = {\n      x: rx * Math.cos(theta),\n      y: ry * Math.sin(theta)\n    };\n    return {\n      x: cx + (pt.x * cosPsi - pt.y * sinPsi),\n      y: cy + (pt.x * sinPsi + pt.y * cosPsi)\n    };\n  } // TODO need some optimisations. possibly build cache only for curved segments?\n\n\n  buildEquidistantCache(inputStep, inputPrecision) {\n    var fullLen = this.getPathLength();\n    var precision = inputPrecision || 0.25; // accuracy vs performance\n\n    var step = inputStep || fullLen / 100;\n\n    if (!this.equidistantCache || this.equidistantCache.step !== step || this.equidistantCache.precision !== precision) {\n      // Prepare cache\n      this.equidistantCache = {\n        step,\n        precision,\n        points: []\n      }; // Calculate points\n\n      var s = 0;\n\n      for (var l = 0; l <= fullLen; l += precision) {\n        var p0 = this.getPointOnPath(l);\n        var p1 = this.getPointOnPath(l + precision);\n\n        if (!p0 || !p1) {\n          continue;\n        }\n\n        s += this.getLineLength(p0.x, p0.y, p1.x, p1.y);\n\n        if (s >= step) {\n          this.equidistantCache.points.push({\n            x: p0.x,\n            y: p0.y,\n            distance: l\n          });\n          s -= step;\n        }\n      }\n    }\n  }\n\n  getEquidistantPointOnPath(targetDistance, step, precision) {\n    this.buildEquidistantCache(step, precision);\n\n    if (targetDistance < 0 || targetDistance - this.getPathLength() > 0.00005) {\n      return null;\n    }\n\n    var idx = Math.round(targetDistance / this.getPathLength() * (this.equidistantCache.points.length - 1));\n    return this.equidistantCache.points[idx] || null;\n  }\n\n}\n\nvar dataUriRegex = /^\\s*data:(([^/,;]+\\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;\nclass ImageElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'image';\n    this.loaded = false;\n    var href = this.getHrefAttribute().getString();\n\n    if (!href) {\n      return;\n    }\n\n    var isSvg = href.endsWith('.svg') || /^\\s*data:image\\/svg\\+xml/i.test(href);\n    document.images.push(this);\n\n    if (!isSvg) {\n      void this.loadImage(href);\n    } else {\n      void this.loadSvg(href);\n    }\n\n    this.isSvg = isSvg;\n  }\n\n  loadImage(href) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        var image = yield _this.document.createImage(href);\n        _this.image = image;\n      } catch (err) {\n        console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n  loadSvg(href) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var match = dataUriRegex.exec(href);\n\n      if (match) {\n        var data = match[5];\n\n        if (match[4] === 'base64') {\n          _this2.image = atob(data);\n        } else {\n          _this2.image = decodeURIComponent(data);\n        }\n      } else {\n        try {\n          var response = yield _this2.document.fetch(href);\n          var svg = yield response.text();\n          _this2.image = svg;\n        } catch (err) {\n          console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n        }\n      }\n\n      _this2.loaded = true;\n    })();\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      image,\n      loaded\n    } = this;\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!loaded || !image || !width || !height) {\n      return;\n    }\n\n    ctx.save();\n    ctx.translate(x, y);\n\n    if (this.isSvg) {\n      var subDocument = document.canvg.forkString(ctx, this.image, {\n        ignoreMouse: true,\n        ignoreAnimation: true,\n        ignoreDimensions: true,\n        ignoreClear: true,\n        offsetX: 0,\n        offsetY: 0,\n        scaleWidth: width,\n        scaleHeight: height\n      });\n      subDocument.document.documentElement.parent = this;\n      void subDocument.render();\n    } else {\n      var _image = this.image;\n      document.setViewBox({\n        ctx,\n        aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n        width,\n        desiredWidth: _image.width,\n        height,\n        desiredHeight: _image.height\n      });\n\n      if (this.loaded) {\n        if (typeof _image.complete === 'undefined' || _image.complete) {\n          ctx.drawImage(_image, 0, 0);\n        }\n      }\n    }\n\n    ctx.restore();\n  }\n\n  getBoundingBox() {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n}\n\nclass SymbolElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'symbol';\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass SVGFontLoader {\n  constructor(document) {\n    this.document = document;\n    this.loaded = false;\n    document.fonts.push(this);\n  }\n\n  load(fontFamily, url) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        var {\n          document\n        } = _this;\n        var svgDocument = yield document.canvg.parser.load(url);\n        var fonts = svgDocument.getElementsByTagName('font');\n        Array.from(fonts).forEach(fontNode => {\n          var font = document.createElement(fontNode);\n          document.definitions[fontFamily] = font;\n        });\n      } catch (err) {\n        console.error(\"Error while loading font \\\"\".concat(url, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n}\n\nclass StyleElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'style';\n    var css = compressSpaces(Array.from(node.childNodes) // NEED TEST\n    .map(_ => _.textContent).join('').replace(/(\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*+\\/)|(^[\\s]*\\/\\/.*)/gm, '') // remove comments\n    .replace(/@import.*;/g, '') // remove imports\n    );\n    var cssDefs = css.split('}');\n    cssDefs.forEach(_ => {\n      var def = _.trim();\n\n      if (!def) {\n        return;\n      }\n\n      var cssParts = def.split('{');\n      var cssClasses = cssParts[0].split(',');\n      var cssProps = cssParts[1].split(';');\n      cssClasses.forEach(_ => {\n        var cssClass = _.trim();\n\n        if (!cssClass) {\n          return;\n        }\n\n        var props = document.styles[cssClass] || {};\n        cssProps.forEach(cssProp => {\n          var prop = cssProp.indexOf(':');\n          var name = cssProp.substr(0, prop).trim();\n          var value = cssProp.substr(prop + 1, cssProp.length - prop).trim();\n\n          if (name && value) {\n            props[name] = new Property(document, name, value);\n          }\n        });\n        document.styles[cssClass] = props;\n        document.stylesSpecificity[cssClass] = getSelectorSpecificity(cssClass);\n\n        if (cssClass === '@font-face') {\n          //  && !nodeEnv\n          var fontFamily = props['font-family'].getString().replace(/\"|'/g, '');\n          var srcs = props.src.getString().split(',');\n          srcs.forEach(src => {\n            if (src.indexOf('format(\"svg\")') > 0) {\n              var url = parseExternalUrl(src);\n\n              if (url) {\n                void new SVGFontLoader(document).load(fontFamily, url);\n              }\n            }\n          });\n        }\n      });\n    });\n  }\n\n}\nStyleElement.parseExternalUrl = parseExternalUrl;\n\nclass UseElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'use';\n  }\n\n  setContext(ctx) {\n    super.setContext(ctx);\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue()) {\n      ctx.translate(xAttr.getPixels('x'), 0);\n    }\n\n    if (yAttr.hasValue()) {\n      ctx.translate(0, yAttr.getPixels('y'));\n    }\n  }\n\n  path(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      element.path(ctx);\n    }\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      element\n    } = this;\n\n    if (element) {\n      var tempSvg = element;\n\n      if (element.type === 'symbol') {\n        // render me using a temporary svg element in symbol cases (http://www.w3.org/TR/SVG/struct.html#UseElement)\n        tempSvg = new SVGElement(document, null);\n        tempSvg.attributes.viewBox = new Property(document, 'viewBox', element.getAttribute('viewBox').getString());\n        tempSvg.attributes.preserveAspectRatio = new Property(document, 'preserveAspectRatio', element.getAttribute('preserveAspectRatio').getString());\n        tempSvg.attributes.overflow = new Property(document, 'overflow', element.getAttribute('overflow').getString());\n        tempSvg.children = element.children; // element is still the parent of the children\n\n        element.styles.opacity = new Property(document, 'opacity', this.calculateOpacity());\n      }\n\n      if (tempSvg.type === 'svg') {\n        var widthStyle = this.getStyle('width', false, true);\n        var heightStyle = this.getStyle('height', false, true); // if symbol or svg, inherit width/height from me\n\n        if (widthStyle.hasValue()) {\n          tempSvg.attributes.width = new Property(document, 'width', widthStyle.getString());\n        }\n\n        if (heightStyle.hasValue()) {\n          tempSvg.attributes.height = new Property(document, 'height', heightStyle.getString());\n        }\n      }\n\n      var oldParent = tempSvg.parent;\n      tempSvg.parent = this;\n      tempSvg.render(ctx);\n      tempSvg.parent = oldParent;\n    }\n  }\n\n  getBoundingBox(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      return element.getBoundingBox(ctx);\n    }\n\n    return null;\n  }\n\n  elementTransform() {\n    var {\n      document,\n      element\n    } = this;\n    return Transform.fromElement(document, element);\n  }\n\n  get element() {\n    if (!this.cachedElement) {\n      this.cachedElement = this.getHrefAttribute().getDefinition();\n    }\n\n    return this.cachedElement;\n  }\n\n}\n\nfunction imGet(img, x, y, width, _height, rgba) {\n  return img[y * width * 4 + x * 4 + rgba];\n}\n\nfunction imSet(img, x, y, width, _height, rgba, val) {\n  img[y * width * 4 + x * 4 + rgba] = val;\n}\n\nfunction m(matrix, i, v) {\n  var mi = matrix[i];\n  return mi * v;\n}\n\nfunction c(a, m1, m2, m3) {\n  return m1 + Math.cos(a) * m2 + Math.sin(a) * m3;\n}\n\nclass FeColorMatrixElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feColorMatrix';\n    var matrix = toNumbers(this.getAttribute('values').getString());\n\n    switch (this.getAttribute('type').getString('matrix')) {\n      // http://www.w3.org/TR/SVG/filters.html#feColorMatrixElement\n      case 'saturate':\n        {\n          var s = matrix[0];\n          /* eslint-disable array-element-newline */\n\n          matrix = [0.213 + 0.787 * s, 0.715 - 0.715 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 + 0.285 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 - 0.715 * s, 0.072 + 0.928 * s, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'hueRotate':\n        {\n          var a = matrix[0] * Math.PI / 180.0;\n          /* eslint-disable array-element-newline */\n\n          matrix = [c(a, 0.213, 0.787, -0.213), c(a, 0.715, -0.715, -0.715), c(a, 0.072, -0.072, 0.928), 0, 0, c(a, 0.213, -0.213, 0.143), c(a, 0.715, 0.285, 0.140), c(a, 0.072, -0.072, -0.283), 0, 0, c(a, 0.213, -0.213, -0.787), c(a, 0.715, -0.715, 0.715), c(a, 0.072, 0.928, 0.072), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'luminanceToAlpha':\n        /* eslint-disable array-element-newline */\n        matrix = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2125, 0.7154, 0.0721, 0, 0, 0, 0, 0, 0, 1];\n        /* eslint-enable array-element-newline */\n\n        break;\n    }\n\n    this.matrix = matrix;\n    this.includeOpacity = this.getAttribute('includeOpacity').hasValue();\n  }\n\n  apply(ctx, _x, _y, width, height) {\n    // assuming x==0 && y==0 for now\n    var {\n      includeOpacity,\n      matrix\n    } = this;\n    var srcData = ctx.getImageData(0, 0, width, height);\n\n    for (var y = 0; y < height; y++) {\n      for (var x = 0; x < width; x++) {\n        var r = imGet(srcData.data, x, y, width, height, 0);\n        var g = imGet(srcData.data, x, y, width, height, 1);\n        var b = imGet(srcData.data, x, y, width, height, 2);\n        var a = imGet(srcData.data, x, y, width, height, 3);\n        var nr = m(matrix, 0, r) + m(matrix, 1, g) + m(matrix, 2, b) + m(matrix, 3, a) + m(matrix, 4, 1);\n        var ng = m(matrix, 5, r) + m(matrix, 6, g) + m(matrix, 7, b) + m(matrix, 8, a) + m(matrix, 9, 1);\n        var nb = m(matrix, 10, r) + m(matrix, 11, g) + m(matrix, 12, b) + m(matrix, 13, a) + m(matrix, 14, 1);\n        var na = m(matrix, 15, r) + m(matrix, 16, g) + m(matrix, 17, b) + m(matrix, 18, a) + m(matrix, 19, 1);\n\n        if (includeOpacity) {\n          nr = 0;\n          ng = 0;\n          nb = 0;\n          na *= a / 255;\n        }\n\n        imSet(srcData.data, x, y, width, height, 0, nr);\n        imSet(srcData.data, x, y, width, height, 1, ng);\n        imSet(srcData.data, x, y, width, height, 2, nb);\n        imSet(srcData.data, x, y, width, height, 3, na);\n      }\n    }\n\n    ctx.clearRect(0, 0, width, height);\n    ctx.putImageData(srcData, 0, 0);\n  }\n\n}\n\nclass MaskElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'mask';\n  }\n\n  apply(ctx, element) {\n    var {\n      document\n    } = this; // render as temp svg\n\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!width && !height) {\n      var boundingBox = new BoundingBox();\n      this.children.forEach(child => {\n        boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n      });\n      x = Math.floor(boundingBox.x1);\n      y = Math.floor(boundingBox.y1);\n      width = Math.floor(boundingBox.width);\n      height = Math.floor(boundingBox.height);\n    }\n\n    var ignoredStyles = this.removeStyles(element, MaskElement.ignoreStyles);\n    var maskCanvas = document.createCanvas(x + width, y + height);\n    var maskCtx = maskCanvas.getContext('2d');\n    document.screen.setDefaults(maskCtx);\n    this.renderChildren(maskCtx); // convert mask to alpha with a fake node\n    // TODO: refactor out apply from feColorMatrix\n\n    new FeColorMatrixElement(document, {\n      nodeType: 1,\n      childNodes: [],\n      attributes: [{\n        nodeName: 'type',\n        value: 'luminanceToAlpha'\n      }, {\n        nodeName: 'includeOpacity',\n        value: 'true'\n      }]\n    }).apply(maskCtx, 0, 0, x + width, y + height);\n    var tmpCanvas = document.createCanvas(x + width, y + height);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    element.render(tmpCtx);\n    tmpCtx.globalCompositeOperation = 'destination-in';\n    tmpCtx.fillStyle = maskCtx.createPattern(maskCanvas, 'no-repeat');\n    tmpCtx.fillRect(0, 0, x + width, y + height);\n    ctx.fillStyle = tmpCtx.createPattern(tmpCanvas, 'no-repeat');\n    ctx.fillRect(0, 0, x + width, y + height); // reassign mask\n\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nMaskElement.ignoreStyles = ['mask', 'transform', 'clip-path'];\n\nvar noop = () => {// NOOP\n};\n\nclass ClipPathElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'clipPath';\n  }\n\n  apply(ctx) {\n    var {\n      document\n    } = this;\n    var contextProto = Reflect.getPrototypeOf(ctx);\n    var {\n      beginPath,\n      closePath\n    } = ctx;\n\n    if (contextProto) {\n      contextProto.beginPath = noop;\n      contextProto.closePath = noop;\n    }\n\n    Reflect.apply(beginPath, ctx, []);\n    this.children.forEach(child => {\n      if (typeof child.path === 'undefined') {\n        return;\n      }\n\n      var transform = typeof child.elementTransform !== 'undefined' ? child.elementTransform() : null; // handle <use />\n\n      if (!transform) {\n        transform = Transform.fromElement(document, child);\n      }\n\n      if (transform) {\n        transform.apply(ctx);\n      }\n\n      child.path(ctx);\n\n      if (contextProto) {\n        contextProto.closePath = closePath;\n      }\n\n      if (transform) {\n        transform.unapply(ctx);\n      }\n    });\n    Reflect.apply(closePath, ctx, []);\n    ctx.clip();\n\n    if (contextProto) {\n      contextProto.beginPath = beginPath;\n      contextProto.closePath = closePath;\n    }\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass FilterElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'filter';\n  }\n\n  apply(ctx, element) {\n    // render as temp svg\n    var {\n      document,\n      children\n    } = this;\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return;\n    }\n\n    var px = 0;\n    var py = 0;\n    children.forEach(child => {\n      var efd = child.extraFilterDistance || 0;\n      px = Math.max(px, efd);\n      py = Math.max(py, efd);\n    });\n    var width = Math.floor(boundingBox.width);\n    var height = Math.floor(boundingBox.height);\n    var tmpCanvasWidth = width + 2 * px;\n    var tmpCanvasHeight = height + 2 * py;\n\n    if (tmpCanvasWidth < 1 || tmpCanvasHeight < 1) {\n      return;\n    }\n\n    var x = Math.floor(boundingBox.x);\n    var y = Math.floor(boundingBox.y);\n    var ignoredStyles = this.removeStyles(element, FilterElement.ignoreStyles);\n    var tmpCanvas = document.createCanvas(tmpCanvasWidth, tmpCanvasHeight);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    tmpCtx.translate(-x + px, -y + py);\n    element.render(tmpCtx); // apply filters\n\n    children.forEach(child => {\n      if (typeof child.apply === 'function') {\n        child.apply(tmpCtx, 0, 0, tmpCanvasWidth, tmpCanvasHeight);\n      }\n    }); // render on me\n\n    ctx.drawImage(tmpCanvas, 0, 0, tmpCanvasWidth, tmpCanvasHeight, x - px, y - py, tmpCanvasWidth, tmpCanvasHeight);\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nFilterElement.ignoreStyles = ['filter', 'transform', 'clip-path'];\n\nclass FeDropShadowElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feDropShadow';\n    this.addStylesFromStyleDefinition();\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeMorphologyElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feMorphology';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeCompositeElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feComposite';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeGaussianBlurElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feGaussianBlur';\n    this.blurRadius = Math.floor(this.getAttribute('stdDeviation').getNumber());\n    this.extraFilterDistance = this.blurRadius;\n  }\n\n  apply(ctx, x, y, width, height) {\n    var {\n      document,\n      blurRadius\n    } = this;\n    var body = document.window ? document.window.document.body : null;\n    var canvas = ctx.canvas; // StackBlur requires canvas be on document\n\n    canvas.id = document.getUniqueId();\n\n    if (body) {\n      canvas.style.display = 'none';\n      body.appendChild(canvas);\n    }\n\n    canvasRGBA(canvas, x, y, width, height, blurRadius);\n\n    if (body) {\n      body.removeChild(canvas);\n    }\n  }\n\n}\n\nclass TitleElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'title';\n  }\n\n}\n\nclass DescElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'desc';\n  }\n\n}\n\nvar elements = {\n  'svg': SVGElement,\n  'rect': RectElement,\n  'circle': CircleElement,\n  'ellipse': EllipseElement,\n  'line': LineElement,\n  'polyline': PolylineElement,\n  'polygon': PolygonElement,\n  'path': PathElement,\n  'pattern': PatternElement,\n  'marker': MarkerElement,\n  'defs': DefsElement,\n  'linearGradient': LinearGradientElement,\n  'radialGradient': RadialGradientElement,\n  'stop': StopElement,\n  'animate': AnimateElement,\n  'animateColor': AnimateColorElement,\n  'animateTransform': AnimateTransformElement,\n  'font': FontElement,\n  'font-face': FontFaceElement,\n  'missing-glyph': MissingGlyphElement,\n  'glyph': GlyphElement,\n  'text': TextElement,\n  'tspan': TSpanElement,\n  'tref': TRefElement,\n  'a': AElement,\n  'textPath': TextPathElement,\n  'image': ImageElement,\n  'g': GElement,\n  'symbol': SymbolElement,\n  'style': StyleElement,\n  'use': UseElement,\n  'mask': MaskElement,\n  'clipPath': ClipPathElement,\n  'filter': FilterElement,\n  'feDropShadow': FeDropShadowElement,\n  'feMorphology': FeMorphologyElement,\n  'feComposite': FeCompositeElement,\n  'feColorMatrix': FeColorMatrixElement,\n  'feGaussianBlur': FeGaussianBlurElement,\n  'title': TitleElement,\n  'desc': DescElement\n};\n\nfunction ownKeys$1(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$1(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$1(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$1(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction createCanvas(width, height) {\n  var canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\n\nfunction createImage(_x) {\n  return _createImage.apply(this, arguments);\n}\n\nfunction _createImage() {\n  _createImage = _asyncToGenerator(function* (src) {\n    var anonymousCrossOrigin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var image = document.createElement('img');\n\n    if (anonymousCrossOrigin) {\n      image.crossOrigin = 'Anonymous';\n    }\n\n    return new Promise((resolve, reject) => {\n      image.onload = () => {\n        resolve(image);\n      };\n\n      image.onerror = (_event, _source, _lineno, _colno, error) => {\n        reject(error);\n      };\n\n      image.src = src;\n    });\n  });\n  return _createImage.apply(this, arguments);\n}\n\nclass Document {\n  constructor(canvg) {\n    var {\n      rootEmSize = 12,\n      emSize = 12,\n      createCanvas = Document.createCanvas,\n      createImage = Document.createImage,\n      anonymousCrossOrigin\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.canvg = canvg;\n    this.definitions = Object.create(null);\n    this.styles = Object.create(null);\n    this.stylesSpecificity = Object.create(null);\n    this.images = [];\n    this.fonts = [];\n    this.emSizeStack = [];\n    this.uniqueId = 0;\n    this.screen = canvg.screen;\n    this.rootEmSize = rootEmSize;\n    this.emSize = emSize;\n    this.createCanvas = createCanvas;\n    this.createImage = this.bindCreateImage(createImage, anonymousCrossOrigin);\n    this.screen.wait(this.isImagesLoaded.bind(this));\n    this.screen.wait(this.isFontsLoaded.bind(this));\n  }\n\n  bindCreateImage(createImage, anonymousCrossOrigin) {\n    if (typeof anonymousCrossOrigin === 'boolean') {\n      return (source, forceAnonymousCrossOrigin) => createImage(source, typeof forceAnonymousCrossOrigin === 'boolean' ? forceAnonymousCrossOrigin : anonymousCrossOrigin);\n    }\n\n    return createImage;\n  }\n\n  get window() {\n    return this.screen.window;\n  }\n\n  get fetch() {\n    return this.screen.fetch;\n  }\n\n  get ctx() {\n    return this.screen.ctx;\n  }\n\n  get emSize() {\n    var {\n      emSizeStack\n    } = this;\n    return emSizeStack[emSizeStack.length - 1];\n  }\n\n  set emSize(value) {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.push(value);\n  }\n\n  popEmSize() {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.pop();\n  }\n\n  getUniqueId() {\n    return \"canvg\".concat(++this.uniqueId);\n  }\n\n  isImagesLoaded() {\n    return this.images.every(_ => _.loaded);\n  }\n\n  isFontsLoaded() {\n    return this.fonts.every(_ => _.loaded);\n  }\n\n  createDocumentElement(document) {\n    var documentElement = this.createElement(document.documentElement);\n    documentElement.root = true;\n    documentElement.addStylesFromStyleDefinition();\n    this.documentElement = documentElement;\n    return documentElement;\n  }\n\n  createElement(node) {\n    var elementType = node.nodeName.replace(/^[^:]+:/, '');\n    var ElementType = Document.elementTypes[elementType];\n\n    if (typeof ElementType !== 'undefined') {\n      return new ElementType(this, node);\n    }\n\n    return new UnknownElement(this, node);\n  }\n\n  createTextNode(node) {\n    return new TextNode(this, node);\n  }\n\n  setViewBox(config) {\n    this.screen.setViewBox(_objectSpread$1({\n      document: this\n    }, config));\n  }\n\n}\nDocument.createCanvas = createCanvas;\nDocument.createImage = createImage;\nDocument.elementTypes = elements;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n/**\r\n * SVG renderer on canvas.\r\n */\n\nclass Canvg {\n  /**\r\n   * Main constructor.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG Document.\r\n   * @param options - Rendering options.\r\n   */\n  constructor(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.parser = new Parser(options);\n    this.screen = new Screen(ctx, options);\n    this.options = options;\n    var document = new Document(this, options);\n    var documentElement = document.createDocumentElement(svg);\n    this.document = document;\n    this.documentElement = documentElement;\n  }\n  /**\r\n   * Create Canvg instance from SVG source string or URL.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static from(ctx, svg) {\n    var _arguments = arguments;\n    return _asyncToGenerator(function* () {\n      var options = _arguments.length > 2 && _arguments[2] !== undefined ? _arguments[2] : {};\n      var parser = new Parser(options);\n      var svgDocument = yield parser.parse(svg);\n      return new Canvg(ctx, svgDocument, options);\n    })();\n  }\n  /**\r\n   * Create Canvg instance from SVG source string.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static fromString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var parser = new Parser(options);\n    var svgDocument = parser.parseFromString(svg);\n    return new Canvg(ctx, svgDocument, options);\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  fork(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.from(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  forkString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.fromString(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Document is ready promise.\r\n   * @returns Ready promise.\r\n   */\n\n\n  ready() {\n    return this.screen.ready();\n  }\n  /**\r\n   * Document is ready value.\r\n   * @returns Is ready or not.\r\n   */\n\n\n  isReady() {\n    return this.screen.isReady();\n  }\n  /**\r\n   * Render only first frame, ignoring animations and mouse.\r\n   * @param options - Rendering options.\r\n   */\n\n\n  render() {\n    var _arguments2 = arguments,\n        _this = this;\n\n    return _asyncToGenerator(function* () {\n      var options = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : {};\n\n      _this.start(_objectSpread({\n        enableRedraw: true,\n        ignoreAnimation: true,\n        ignoreMouse: true\n      }, options));\n\n      yield _this.ready();\n\n      _this.stop();\n    })();\n  }\n  /**\r\n   * Start rendering.\r\n   * @param options - Render options.\r\n   */\n\n\n  start() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var {\n      documentElement,\n      screen,\n      options: baseOptions\n    } = this;\n    screen.start(documentElement, _objectSpread(_objectSpread({\n      enableRedraw: true\n    }, baseOptions), options));\n  }\n  /**\r\n   * Stop rendering.\r\n   */\n\n\n  stop() {\n    this.screen.stop();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.documentElement.resize(width, height, preserveAspectRatio);\n  }\n\n}\n\nexport { AElement, AnimateColorElement, AnimateElement, AnimateTransformElement, BoundingBox, CB1, CB2, CB3, CB4, Canvg, CircleElement, ClipPathElement, DefsElement, DescElement, Document, Element, EllipseElement, FeColorMatrixElement, FeCompositeElement, FeDropShadowElement, FeGaussianBlurElement, FeMorphologyElement, FilterElement, Font, FontElement, FontFaceElement, GElement, GlyphElement, GradientElement, ImageElement, LineElement, LinearGradientElement, MarkerElement, MaskElement, Matrix, MissingGlyphElement, Mouse, PSEUDO_ZERO, Parser, PathElement, PathParser, PatternElement, Point, PolygonElement, PolylineElement, Property, QB1, QB2, QB3, RadialGradientElement, RectElement, RenderedElement, Rotate, SVGElement, SVGFontLoader, Scale, Screen, Skew, SkewX, SkewY, StopElement, StyleElement, SymbolElement, TRefElement, TSpanElement, TextElement, TextPathElement, TitleElement, Transform, Translate, UnknownElement, UseElement, ViewPort, compressSpaces, Canvg as default, getSelectorSpecificity, normalizeAttributeName, normalizeColor, parseExternalUrl, index as presets, toNumbers, trimLeft, trimRight, vectorMagnitude, vectorsAngle, vectorsRatio };\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}