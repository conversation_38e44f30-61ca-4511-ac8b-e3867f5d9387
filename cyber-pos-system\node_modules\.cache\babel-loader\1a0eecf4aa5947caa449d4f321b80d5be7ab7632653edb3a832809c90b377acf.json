{"ast": null, "code": "import _objectSpread from\"E:/FX/Cyber POS/cyber-pos-system/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{Package,TrendingDown,AlertTriangle,Calendar,DollarSign,BarChart3,Activity,Tag}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const InventoryStats=_ref=>{let{products}=_ref;// Calculate basic stats\nconst totalProducts=products.length;const activeProducts=products.filter(p=>p.isActive).length;const inactiveProducts=totalProducts-activeProducts;const totalStockValue=products.reduce((sum,product)=>sum+product.price*product.stockQuantity,0);const averagePrice=products.length>0?products.reduce((sum,product)=>sum+product.price,0)/products.length:0;// Stock analysis\nconst inStockProducts=products.filter(p=>p.stockQuantity>0).length;const outOfStockProducts=products.filter(p=>p.stockQuantity===0).length;const lowStockProducts=products.filter(p=>p.stockQuantity>0&&p.stockQuantity<=p.reorderLevel).length;// Expiry analysis\nconst productsWithExpiry=products.filter(p=>p.hasExpiry).length;const thirtyDaysFromNow=new Date();void thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate()+30);const expiringProducts=products.filter(p=>p.hasExpiry&&p.expiryDate&&p.expiryDate<=thirtyDaysFromNow).length;// Category analysis\nconst categoryStats=products.reduce((acc,product)=>{if(!acc[product.category]){acc[product.category]={count:0,totalValue:0,totalStock:0,lowStock:0};}acc[product.category].count++;acc[product.category].totalValue+=product.price*product.stockQuantity;acc[product.category].totalStock+=product.stockQuantity;if(product.stockQuantity<=product.reorderLevel){acc[product.category].lowStock++;}return acc;},{});const topCategories=Object.entries(categoryStats).sort((_ref2,_ref3)=>{let[,a]=_ref2;let[,b]=_ref3;return b.totalValue-a.totalValue;}).slice(0,5);// Top value products\nconst topValueProducts=products.map(product=>_objectSpread(_objectSpread({},product),{},{totalValue:product.price*product.stockQuantity})).sort((a,b)=>b.totalValue-a.totalValue).slice(0,5);const mainStats=[{name:'Total Products',value:totalProducts.toString(),icon:Package,color:'text-blue-600',bgColor:'bg-blue-100'},{name:'Stock Value',value:\"KSh \".concat(totalStockValue.toLocaleString()),icon:DollarSign,color:'text-green-600',bgColor:'bg-green-100'},{name:'Low Stock Items',value:lowStockProducts.toString(),icon:TrendingDown,color:'text-orange-600',bgColor:'bg-orange-100'},{name:'Out of Stock',value:outOfStockProducts.toString(),icon:AlertTriangle,color:'text-red-600',bgColor:'bg-red-100'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",children:mainStats.map(stat=>/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 rounded-lg \".concat(stat.bgColor),children:/*#__PURE__*/_jsx(stat.icon,{className:\"h-5 w-5 \".concat(stat.color)})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-500\",children:stat.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg font-semibold text-gray-900\",children:stat.value})]})]})},stat.name))}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(Activity,{className:\"h-5 w-5 mr-2\"}),\"Stock Status Overview\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-green-500 rounded-full mr-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"In Stock\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-gray-900\",children:inStockProducts})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-orange-500 rounded-full mr-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Low Stock\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-gray-900\",children:lowStockProducts})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-red-500 rounded-full mr-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Out of Stock\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-gray-900\",children:outOfStockProducts})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-gray-400 rounded-full mr-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Inactive\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-gray-900\",children:inactiveProducts})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(Calendar,{className:\"h-5 w-5 mr-2\"}),\"Expiry Analysis\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Products with Expiry\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-gray-900\",children:productsWithExpiry})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Expiring in 30 Days\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold \".concat(expiringProducts>0?'text-orange-600':'text-gray-900'),children:expiringProducts})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"No Expiry Tracking\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-gray-900\",children:totalProducts-productsWithExpiry})]}),expiringProducts>0&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-orange-50 border border-orange-200 rounded-md p-3 mt-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-4 w-4 text-orange-600 mr-2\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-orange-700\",children:[expiringProducts,\" product\",expiringProducts>1?'s':'',\" expiring soon\"]})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(Tag,{className:\"h-5 w-5 mr-2\"}),\"Top Categories by Value\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:topCategories.length>0?topCategories.map(_ref4=>{let[category,stats]=_ref4;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-gray-50 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:category}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-500\",children:[stats.count,\" products \\u2022 \",stats.totalStock,\" units\",stats.lowStock>0&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-orange-600 ml-1\",children:[\"\\u2022 \",stats.lowStock,\" low stock\"]})]})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-semibold text-gray-900\",children:[\"KSh \",stats.totalValue.toLocaleString()]})]},category);}):/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 text-center py-4\",children:\"No categories yet\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(BarChart3,{className:\"h-5 w-5 mr-2\"}),\"Top Products by Stock Value\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:topValueProducts.length>0?topValueProducts.map(product=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-gray-50 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-500\",children:[product.stockQuantity,\" units @ KSh \",product.price.toLocaleString(),\" each\"]})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-semibold text-gray-900\",children:[\"KSh \",product.totalValue.toLocaleString()]})]},product.id)):/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 text-center py-4\",children:\"No products yet\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow p-6 lg:col-span-2\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(DollarSign,{className:\"h-5 w-5 mr-2\"}),\"Financial Summary\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-4 bg-green-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"Total Stock Value\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl font-bold text-green-600\",children:[\"KSh \",totalStockValue.toLocaleString()]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-4 bg-blue-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"Average Product Price\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl font-bold text-blue-600\",children:[\"KSh \",averagePrice.toLocaleString()]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-4 bg-purple-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"Categories\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-purple-600\",children:Object.keys(categoryStats).length})]})]})]})]})]});};export default InventoryStats;", "map": {"version": 3, "names": ["React", "Package", "TrendingDown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Calendar", "DollarSign", "BarChart3", "Activity", "Tag", "jsx", "_jsx", "jsxs", "_jsxs", "InventoryStats", "_ref", "products", "totalProducts", "length", "activeProducts", "filter", "p", "isActive", "inactiveProducts", "totalStockValue", "reduce", "sum", "product", "price", "stockQuantity", "averagePrice", "inStockProducts", "outOfStockProducts", "lowStockProducts", "reorderLevel", "productsWithExpiry", "hasEx<PERSON>ry", "thirtyDaysFromNow", "Date", "setDate", "getDate", "expiringProducts", "expiryDate", "categoryStats", "acc", "category", "count", "totalValue", "totalStock", "lowStock", "topCategories", "Object", "entries", "sort", "_ref2", "_ref3", "a", "b", "slice", "topValueProducts", "map", "_objectSpread", "mainStats", "name", "value", "toString", "icon", "color", "bgColor", "concat", "toLocaleString", "className", "children", "stat", "_ref4", "stats", "id", "keys"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/inventory/InventoryStats.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Package,\n  TrendingDown,\n  AlertTriangle,\n  Calendar,\n  DollarSign,\n  BarChart3,\n  Activity,\n  Tag\n} from 'lucide-react';\nimport { Product } from '../../types';\n\ninterface InventoryStatsProps {\n  products: Product[];\n}\n\nconst InventoryStats: React.FC<InventoryStatsProps> = ({ products }) => {\n  // Calculate basic stats\n  const totalProducts = products.length;\n  const activeProducts = products.filter(p => p.isActive).length;\n  const inactiveProducts = totalProducts - activeProducts;\n  \n  const totalStockValue = products.reduce((sum, product) => \n    sum + (product.price * product.stockQuantity), 0\n  );\n  \n  const averagePrice = products.length > 0 \n    ? products.reduce((sum, product) => sum + product.price, 0) / products.length \n    : 0;\n\n  // Stock analysis\n  const inStockProducts = products.filter(p => p.stockQuantity > 0).length;\n  const outOfStockProducts = products.filter(p => p.stockQuantity === 0).length;\n  const lowStockProducts = products.filter(p => \n    p.stockQuantity > 0 && p.stockQuantity <= p.reorderLevel\n  ).length;\n\n  // Expiry analysis\n  const productsWithExpiry = products.filter(p => p.hasExpiry).length;\n  const thirtyDaysFromNow = new Date();\n  void thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\n  \n  const expiringProducts = products.filter(p => \n    p.hasExpiry && p.expiryDate && p.expiryDate <= thirtyDaysFromNow\n  ).length;\n\n  // Category analysis\n  const categoryStats = products.reduce((acc, product) => {\n    if (!acc[product.category]) {\n      acc[product.category] = {\n        count: 0,\n        totalValue: 0,\n        totalStock: 0,\n        lowStock: 0\n      };\n    }\n    acc[product.category].count++;\n    acc[product.category].totalValue += product.price * product.stockQuantity;\n    acc[product.category].totalStock += product.stockQuantity;\n    if (product.stockQuantity <= product.reorderLevel) {\n      acc[product.category].lowStock++;\n    }\n    return acc;\n  }, {} as Record<string, { count: number; totalValue: number; totalStock: number; lowStock: number }>);\n\n  const topCategories = Object.entries(categoryStats)\n    .sort(([,a], [,b]) => b.totalValue - a.totalValue)\n    .slice(0, 5);\n\n  // Top value products\n  const topValueProducts = products\n    .map(product => ({\n      ...product,\n      totalValue: product.price * product.stockQuantity\n    }))\n    .sort((a, b) => b.totalValue - a.totalValue)\n    .slice(0, 5);\n\n  const mainStats = [\n    {\n      name: 'Total Products',\n      value: totalProducts.toString(),\n      icon: Package,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100'\n    },\n    {\n      name: 'Stock Value',\n      value: `KSh ${totalStockValue.toLocaleString()}`,\n      icon: DollarSign,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100'\n    },\n    {\n      name: 'Low Stock Items',\n      value: lowStockProducts.toString(),\n      icon: TrendingDown,\n      color: 'text-orange-600',\n      bgColor: 'bg-orange-100'\n    },\n    {\n      name: 'Out of Stock',\n      value: outOfStockProducts.toString(),\n      icon: AlertTriangle,\n      color: 'text-red-600',\n      bgColor: 'bg-red-100'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Main Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {mainStats.map((stat) => (\n          <div key={stat.name} className=\"bg-white rounded-lg shadow p-4\">\n            <div className=\"flex items-center\">\n              <div className={`p-2 rounded-lg ${stat.bgColor}`}>\n                <stat.icon className={`h-5 w-5 ${stat.color}`} />\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-500\">{stat.name}</p>\n                <p className=\"text-lg font-semibold text-gray-900\">{stat.value}</p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Detailed Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Stock Status Breakdown */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <Activity className=\"h-5 w-5 mr-2\" />\n            Stock Status Overview\n          </h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600\">In Stock</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{inStockProducts}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-orange-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600\">Low Stock</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{lowStockProducts}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-red-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600\">Out of Stock</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{outOfStockProducts}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-gray-400 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600\">Inactive</span>\n              </div>\n              <span className=\"text-sm font-semibold text-gray-900\">{inactiveProducts}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Expiry Analysis */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <Calendar className=\"h-5 w-5 mr-2\" />\n            Expiry Analysis\n          </h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Products with Expiry</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{productsWithExpiry}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Expiring in 30 Days</span>\n              <span className={`text-sm font-semibold ${expiringProducts > 0 ? 'text-orange-600' : 'text-gray-900'}`}>\n                {expiringProducts}\n              </span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">No Expiry Tracking</span>\n              <span className=\"text-sm font-semibold text-gray-900\">{totalProducts - productsWithExpiry}</span>\n            </div>\n            {expiringProducts > 0 && (\n              <div className=\"bg-orange-50 border border-orange-200 rounded-md p-3 mt-3\">\n                <div className=\"flex items-center\">\n                  <AlertTriangle className=\"h-4 w-4 text-orange-600 mr-2\" />\n                  <span className=\"text-sm text-orange-700\">\n                    {expiringProducts} product{expiringProducts > 1 ? 's' : ''} expiring soon\n                  </span>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Top Categories by Value */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <Tag className=\"h-5 w-5 mr-2\" />\n            Top Categories by Value\n          </h3>\n          <div className=\"space-y-3\">\n            {topCategories.length > 0 ? (\n              topCategories.map(([category, stats]) => (\n                <div key={category} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-700\">{category}</span>\n                    <div className=\"text-xs text-gray-500\">\n                      {stats.count} products • {stats.totalStock} units\n                      {stats.lowStock > 0 && (\n                        <span className=\"text-orange-600 ml-1\">• {stats.lowStock} low stock</span>\n                      )}\n                    </div>\n                  </div>\n                  <span className=\"text-sm font-semibold text-gray-900\">\n                    KSh {stats.totalValue.toLocaleString()}\n                  </span>\n                </div>\n              ))\n            ) : (\n              <p className=\"text-sm text-gray-500 text-center py-4\">No categories yet</p>\n            )}\n          </div>\n        </div>\n\n        {/* Top Products by Value */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <BarChart3 className=\"h-5 w-5 mr-2\" />\n            Top Products by Stock Value\n          </h3>\n          <div className=\"space-y-3\">\n            {topValueProducts.length > 0 ? (\n              topValueProducts.map((product) => (\n                <div key={product.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-700\">{product.name}</span>\n                    <div className=\"text-xs text-gray-500\">\n                      {product.stockQuantity} units @ KSh {product.price.toLocaleString()} each\n                    </div>\n                  </div>\n                  <span className=\"text-sm font-semibold text-gray-900\">\n                    KSh {product.totalValue.toLocaleString()}\n                  </span>\n                </div>\n              ))\n            ) : (\n              <p className=\"text-sm text-gray-500 text-center py-4\">No products yet</p>\n            )}\n          </div>\n        </div>\n\n        {/* Summary Metrics */}\n        <div className=\"bg-white rounded-lg shadow p-6 lg:col-span-2\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n            <DollarSign className=\"h-5 w-5 mr-2\" />\n            Financial Summary\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n              <p className=\"text-sm text-gray-600\">Total Stock Value</p>\n              <p className=\"text-2xl font-bold text-green-600\">\n                KSh {totalStockValue.toLocaleString()}\n              </p>\n            </div>\n            <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n              <p className=\"text-sm text-gray-600\">Average Product Price</p>\n              <p className=\"text-2xl font-bold text-blue-600\">\n                KSh {averagePrice.toLocaleString()}\n              </p>\n            </div>\n            <div className=\"text-center p-4 bg-purple-50 rounded-lg\">\n              <p className=\"text-sm text-gray-600\">Categories</p>\n              <p className=\"text-2xl font-bold text-purple-600\">\n                {Object.keys(categoryStats).length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InventoryStats;\n"], "mappings": "qHAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,OAAO,CACPC,YAAY,CACZC,aAAa,CACbC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,QAAQ,CACRC,GAAG,KACE,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOtB,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjE;AACA,KAAM,CAAAE,aAAa,CAAGD,QAAQ,CAACE,MAAM,CACrC,KAAM,CAAAC,cAAc,CAAGH,QAAQ,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,CAAC,CAACJ,MAAM,CAC9D,KAAM,CAAAK,gBAAgB,CAAGN,aAAa,CAAGE,cAAc,CAEvD,KAAM,CAAAK,eAAe,CAAGR,QAAQ,CAACS,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GACnDD,GAAG,CAAIC,OAAO,CAACC,KAAK,CAAGD,OAAO,CAACE,aAAc,CAAE,CACjD,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGd,QAAQ,CAACE,MAAM,CAAG,CAAC,CACpCF,QAAQ,CAACS,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GAAKD,GAAG,CAAGC,OAAO,CAACC,KAAK,CAAE,CAAC,CAAC,CAAGZ,QAAQ,CAACE,MAAM,CAC3E,CAAC,CAEL;AACA,KAAM,CAAAa,eAAe,CAAGf,QAAQ,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACQ,aAAa,CAAG,CAAC,CAAC,CAACX,MAAM,CACxE,KAAM,CAAAc,kBAAkB,CAAGhB,QAAQ,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACQ,aAAa,GAAK,CAAC,CAAC,CAACX,MAAM,CAC7E,KAAM,CAAAe,gBAAgB,CAAGjB,QAAQ,CAACI,MAAM,CAACC,CAAC,EACxCA,CAAC,CAACQ,aAAa,CAAG,CAAC,EAAIR,CAAC,CAACQ,aAAa,EAAIR,CAAC,CAACa,YAC9C,CAAC,CAAChB,MAAM,CAER;AACA,KAAM,CAAAiB,kBAAkB,CAAGnB,QAAQ,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACe,SAAS,CAAC,CAAClB,MAAM,CACnE,KAAM,CAAAmB,iBAAiB,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACpC,IAAK,CAAAD,iBAAiB,CAACE,OAAO,CAACF,iBAAiB,CAACG,OAAO,CAAC,CAAC,CAAG,EAAE,CAAC,CAEhE,KAAM,CAAAC,gBAAgB,CAAGzB,QAAQ,CAACI,MAAM,CAACC,CAAC,EACxCA,CAAC,CAACe,SAAS,EAAIf,CAAC,CAACqB,UAAU,EAAIrB,CAAC,CAACqB,UAAU,EAAIL,iBACjD,CAAC,CAACnB,MAAM,CAER;AACA,KAAM,CAAAyB,aAAa,CAAG3B,QAAQ,CAACS,MAAM,CAAC,CAACmB,GAAG,CAAEjB,OAAO,GAAK,CACtD,GAAI,CAACiB,GAAG,CAACjB,OAAO,CAACkB,QAAQ,CAAC,CAAE,CAC1BD,GAAG,CAACjB,OAAO,CAACkB,QAAQ,CAAC,CAAG,CACtBC,KAAK,CAAE,CAAC,CACRC,UAAU,CAAE,CAAC,CACbC,UAAU,CAAE,CAAC,CACbC,QAAQ,CAAE,CACZ,CAAC,CACH,CACAL,GAAG,CAACjB,OAAO,CAACkB,QAAQ,CAAC,CAACC,KAAK,EAAE,CAC7BF,GAAG,CAACjB,OAAO,CAACkB,QAAQ,CAAC,CAACE,UAAU,EAAIpB,OAAO,CAACC,KAAK,CAAGD,OAAO,CAACE,aAAa,CACzEe,GAAG,CAACjB,OAAO,CAACkB,QAAQ,CAAC,CAACG,UAAU,EAAIrB,OAAO,CAACE,aAAa,CACzD,GAAIF,OAAO,CAACE,aAAa,EAAIF,OAAO,CAACO,YAAY,CAAE,CACjDU,GAAG,CAACjB,OAAO,CAACkB,QAAQ,CAAC,CAACI,QAAQ,EAAE,CAClC,CACA,MAAO,CAAAL,GAAG,CACZ,CAAC,CAAE,CAAC,CAAgG,CAAC,CAErG,KAAM,CAAAM,aAAa,CAAGC,MAAM,CAACC,OAAO,CAACT,aAAa,CAAC,CAChDU,IAAI,CAAC,CAAAC,KAAA,CAAAC,KAAA,OAAC,EAAEC,CAAC,CAAC,CAAAF,KAAA,IAAE,EAAEG,CAAC,CAAC,CAAAF,KAAA,OAAK,CAAAE,CAAC,CAACV,UAAU,CAAGS,CAAC,CAACT,UAAU,GAAC,CACjDW,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAEd;AACA,KAAM,CAAAC,gBAAgB,CAAG3C,QAAQ,CAC9B4C,GAAG,CAACjC,OAAO,EAAAkC,aAAA,CAAAA,aAAA,IACPlC,OAAO,MACVoB,UAAU,CAAEpB,OAAO,CAACC,KAAK,CAAGD,OAAO,CAACE,aAAa,EACjD,CAAC,CACFwB,IAAI,CAAC,CAACG,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACV,UAAU,CAAGS,CAAC,CAACT,UAAU,CAAC,CAC3CW,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAEd,KAAM,CAAAI,SAAS,CAAG,CAChB,CACEC,IAAI,CAAE,gBAAgB,CACtBC,KAAK,CAAE/C,aAAa,CAACgD,QAAQ,CAAC,CAAC,CAC/BC,IAAI,CAAEhE,OAAO,CACbiE,KAAK,CAAE,eAAe,CACtBC,OAAO,CAAE,aACX,CAAC,CACD,CACEL,IAAI,CAAE,aAAa,CACnBC,KAAK,QAAAK,MAAA,CAAS7C,eAAe,CAAC8C,cAAc,CAAC,CAAC,CAAE,CAChDJ,IAAI,CAAE5D,UAAU,CAChB6D,KAAK,CAAE,gBAAgB,CACvBC,OAAO,CAAE,cACX,CAAC,CACD,CACEL,IAAI,CAAE,iBAAiB,CACvBC,KAAK,CAAE/B,gBAAgB,CAACgC,QAAQ,CAAC,CAAC,CAClCC,IAAI,CAAE/D,YAAY,CAClBgE,KAAK,CAAE,iBAAiB,CACxBC,OAAO,CAAE,eACX,CAAC,CACD,CACEL,IAAI,CAAE,cAAc,CACpBC,KAAK,CAAEhC,kBAAkB,CAACiC,QAAQ,CAAC,CAAC,CACpCC,IAAI,CAAE9D,aAAa,CACnB+D,KAAK,CAAE,cAAc,CACrBC,OAAO,CAAE,YACX,CAAC,CACF,CAED,mBACEvD,KAAA,QAAK0D,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB7D,IAAA,QAAK4D,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEV,SAAS,CAACF,GAAG,CAAEa,IAAI,eAClB9D,IAAA,QAAqB4D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7D3D,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7D,IAAA,QAAK4D,SAAS,mBAAAF,MAAA,CAAoBI,IAAI,CAACL,OAAO,CAAG,CAAAI,QAAA,cAC/C7D,IAAA,CAAC8D,IAAI,CAACP,IAAI,EAACK,SAAS,YAAAF,MAAA,CAAaI,IAAI,CAACN,KAAK,CAAG,CAAE,CAAC,CAC9C,CAAC,cACNtD,KAAA,QAAK0D,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB7D,IAAA,MAAG4D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEC,IAAI,CAACV,IAAI,CAAI,CAAC,cAChEpD,IAAA,MAAG4D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAEC,IAAI,CAACT,KAAK,CAAI,CAAC,EAChE,CAAC,EACH,CAAC,EATES,IAAI,CAACV,IAUV,CACN,CAAC,CACC,CAAC,cAGNlD,KAAA,QAAK0D,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpD3D,KAAA,QAAK0D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C3D,KAAA,OAAI0D,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtE7D,IAAA,CAACH,QAAQ,EAAC+D,SAAS,CAAC,cAAc,CAAE,CAAC,wBAEvC,EAAI,CAAC,cACL1D,KAAA,QAAK0D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3D,KAAA,QAAK0D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD3D,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7D,IAAA,QAAK4D,SAAS,CAAC,wCAAwC,CAAM,CAAC,cAC9D5D,IAAA,SAAM4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EACpD,CAAC,cACN7D,IAAA,SAAM4D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAEzC,eAAe,CAAO,CAAC,EAC3E,CAAC,cACNlB,KAAA,QAAK0D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD3D,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7D,IAAA,QAAK4D,SAAS,CAAC,yCAAyC,CAAM,CAAC,cAC/D5D,IAAA,SAAM4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACrD,CAAC,cACN7D,IAAA,SAAM4D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAEvC,gBAAgB,CAAO,CAAC,EAC5E,CAAC,cACNpB,KAAA,QAAK0D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD3D,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7D,IAAA,QAAK4D,SAAS,CAAC,sCAAsC,CAAM,CAAC,cAC5D5D,IAAA,SAAM4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,EACxD,CAAC,cACN7D,IAAA,SAAM4D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAExC,kBAAkB,CAAO,CAAC,EAC9E,CAAC,cACNnB,KAAA,QAAK0D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD3D,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7D,IAAA,QAAK4D,SAAS,CAAC,uCAAuC,CAAM,CAAC,cAC7D5D,IAAA,SAAM4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EACpD,CAAC,cACN7D,IAAA,SAAM4D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAEjD,gBAAgB,CAAO,CAAC,EAC5E,CAAC,EACH,CAAC,EACH,CAAC,cAGNV,KAAA,QAAK0D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C3D,KAAA,OAAI0D,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtE7D,IAAA,CAACN,QAAQ,EAACkE,SAAS,CAAC,cAAc,CAAE,CAAC,kBAEvC,EAAI,CAAC,cACL1D,KAAA,QAAK0D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3D,KAAA,QAAK0D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD7D,IAAA,SAAM4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,sBAAoB,CAAM,CAAC,cACnE7D,IAAA,SAAM4D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAErC,kBAAkB,CAAO,CAAC,EAC9E,CAAC,cACNtB,KAAA,QAAK0D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD7D,IAAA,SAAM4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,cAClE7D,IAAA,SAAM4D,SAAS,0BAAAF,MAAA,CAA2B5B,gBAAgB,CAAG,CAAC,CAAG,iBAAiB,CAAG,eAAe,CAAG,CAAA+B,QAAA,CACpG/B,gBAAgB,CACb,CAAC,EACJ,CAAC,cACN5B,KAAA,QAAK0D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD7D,IAAA,SAAM4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,oBAAkB,CAAM,CAAC,cACjE7D,IAAA,SAAM4D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAEvD,aAAa,CAAGkB,kBAAkB,CAAO,CAAC,EAC9F,CAAC,CACLM,gBAAgB,CAAG,CAAC,eACnB9B,IAAA,QAAK4D,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE3D,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7D,IAAA,CAACP,aAAa,EAACmE,SAAS,CAAC,8BAA8B,CAAE,CAAC,cAC1D1D,KAAA,SAAM0D,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EACtC/B,gBAAgB,CAAC,UAAQ,CAACA,gBAAgB,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAC,gBAC7D,EAAM,CAAC,EACJ,CAAC,CACH,CACN,EACE,CAAC,EACH,CAAC,cAGN5B,KAAA,QAAK0D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C3D,KAAA,OAAI0D,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtE7D,IAAA,CAACF,GAAG,EAAC8D,SAAS,CAAC,cAAc,CAAE,CAAC,0BAElC,EAAI,CAAC,cACL5D,IAAA,QAAK4D,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBtB,aAAa,CAAChC,MAAM,CAAG,CAAC,CACvBgC,aAAa,CAACU,GAAG,CAACc,KAAA,MAAC,CAAC7B,QAAQ,CAAE8B,KAAK,CAAC,CAAAD,KAAA,oBAClC7D,KAAA,QAAoB0D,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtF3D,KAAA,QAAA2D,QAAA,eACE7D,IAAA,SAAM4D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE3B,QAAQ,CAAO,CAAC,cACrEhC,KAAA,QAAK0D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCG,KAAK,CAAC7B,KAAK,CAAC,mBAAY,CAAC6B,KAAK,CAAC3B,UAAU,CAAC,QAC3C,CAAC2B,KAAK,CAAC1B,QAAQ,CAAG,CAAC,eACjBpC,KAAA,SAAM0D,SAAS,CAAC,sBAAsB,CAAAC,QAAA,EAAC,SAAE,CAACG,KAAK,CAAC1B,QAAQ,CAAC,YAAU,EAAM,CAC1E,EACE,CAAC,EACH,CAAC,cACNpC,KAAA,SAAM0D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,EAAC,MAChD,CAACG,KAAK,CAAC5B,UAAU,CAACuB,cAAc,CAAC,CAAC,EAClC,CAAC,GAZCzB,QAaL,CAAC,EACP,CAAC,cAEFlC,IAAA,MAAG4D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAC3E,CACE,CAAC,EACH,CAAC,cAGN3D,KAAA,QAAK0D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C3D,KAAA,OAAI0D,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtE7D,IAAA,CAACJ,SAAS,EAACgE,SAAS,CAAC,cAAc,CAAE,CAAC,8BAExC,EAAI,CAAC,cACL5D,IAAA,QAAK4D,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBb,gBAAgB,CAACzC,MAAM,CAAG,CAAC,CAC1ByC,gBAAgB,CAACC,GAAG,CAAEjC,OAAO,eAC3Bd,KAAA,QAAsB0D,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACxF3D,KAAA,QAAA2D,QAAA,eACE7D,IAAA,SAAM4D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE7C,OAAO,CAACoC,IAAI,CAAO,CAAC,cACzElD,KAAA,QAAK0D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnC7C,OAAO,CAACE,aAAa,CAAC,eAAa,CAACF,OAAO,CAACC,KAAK,CAAC0C,cAAc,CAAC,CAAC,CAAC,OACtE,EAAK,CAAC,EACH,CAAC,cACNzD,KAAA,SAAM0D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,EAAC,MAChD,CAAC7C,OAAO,CAACoB,UAAU,CAACuB,cAAc,CAAC,CAAC,EACpC,CAAC,GATC3C,OAAO,CAACiD,EAUb,CACN,CAAC,cAEFjE,IAAA,MAAG4D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,iBAAe,CAAG,CACzE,CACE,CAAC,EACH,CAAC,cAGN3D,KAAA,QAAK0D,SAAS,CAAC,8CAA8C,CAAAC,QAAA,eAC3D3D,KAAA,OAAI0D,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACtE7D,IAAA,CAACL,UAAU,EAACiE,SAAS,CAAC,cAAc,CAAE,CAAC,oBAEzC,EAAI,CAAC,cACL1D,KAAA,QAAK0D,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD3D,KAAA,QAAK0D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD7D,IAAA,MAAG4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAAC,cAC1D3D,KAAA,MAAG0D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,MAC3C,CAAChD,eAAe,CAAC8C,cAAc,CAAC,CAAC,EACpC,CAAC,EACD,CAAC,cACNzD,KAAA,QAAK0D,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD7D,IAAA,MAAG4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uBAAqB,CAAG,CAAC,cAC9D3D,KAAA,MAAG0D,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAC,MAC1C,CAAC1C,YAAY,CAACwC,cAAc,CAAC,CAAC,EACjC,CAAC,EACD,CAAC,cACNzD,KAAA,QAAK0D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD7D,IAAA,MAAG4D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAAU,CAAG,CAAC,cACnD7D,IAAA,MAAG4D,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAC9CrB,MAAM,CAAC0B,IAAI,CAAClC,aAAa,CAAC,CAACzB,MAAM,CACjC,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}