{"ast": null, "code": "'use strict';\n\n// TODO: Remove from `core-js@4` since it's moved to entry points\nrequire('../modules/es.regexp.exec');\nvar call = require('../internals/function-call');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar regexpExec = require('../internals/regexp-exec');\nvar fails = require('../internals/fails');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar SPECIES = wellKnownSymbol('species');\nvar RegExpPrototype = RegExp.prototype;\nmodule.exports = function (KEY, exec, FORCED, SHAM) {\n  var SYMBOL = wellKnownSymbol(KEY);\n  var DELEGATES_TO_SYMBOL = !fails(function () {\n    // String methods call symbol-named RegExp methods\n    var O = {};\n    O[SYMBOL] = function () {\n      return 7;\n    };\n    return ''[KEY](O) !== 7;\n  });\n  var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL && !fails(function () {\n    // Symbol-named RegExp methods call .exec\n    var execCalled = false;\n    var re = /a/;\n    if (KEY === 'split') {\n      // We can't use real regex here since it causes deoptimization\n      // and serious performance degradation in V8\n      // https://github.com/zloirock/core-js/issues/306\n      re = {};\n      // RegExp[@@split] doesn't call the regex's exec method, but first creates\n      // a new one. We need to return the patched regex when creating the new one.\n      re.constructor = {};\n      re.constructor[SPECIES] = function () {\n        return re;\n      };\n      re.flags = '';\n      re[SYMBOL] = /./[SYMBOL];\n    }\n    re.exec = function () {\n      execCalled = true;\n      return null;\n    };\n    re[SYMBOL]('');\n    return !execCalled;\n  });\n  if (!DELEGATES_TO_SYMBOL || !DELEGATES_TO_EXEC || FORCED) {\n    var nativeRegExpMethod = /./[SYMBOL];\n    var methods = exec(SYMBOL, ''[KEY], function (nativeMethod, regexp, str, arg2, forceStringMethod) {\n      var $exec = regexp.exec;\n      if ($exec === regexpExec || $exec === RegExpPrototype.exec) {\n        if (DELEGATES_TO_SYMBOL && !forceStringMethod) {\n          // The native String method already delegates to @@method (this\n          // polyfilled function), leasing to infinite recursion.\n          // We avoid it by directly calling the native @@method method.\n          return {\n            done: true,\n            value: call(nativeRegExpMethod, regexp, str, arg2)\n          };\n        }\n        return {\n          done: true,\n          value: call(nativeMethod, str, regexp, arg2)\n        };\n      }\n      return {\n        done: false\n      };\n    });\n    defineBuiltIn(String.prototype, KEY, methods[0]);\n    defineBuiltIn(RegExpPrototype, SYMBOL, methods[1]);\n  }\n  if (SHAM) createNonEnumerableProperty(RegExpPrototype[SYMBOL], 'sham', true);\n};", "map": {"version": 3, "names": ["require", "call", "defineBuiltIn", "regexpExec", "fails", "wellKnownSymbol", "createNonEnumerableProperty", "SPECIES", "RegExpPrototype", "RegExp", "prototype", "module", "exports", "KEY", "exec", "FORCED", "SHAM", "SYMBOL", "DELEGATES_TO_SYMBOL", "O", "DELEGATES_TO_EXEC", "execCalled", "re", "constructor", "flags", "nativeRegExpMethod", "methods", "nativeMethod", "regexp", "str", "arg2", "forceStringMethod", "$exec", "done", "value", "String"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/fix-regexp-well-known-symbol-logic.js"], "sourcesContent": ["'use strict';\n// TODO: Remove from `core-js@4` since it's moved to entry points\nrequire('../modules/es.regexp.exec');\nvar call = require('../internals/function-call');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar regexpExec = require('../internals/regexp-exec');\nvar fails = require('../internals/fails');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\nvar SPECIES = wellKnownSymbol('species');\nvar RegExpPrototype = RegExp.prototype;\n\nmodule.exports = function (KEY, exec, FORCED, SHAM) {\n  var SYMBOL = wellKnownSymbol(KEY);\n\n  var DELEGATES_TO_SYMBOL = !fails(function () {\n    // String methods call symbol-named RegExp methods\n    var O = {};\n    O[SYMBOL] = function () { return 7; };\n    return ''[KEY](O) !== 7;\n  });\n\n  var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL && !fails(function () {\n    // Symbol-named RegExp methods call .exec\n    var execCalled = false;\n    var re = /a/;\n\n    if (KEY === 'split') {\n      // We can't use real regex here since it causes deoptimization\n      // and serious performance degradation in V8\n      // https://github.com/zloirock/core-js/issues/306\n      re = {};\n      // RegExp[@@split] doesn't call the regex's exec method, but first creates\n      // a new one. We need to return the patched regex when creating the new one.\n      re.constructor = {};\n      re.constructor[SPECIES] = function () { return re; };\n      re.flags = '';\n      re[SYMBOL] = /./[SYMBOL];\n    }\n\n    re.exec = function () {\n      execCalled = true;\n      return null;\n    };\n\n    re[SYMBOL]('');\n    return !execCalled;\n  });\n\n  if (\n    !DELEGATES_TO_SYMBOL ||\n    !DELEGATES_TO_EXEC ||\n    FORCED\n  ) {\n    var nativeRegExpMethod = /./[SYMBOL];\n    var methods = exec(SYMBOL, ''[KEY], function (nativeMethod, regexp, str, arg2, forceStringMethod) {\n      var $exec = regexp.exec;\n      if ($exec === regexpExec || $exec === RegExpPrototype.exec) {\n        if (DELEGATES_TO_SYMBOL && !forceStringMethod) {\n          // The native String method already delegates to @@method (this\n          // polyfilled function), leasing to infinite recursion.\n          // We avoid it by directly calling the native @@method method.\n          return { done: true, value: call(nativeRegExpMethod, regexp, str, arg2) };\n        }\n        return { done: true, value: call(nativeMethod, str, regexp, arg2) };\n      }\n      return { done: false };\n    });\n\n    defineBuiltIn(String.prototype, KEY, methods[0]);\n    defineBuiltIn(RegExpPrototype, SYMBOL, methods[1]);\n  }\n\n  if (SHAM) createNonEnumerableProperty(RegExpPrototype[SYMBOL], 'sham', true);\n};\n"], "mappings": "AAAA,YAAY;;AACZ;AACAA,OAAO,CAAC,2BAA2B,CAAC;AACpC,IAAIC,IAAI,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIE,aAAa,GAAGF,OAAO,CAAC,8BAA8B,CAAC;AAC3D,IAAIG,UAAU,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAII,KAAK,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIK,eAAe,GAAGL,OAAO,CAAC,gCAAgC,CAAC;AAC/D,IAAIM,2BAA2B,GAAGN,OAAO,CAAC,6CAA6C,CAAC;AAExF,IAAIO,OAAO,GAAGF,eAAe,CAAC,SAAS,CAAC;AACxC,IAAIG,eAAe,GAAGC,MAAM,CAACC,SAAS;AAEtCC,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAClD,IAAIC,MAAM,GAAGZ,eAAe,CAACQ,GAAG,CAAC;EAEjC,IAAIK,mBAAmB,GAAG,CAACd,KAAK,CAAC,YAAY;IAC3C;IACA,IAAIe,CAAC,GAAG,CAAC,CAAC;IACVA,CAAC,CAACF,MAAM,CAAC,GAAG,YAAY;MAAE,OAAO,CAAC;IAAE,CAAC;IACrC,OAAO,EAAE,CAACJ,GAAG,CAAC,CAACM,CAAC,CAAC,KAAK,CAAC;EACzB,CAAC,CAAC;EAEF,IAAIC,iBAAiB,GAAGF,mBAAmB,IAAI,CAACd,KAAK,CAAC,YAAY;IAChE;IACA,IAAIiB,UAAU,GAAG,KAAK;IACtB,IAAIC,EAAE,GAAG,GAAG;IAEZ,IAAIT,GAAG,KAAK,OAAO,EAAE;MACnB;MACA;MACA;MACAS,EAAE,GAAG,CAAC,CAAC;MACP;MACA;MACAA,EAAE,CAACC,WAAW,GAAG,CAAC,CAAC;MACnBD,EAAE,CAACC,WAAW,CAAChB,OAAO,CAAC,GAAG,YAAY;QAAE,OAAOe,EAAE;MAAE,CAAC;MACpDA,EAAE,CAACE,KAAK,GAAG,EAAE;MACbF,EAAE,CAACL,MAAM,CAAC,GAAG,GAAG,CAACA,MAAM,CAAC;IAC1B;IAEAK,EAAE,CAACR,IAAI,GAAG,YAAY;MACpBO,UAAU,GAAG,IAAI;MACjB,OAAO,IAAI;IACb,CAAC;IAEDC,EAAE,CAACL,MAAM,CAAC,CAAC,EAAE,CAAC;IACd,OAAO,CAACI,UAAU;EACpB,CAAC,CAAC;EAEF,IACE,CAACH,mBAAmB,IACpB,CAACE,iBAAiB,IAClBL,MAAM,EACN;IACA,IAAIU,kBAAkB,GAAG,GAAG,CAACR,MAAM,CAAC;IACpC,IAAIS,OAAO,GAAGZ,IAAI,CAACG,MAAM,EAAE,EAAE,CAACJ,GAAG,CAAC,EAAE,UAAUc,YAAY,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,iBAAiB,EAAE;MAChG,IAAIC,KAAK,GAAGJ,MAAM,CAACd,IAAI;MACvB,IAAIkB,KAAK,KAAK7B,UAAU,IAAI6B,KAAK,KAAKxB,eAAe,CAACM,IAAI,EAAE;QAC1D,IAAII,mBAAmB,IAAI,CAACa,iBAAiB,EAAE;UAC7C;UACA;UACA;UACA,OAAO;YAAEE,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAEjC,IAAI,CAACwB,kBAAkB,EAAEG,MAAM,EAAEC,GAAG,EAAEC,IAAI;UAAE,CAAC;QAC3E;QACA,OAAO;UAAEG,IAAI,EAAE,IAAI;UAAEC,KAAK,EAAEjC,IAAI,CAAC0B,YAAY,EAAEE,GAAG,EAAED,MAAM,EAAEE,IAAI;QAAE,CAAC;MACrE;MACA,OAAO;QAAEG,IAAI,EAAE;MAAM,CAAC;IACxB,CAAC,CAAC;IAEF/B,aAAa,CAACiC,MAAM,CAACzB,SAAS,EAAEG,GAAG,EAAEa,OAAO,CAAC,CAAC,CAAC,CAAC;IAChDxB,aAAa,CAACM,eAAe,EAAES,MAAM,EAAES,OAAO,CAAC,CAAC,CAAC,CAAC;EACpD;EAEA,IAAIV,IAAI,EAAEV,2BAA2B,CAACE,eAAe,CAACS,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC;AAC9E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}