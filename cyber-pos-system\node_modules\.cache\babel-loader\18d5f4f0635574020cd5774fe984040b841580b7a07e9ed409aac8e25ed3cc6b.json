{"ast": null, "code": "'use strict';\n\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isArray = require('../internals/is-array');\nvar nativeReverse = uncurryThis([].reverse);\nvar test = [1, 2];\n\n// `Array.prototype.reverse` method\n// https://tc39.es/ecma262/#sec-array.prototype.reverse\n// fix for Safari 12.0 bug\n// https://bugs.webkit.org/show_bug.cgi?id=188794\n$({\n  target: 'Array',\n  proto: true,\n  forced: String(test) === String(test.reverse())\n}, {\n  reverse: function reverse() {\n    // eslint-disable-next-line no-self-assign -- dirty hack\n    if (isArray(this)) this.length = this.length;\n    return nativeReverse(this);\n  }\n});", "map": {"version": 3, "names": ["$", "require", "uncurryThis", "isArray", "nativeReverse", "reverse", "test", "target", "proto", "forced", "String", "length"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/modules/es.array.reverse.js"], "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isArray = require('../internals/is-array');\n\nvar nativeReverse = uncurryThis([].reverse);\nvar test = [1, 2];\n\n// `Array.prototype.reverse` method\n// https://tc39.es/ecma262/#sec-array.prototype.reverse\n// fix for Safari 12.0 bug\n// https://bugs.webkit.org/show_bug.cgi?id=188794\n$({ target: 'Array', proto: true, forced: String(test) === String(test.reverse()) }, {\n  reverse: function reverse() {\n    // eslint-disable-next-line no-self-assign -- dirty hack\n    if (isArray(this)) this.length = this.length;\n    return nativeReverse(this);\n  }\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AACtC,IAAIC,WAAW,GAAGD,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIE,OAAO,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAE9C,IAAIG,aAAa,GAAGF,WAAW,CAAC,EAAE,CAACG,OAAO,CAAC;AAC3C,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;AAEjB;AACA;AACA;AACA;AACAN,CAAC,CAAC;EAAEO,MAAM,EAAE,OAAO;EAAEC,KAAK,EAAE,IAAI;EAAEC,MAAM,EAAEC,MAAM,CAACJ,IAAI,CAAC,KAAKI,MAAM,CAACJ,IAAI,CAACD,OAAO,CAAC,CAAC;AAAE,CAAC,EAAE;EACnFA,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B;IACA,IAAIF,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACA,MAAM;IAC5C,OAAOP,aAAa,CAAC,IAAI,CAAC;EAC5B;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}