{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = globalThis;\n  } else if (STATIC) {\n    target = globalThis[TARGET] || defineGlobalProperty(TARGET, {});\n  } else {\n    target = globalThis[TARGET] && globalThis[TARGET].prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty == typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || targetProperty && targetProperty.sham) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    defineBuiltIn(target, key, sourceProperty, options);\n  }\n};", "map": {"version": 3, "names": ["globalThis", "require", "getOwnPropertyDescriptor", "f", "createNonEnumerableProperty", "defineBuiltIn", "defineGlobalProperty", "copyConstructorProperties", "isForced", "module", "exports", "options", "source", "TARGET", "target", "GLOBAL", "global", "STATIC", "stat", "FORCED", "key", "targetProperty", "sourceProperty", "descriptor", "prototype", "dontCallGetSet", "value", "forced", "undefined", "sham"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/export.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = globalThis;\n  } else if (STATIC) {\n    target = globalThis[TARGET] || defineGlobalProperty(TARGET, {});\n  } else {\n    target = globalThis[TARGET] && globalThis[TARGET].prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty == typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    defineBuiltIn(target, key, sourceProperty, options);\n  }\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,wBAAwB,GAAGD,OAAO,CAAC,iDAAiD,CAAC,CAACE,CAAC;AAC3F,IAAIC,2BAA2B,GAAGH,OAAO,CAAC,6CAA6C,CAAC;AACxF,IAAII,aAAa,GAAGJ,OAAO,CAAC,8BAA8B,CAAC;AAC3D,IAAIK,oBAAoB,GAAGL,OAAO,CAAC,qCAAqC,CAAC;AACzE,IAAIM,yBAAyB,GAAGN,OAAO,CAAC,0CAA0C,CAAC;AACnF,IAAIO,QAAQ,GAAGP,OAAO,CAAC,wBAAwB,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAQ,MAAM,CAACC,OAAO,GAAG,UAAUC,OAAO,EAAEC,MAAM,EAAE;EAC1C,IAAIC,MAAM,GAAGF,OAAO,CAACG,MAAM;EAC3B,IAAIC,MAAM,GAAGJ,OAAO,CAACK,MAAM;EAC3B,IAAIC,MAAM,GAAGN,OAAO,CAACO,IAAI;EACzB,IAAIC,MAAM,EAAEL,MAAM,EAAEM,GAAG,EAAEC,cAAc,EAAEC,cAAc,EAAEC,UAAU;EACnE,IAAIR,MAAM,EAAE;IACVD,MAAM,GAAGd,UAAU;EACrB,CAAC,MAAM,IAAIiB,MAAM,EAAE;IACjBH,MAAM,GAAGd,UAAU,CAACa,MAAM,CAAC,IAAIP,oBAAoB,CAACO,MAAM,EAAE,CAAC,CAAC,CAAC;EACjE,CAAC,MAAM;IACLC,MAAM,GAAGd,UAAU,CAACa,MAAM,CAAC,IAAIb,UAAU,CAACa,MAAM,CAAC,CAACW,SAAS;EAC7D;EACA,IAAIV,MAAM,EAAE,KAAKM,GAAG,IAAIR,MAAM,EAAE;IAC9BU,cAAc,GAAGV,MAAM,CAACQ,GAAG,CAAC;IAC5B,IAAIT,OAAO,CAACc,cAAc,EAAE;MAC1BF,UAAU,GAAGrB,wBAAwB,CAACY,MAAM,EAAEM,GAAG,CAAC;MAClDC,cAAc,GAAGE,UAAU,IAAIA,UAAU,CAACG,KAAK;IACjD,CAAC,MAAML,cAAc,GAAGP,MAAM,CAACM,GAAG,CAAC;IACnCD,MAAM,GAAGX,QAAQ,CAACO,MAAM,GAAGK,GAAG,GAAGP,MAAM,IAAII,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGG,GAAG,EAAET,OAAO,CAACgB,MAAM,CAAC;IACrF;IACA,IAAI,CAACR,MAAM,IAAIE,cAAc,KAAKO,SAAS,EAAE;MAC3C,IAAI,OAAON,cAAc,IAAI,OAAOD,cAAc,EAAE;MACpDd,yBAAyB,CAACe,cAAc,EAAED,cAAc,CAAC;IAC3D;IACA;IACA,IAAIV,OAAO,CAACkB,IAAI,IAAKR,cAAc,IAAIA,cAAc,CAACQ,IAAK,EAAE;MAC3DzB,2BAA2B,CAACkB,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC;IAC3D;IACAjB,aAAa,CAACS,MAAM,EAAEM,GAAG,EAAEE,cAAc,EAAEX,OAAO,CAAC;EACrD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}