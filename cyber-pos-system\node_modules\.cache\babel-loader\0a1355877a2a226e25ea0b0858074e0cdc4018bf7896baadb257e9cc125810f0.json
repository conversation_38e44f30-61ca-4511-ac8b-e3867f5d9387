{"ast": null, "code": "'use strict';\n\nvar $ = require('../internals/export');\nvar exec = require('../internals/regexp-exec');\n\n// `RegExp.prototype.exec` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.exec\n$({\n  target: 'RegExp',\n  proto: true,\n  forced: /./.exec !== exec\n}, {\n  exec: exec\n});", "map": {"version": 3, "names": ["$", "require", "exec", "target", "proto", "forced"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/modules/es.regexp.exec.js"], "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar exec = require('../internals/regexp-exec');\n\n// `RegExp.prototype.exec` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.exec\n$({ target: 'RegExp', proto: true, forced: /./.exec !== exec }, {\n  exec: exec\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AACtC,IAAIC,IAAI,GAAGD,OAAO,CAAC,0BAA0B,CAAC;;AAE9C;AACA;AACAD,CAAC,CAAC;EAAEG,MAAM,EAAE,QAAQ;EAAEC,KAAK,EAAE,IAAI;EAAEC,MAAM,EAAE,GAAG,CAACH,IAAI,KAAKA;AAAK,CAAC,EAAE;EAC9DA,IAAI,EAAEA;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}