import React, { useState, useEffect } from 'react';
import {
  Package,
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  BarChart3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Filter
} from 'lucide-react';
import { useProducts } from '../../hooks/useProducts';
import { Product } from '../../types';

interface InventoryAlert {
  id: string;
  productName: string;
  currentStock: number;
  reorderLevel: number;
  category: string;
  alertType: 'low_stock' | 'out_of_stock' | 'expiring_soon';
  expiryDate?: Date;
}

interface InventoryMetrics {
  totalProducts: number;
  lowStockItems: number;
  outOfStockItems: number;
  expiringSoonItems: number;
  totalStockValue: number;
  averageStockLevel: number;
}

const InventoryAnalytics: React.FC = () => {
  const { products, loading } = useProducts();
  const [alerts, setAlerts] = useState<InventoryAlert[]>([]);
  const [metrics, setMetrics] = useState<InventoryMetrics | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [alertFilter, setAlertFilter] = useState<string>('all');

  useEffect(() => {
    if (products.length > 0) {
      calculateMetrics();
      generateAlerts();
    }
  }, [products]);

  const calculateMetrics = () => {
    const totalProducts = products.length;
    const lowStockItems = products.filter(p => p.stockQuantity <= p.reorderLevel && p.stockQuantity > 0).length;
    const outOfStockItems = products.filter(p => p.stockQuantity === 0).length;
    
    // Calculate expiring soon items (within 30 days)
    const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    const expiringSoonItems = products.filter(p => 
      p.hasExpiry && p.expiryDate && p.expiryDate <= thirtyDaysFromNow
    ).length;

    const totalStockValue = products.reduce((sum, p) => sum + (p.stockQuantity * p.price), 0);
    const averageStockLevel = totalProducts > 0 ? 
      products.reduce((sum, p) => sum + p.stockQuantity, 0) / totalProducts : 0;

    setMetrics({
      totalProducts,
      lowStockItems,
      outOfStockItems,
      expiringSoonItems,
      totalStockValue,
      averageStockLevel,
    });
  };

  const generateAlerts = () => {
    const newAlerts: InventoryAlert[] = [];
    const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

    products.forEach(product => {
      // Out of stock alert
      if (product.stockQuantity === 0) {
        newAlerts.push({
          id: `${product.id}-out-of-stock`,
          productName: product.name,
          currentStock: product.stockQuantity,
          reorderLevel: product.reorderLevel,
          category: product.category,
          alertType: 'out_of_stock',
        });
      }
      // Low stock alert
      else if (product.stockQuantity <= product.reorderLevel) {
        newAlerts.push({
          id: `${product.id}-low-stock`,
          productName: product.name,
          currentStock: product.stockQuantity,
          reorderLevel: product.reorderLevel,
          category: product.category,
          alertType: 'low_stock',
        });
      }

      // Expiring soon alert
      if (product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow) {
        newAlerts.push({
          id: `${product.id}-expiring`,
          productName: product.name,
          currentStock: product.stockQuantity,
          reorderLevel: product.reorderLevel,
          category: product.category,
          alertType: 'expiring_soon',
          expiryDate: product.expiryDate,
        });
      }
    });

    setAlerts(newAlerts);
  };

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'out_of_stock':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'low_stock':
        return <TrendingDown className="h-5 w-5 text-orange-600" />;
      case 'expiring_soon':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      default:
        return <Package className="h-5 w-5 text-gray-600" />;
    }
  };

  const getAlertColor = (alertType: string) => {
    switch (alertType) {
      case 'out_of_stock':
        return 'border-red-200 bg-red-50';
      case 'low_stock':
        return 'border-orange-200 bg-orange-50';
      case 'expiring_soon':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const categories = ['all', ...Array.from(new Set(products.map(p => p.category)))];
  const filteredAlerts = alerts.filter(alert => {
    const categoryMatch = selectedCategory === 'all' || alert.category === selectedCategory;
    const alertMatch = alertFilter === 'all' || alert.alertType === alertFilter;
    return categoryMatch && alertMatch;
  });

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 text-primary-600 animate-spin mr-3" />
          <span className="text-lg text-gray-600">Loading inventory data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Inventory Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Package className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Products</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.totalProducts}</p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Critical Alerts</p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics.outOfStockItems + metrics.lowStockItems}
                </p>
                <p className="text-xs text-gray-500">
                  {metrics.outOfStockItems} out of stock, {metrics.lowStockItems} low stock
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Stock Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  KSh {metrics.totalStockValue.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BarChart3 className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg. Stock Level</p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics.averageStockLevel.toFixed(0)}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Alerts */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
            Inventory Alerts ({filteredAlerts.length})
          </h3>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>
            
            <select
              value={alertFilter}
              onChange={(e) => setAlertFilter(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            >
              <option value="all">All Alerts</option>
              <option value="out_of_stock">Out of Stock</option>
              <option value="low_stock">Low Stock</option>
              <option value="expiring_soon">Expiring Soon</option>
            </select>
          </div>
        </div>

        <div className="space-y-3">
          {filteredAlerts.length > 0 ? (
            filteredAlerts.map((alert) => (
              <div
                key={alert.id}
                className={`border rounded-lg p-4 ${getAlertColor(alert.alertType)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {getAlertIcon(alert.alertType)}
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{alert.productName}</p>
                      <p className="text-xs text-gray-500">{alert.category}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      Stock: {alert.currentStock}
                    </p>
                    {alert.alertType !== 'expiring_soon' && (
                      <p className="text-xs text-gray-500">
                        Reorder at: {alert.reorderLevel}
                      </p>
                    )}
                    {alert.expiryDate && (
                      <p className="text-xs text-red-600">
                        Expires: {alert.expiryDate.toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No Alerts</h3>
              <p className="mt-1 text-sm text-gray-500">
                All inventory levels are within normal ranges.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InventoryAnalytics;
