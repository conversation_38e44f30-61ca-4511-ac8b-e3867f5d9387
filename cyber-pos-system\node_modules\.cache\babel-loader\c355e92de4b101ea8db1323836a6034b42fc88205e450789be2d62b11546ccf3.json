{"ast": null, "code": "'use strict';\n\nvar trunc = require('../internals/math-trunc');\n\n// `ToIntegerOrInfinity` abstract operation\n// https://tc39.es/ecma262/#sec-tointegerorinfinity\nmodule.exports = function (argument) {\n  var number = +argument;\n  // eslint-disable-next-line no-self-compare -- NaN check\n  return number !== number || number === 0 ? 0 : trunc(number);\n};", "map": {"version": 3, "names": ["trunc", "require", "module", "exports", "argument", "number"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/to-integer-or-infinity.js"], "sourcesContent": ["'use strict';\nvar trunc = require('../internals/math-trunc');\n\n// `ToIntegerOrInfinity` abstract operation\n// https://tc39.es/ecma262/#sec-tointegerorinfinity\nmodule.exports = function (argument) {\n  var number = +argument;\n  // eslint-disable-next-line no-self-compare -- NaN check\n  return number !== number || number === 0 ? 0 : trunc(number);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,yBAAyB,CAAC;;AAE9C;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIC,MAAM,GAAG,CAACD,QAAQ;EACtB;EACA,OAAOC,MAAM,KAAKA,MAAM,IAAIA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGL,KAAK,CAACK,MAAM,CAAC;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}