{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\nvar WeakMap = globalThis.WeakMap;\nmodule.exports = isCallable(WeakMap) && /native code/.test(String(WeakMap));", "map": {"version": 3, "names": ["globalThis", "require", "isCallable", "WeakMap", "module", "exports", "test", "String"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/weak-map-basic-detection.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\n\nvar WeakMap = globalThis.WeakMap;\n\nmodule.exports = isCallable(WeakMap) && /native code/.test(String(WeakMap));\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AAEpD,IAAIE,OAAO,GAAGH,UAAU,CAACG,OAAO;AAEhCC,MAAM,CAACC,OAAO,GAAGH,UAAU,CAACC,OAAO,CAAC,IAAI,aAAa,CAACG,IAAI,CAACC,MAAM,CAACJ,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}