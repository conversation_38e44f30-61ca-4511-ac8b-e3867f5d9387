{"ast": null, "code": "'use strict';\n\nvar hasOwn = require('../internals/has-own-property');\nvar isCallable = require('../internals/is-callable');\nvar toObject = require('../internals/to-object');\nvar sharedKey = require('../internals/shared-key');\nvar CORRECT_PROTOTYPE_GETTER = require('../internals/correct-prototype-getter');\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar $Object = Object;\nvar ObjectPrototype = $Object.prototype;\n\n// `Object.getPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.getprototypeof\n// eslint-disable-next-line es/no-object-getprototypeof -- safe\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? $Object.getPrototypeOf : function (O) {\n  var object = toObject(O);\n  if (hasOwn(object, IE_PROTO)) return object[IE_PROTO];\n  var constructor = object.constructor;\n  if (isCallable(constructor) && object instanceof constructor) {\n    return constructor.prototype;\n  }\n  return object instanceof $Object ? ObjectPrototype : null;\n};", "map": {"version": 3, "names": ["hasOwn", "require", "isCallable", "toObject", "sharedKey", "CORRECT_PROTOTYPE_GETTER", "IE_PROTO", "$Object", "Object", "ObjectPrototype", "prototype", "module", "exports", "getPrototypeOf", "O", "object", "constructor"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/object-get-prototype-of.js"], "sourcesContent": ["'use strict';\nvar hasOwn = require('../internals/has-own-property');\nvar isCallable = require('../internals/is-callable');\nvar toObject = require('../internals/to-object');\nvar sharedKey = require('../internals/shared-key');\nvar CORRECT_PROTOTYPE_GETTER = require('../internals/correct-prototype-getter');\n\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar $Object = Object;\nvar ObjectPrototype = $Object.prototype;\n\n// `Object.getPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.getprototypeof\n// eslint-disable-next-line es/no-object-getprototypeof -- safe\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? $Object.getPrototypeOf : function (O) {\n  var object = toObject(O);\n  if (hasOwn(object, IE_PROTO)) return object[IE_PROTO];\n  var constructor = object.constructor;\n  if (isCallable(constructor) && object instanceof constructor) {\n    return constructor.prototype;\n  } return object instanceof $Object ? ObjectPrototype : null;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AACrD,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIG,SAAS,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AAClD,IAAII,wBAAwB,GAAGJ,OAAO,CAAC,uCAAuC,CAAC;AAE/E,IAAIK,QAAQ,GAAGF,SAAS,CAAC,UAAU,CAAC;AACpC,IAAIG,OAAO,GAAGC,MAAM;AACpB,IAAIC,eAAe,GAAGF,OAAO,CAACG,SAAS;;AAEvC;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAGP,wBAAwB,GAAGE,OAAO,CAACM,cAAc,GAAG,UAAUC,CAAC,EAAE;EAChF,IAAIC,MAAM,GAAGZ,QAAQ,CAACW,CAAC,CAAC;EACxB,IAAId,MAAM,CAACe,MAAM,EAAET,QAAQ,CAAC,EAAE,OAAOS,MAAM,CAACT,QAAQ,CAAC;EACrD,IAAIU,WAAW,GAAGD,MAAM,CAACC,WAAW;EACpC,IAAId,UAAU,CAACc,WAAW,CAAC,IAAID,MAAM,YAAYC,WAAW,EAAE;IAC5D,OAAOA,WAAW,CAACN,SAAS;EAC9B;EAAE,OAAOK,MAAM,YAAYR,OAAO,GAAGE,eAAe,GAAG,IAAI;AAC7D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}