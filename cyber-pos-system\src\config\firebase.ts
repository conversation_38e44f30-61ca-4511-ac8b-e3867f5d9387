import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import {
  getFirestore,
  enableNetwork,
  disableNetwork,
  connectFirestoreEmulator,
  persistentLocalCache
} from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Firebase configuration
// For demo purposes, using Firebase emulator
// TODO: Replace with your actual Firebase config for production
const firebaseConfig = {
  apiKey: "demo-api-key",
  authDomain: "demo-project.firebaseapp.com",
  projectId: "demo-cyber-pos",
  storageBucket: "demo-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "demo-app-id"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services with modern persistence
export const auth = getAuth(app);
export const db = getFirestore(app, {
  localCache: persistentLocalCache({
    // Default tab manager supports multiple tabs automatically
  })
});
export const storage = getStorage(app);

// Connect to emulators in development
if (process.env.NODE_ENV === 'development') {
  try {
    // Only connect if not already connected
    // Check if emulator is already connected by trying to connect
    connectAuthEmulator(auth, 'http://localhost:9099');
    // Note: Firestore emulator connection would go here if using emulator
    // connectFirestoreEmulator(db, 'localhost', 8080);
  } catch (error) {
    console.log('Emulators not available, using production Firebase');
  }
}

// Offline persistence is now automatically enabled through FirestoreSettings.localCache
// No need for a separate enableOfflineSupport function with the new approach
export const enableOfflineSupport = async () => {
  // This function is kept for backward compatibility but is no longer needed
  // Persistence is automatically enabled through the localCache configuration above
  console.log('Firebase offline persistence is automatically enabled with the new configuration');
};

// Network status management
export const goOffline = async () => {
  try {
    await disableNetwork(db);
    console.log('Firebase network disabled');
  } catch (error) {
    console.error('Error disabling network:', error);
  }
};

export const goOnline = async () => {
  try {
    await enableNetwork(db);
    console.log('Firebase network enabled');
  } catch (error) {
    console.error('Error enabling network:', error);
  }
};

export default app;
