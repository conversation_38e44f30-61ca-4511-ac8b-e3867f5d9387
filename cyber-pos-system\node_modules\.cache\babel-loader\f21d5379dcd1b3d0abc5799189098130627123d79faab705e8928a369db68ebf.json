{"ast": null, "code": "'use strict';\n\n/* eslint-disable regexp/no-empty-capturing-group, regexp/no-empty-group, regexp/no-lazy-ends -- testing */\n/* eslint-disable regexp/no-useless-quantifier -- testing */\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toString = require('../internals/to-string');\nvar regexpFlags = require('../internals/regexp-flags');\nvar stickyHelpers = require('../internals/regexp-sticky-helpers');\nvar shared = require('../internals/shared');\nvar create = require('../internals/object-create');\nvar getInternalState = require('../internals/internal-state').get;\nvar UNSUPPORTED_DOT_ALL = require('../internals/regexp-unsupported-dot-all');\nvar UNSUPPORTED_NCG = require('../internals/regexp-unsupported-ncg');\nvar nativeReplace = shared('native-string-replace', String.prototype.replace);\nvar nativeExec = RegExp.prototype.exec;\nvar patchedExec = nativeExec;\nvar charAt = uncurryThis(''.charAt);\nvar indexOf = uncurryThis(''.indexOf);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\nvar UPDATES_LAST_INDEX_WRONG = function () {\n  var re1 = /a/;\n  var re2 = /b*/g;\n  call(nativeExec, re1, 'a');\n  call(nativeExec, re2, 'a');\n  return re1.lastIndex !== 0 || re2.lastIndex !== 0;\n}();\nvar UNSUPPORTED_Y = stickyHelpers.BROKEN_CARET;\n\n// nonparticipating capturing group, copied from es5-shim's String#split patch.\nvar NPCG_INCLUDED = /()??/.exec('')[1] !== undefined;\nvar PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED || UNSUPPORTED_Y || UNSUPPORTED_DOT_ALL || UNSUPPORTED_NCG;\nif (PATCH) {\n  patchedExec = function exec(string) {\n    var re = this;\n    var state = getInternalState(re);\n    var str = toString(string);\n    var raw = state.raw;\n    var result, reCopy, lastIndex, match, i, object, group;\n    if (raw) {\n      raw.lastIndex = re.lastIndex;\n      result = call(patchedExec, raw, str);\n      re.lastIndex = raw.lastIndex;\n      return result;\n    }\n    var groups = state.groups;\n    var sticky = UNSUPPORTED_Y && re.sticky;\n    var flags = call(regexpFlags, re);\n    var source = re.source;\n    var charsAdded = 0;\n    var strCopy = str;\n    if (sticky) {\n      flags = replace(flags, 'y', '');\n      if (indexOf(flags, 'g') === -1) {\n        flags += 'g';\n      }\n      strCopy = stringSlice(str, re.lastIndex);\n      // Support anchored sticky behavior.\n      if (re.lastIndex > 0 && (!re.multiline || re.multiline && charAt(str, re.lastIndex - 1) !== '\\n')) {\n        source = '(?: ' + source + ')';\n        strCopy = ' ' + strCopy;\n        charsAdded++;\n      }\n      // ^(? + rx + ) is needed, in combination with some str slicing, to\n      // simulate the 'y' flag.\n      reCopy = new RegExp('^(?:' + source + ')', flags);\n    }\n    if (NPCG_INCLUDED) {\n      reCopy = new RegExp('^' + source + '$(?!\\\\s)', flags);\n    }\n    if (UPDATES_LAST_INDEX_WRONG) lastIndex = re.lastIndex;\n    match = call(nativeExec, sticky ? reCopy : re, strCopy);\n    if (sticky) {\n      if (match) {\n        match.input = stringSlice(match.input, charsAdded);\n        match[0] = stringSlice(match[0], charsAdded);\n        match.index = re.lastIndex;\n        re.lastIndex += match[0].length;\n      } else re.lastIndex = 0;\n    } else if (UPDATES_LAST_INDEX_WRONG && match) {\n      re.lastIndex = re.global ? match.index + match[0].length : lastIndex;\n    }\n    if (NPCG_INCLUDED && match && match.length > 1) {\n      // Fix browsers whose `exec` methods don't consistently return `undefined`\n      // for NPCG, like IE8. NOTE: This doesn't work for /(.?)?/\n      call(nativeReplace, match[0], reCopy, function () {\n        for (i = 1; i < arguments.length - 2; i++) {\n          if (arguments[i] === undefined) match[i] = undefined;\n        }\n      });\n    }\n    if (match && groups) {\n      match.groups = object = create(null);\n      for (i = 0; i < groups.length; i++) {\n        group = groups[i];\n        object[group[0]] = match[group[1]];\n      }\n    }\n    return match;\n  };\n}\nmodule.exports = patchedExec;", "map": {"version": 3, "names": ["call", "require", "uncurryThis", "toString", "regexpFlags", "stickyHelpers", "shared", "create", "getInternalState", "get", "UNSUPPORTED_DOT_ALL", "UNSUPPORTED_NCG", "nativeReplace", "String", "prototype", "replace", "nativeExec", "RegExp", "exec", "patchedExec", "char<PERSON>t", "indexOf", "stringSlice", "slice", "UPDATES_LAST_INDEX_WRONG", "re1", "re2", "lastIndex", "UNSUPPORTED_Y", "BROKEN_CARET", "NPCG_INCLUDED", "undefined", "PATCH", "string", "re", "state", "str", "raw", "result", "reCopy", "match", "i", "object", "group", "groups", "sticky", "flags", "source", "charsAdded", "strCopy", "multiline", "input", "index", "length", "global", "arguments", "module", "exports"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/regexp-exec.js"], "sourcesContent": ["'use strict';\n/* eslint-disable regexp/no-empty-capturing-group, regexp/no-empty-group, regexp/no-lazy-ends -- testing */\n/* eslint-disable regexp/no-useless-quantifier -- testing */\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toString = require('../internals/to-string');\nvar regexpFlags = require('../internals/regexp-flags');\nvar stickyHelpers = require('../internals/regexp-sticky-helpers');\nvar shared = require('../internals/shared');\nvar create = require('../internals/object-create');\nvar getInternalState = require('../internals/internal-state').get;\nvar UNSUPPORTED_DOT_ALL = require('../internals/regexp-unsupported-dot-all');\nvar UNSUPPORTED_NCG = require('../internals/regexp-unsupported-ncg');\n\nvar nativeReplace = shared('native-string-replace', String.prototype.replace);\nvar nativeExec = RegExp.prototype.exec;\nvar patchedExec = nativeExec;\nvar charAt = uncurryThis(''.charAt);\nvar indexOf = uncurryThis(''.indexOf);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\n\nvar UPDATES_LAST_INDEX_WRONG = (function () {\n  var re1 = /a/;\n  var re2 = /b*/g;\n  call(nativeExec, re1, 'a');\n  call(nativeExec, re2, 'a');\n  return re1.lastIndex !== 0 || re2.lastIndex !== 0;\n})();\n\nvar UNSUPPORTED_Y = stickyHelpers.BROKEN_CARET;\n\n// nonparticipating capturing group, copied from es5-shim's String#split patch.\nvar NPCG_INCLUDED = /()??/.exec('')[1] !== undefined;\n\nvar PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED || UNSUPPORTED_Y || UNSUPPORTED_DOT_ALL || UNSUPPORTED_NCG;\n\nif (PATCH) {\n  patchedExec = function exec(string) {\n    var re = this;\n    var state = getInternalState(re);\n    var str = toString(string);\n    var raw = state.raw;\n    var result, reCopy, lastIndex, match, i, object, group;\n\n    if (raw) {\n      raw.lastIndex = re.lastIndex;\n      result = call(patchedExec, raw, str);\n      re.lastIndex = raw.lastIndex;\n      return result;\n    }\n\n    var groups = state.groups;\n    var sticky = UNSUPPORTED_Y && re.sticky;\n    var flags = call(regexpFlags, re);\n    var source = re.source;\n    var charsAdded = 0;\n    var strCopy = str;\n\n    if (sticky) {\n      flags = replace(flags, 'y', '');\n      if (indexOf(flags, 'g') === -1) {\n        flags += 'g';\n      }\n\n      strCopy = stringSlice(str, re.lastIndex);\n      // Support anchored sticky behavior.\n      if (re.lastIndex > 0 && (!re.multiline || re.multiline && charAt(str, re.lastIndex - 1) !== '\\n')) {\n        source = '(?: ' + source + ')';\n        strCopy = ' ' + strCopy;\n        charsAdded++;\n      }\n      // ^(? + rx + ) is needed, in combination with some str slicing, to\n      // simulate the 'y' flag.\n      reCopy = new RegExp('^(?:' + source + ')', flags);\n    }\n\n    if (NPCG_INCLUDED) {\n      reCopy = new RegExp('^' + source + '$(?!\\\\s)', flags);\n    }\n    if (UPDATES_LAST_INDEX_WRONG) lastIndex = re.lastIndex;\n\n    match = call(nativeExec, sticky ? reCopy : re, strCopy);\n\n    if (sticky) {\n      if (match) {\n        match.input = stringSlice(match.input, charsAdded);\n        match[0] = stringSlice(match[0], charsAdded);\n        match.index = re.lastIndex;\n        re.lastIndex += match[0].length;\n      } else re.lastIndex = 0;\n    } else if (UPDATES_LAST_INDEX_WRONG && match) {\n      re.lastIndex = re.global ? match.index + match[0].length : lastIndex;\n    }\n    if (NPCG_INCLUDED && match && match.length > 1) {\n      // Fix browsers whose `exec` methods don't consistently return `undefined`\n      // for NPCG, like IE8. NOTE: This doesn't work for /(.?)?/\n      call(nativeReplace, match[0], reCopy, function () {\n        for (i = 1; i < arguments.length - 2; i++) {\n          if (arguments[i] === undefined) match[i] = undefined;\n        }\n      });\n    }\n\n    if (match && groups) {\n      match.groups = object = create(null);\n      for (i = 0; i < groups.length; i++) {\n        group = groups[i];\n        object[group[0]] = match[group[1]];\n      }\n    }\n\n    return match;\n  };\n}\n\nmodule.exports = patchedExec;\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA,IAAIA,IAAI,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIC,WAAW,GAAGD,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIG,WAAW,GAAGH,OAAO,CAAC,2BAA2B,CAAC;AACtD,IAAII,aAAa,GAAGJ,OAAO,CAAC,oCAAoC,CAAC;AACjE,IAAIK,MAAM,GAAGL,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIM,MAAM,GAAGN,OAAO,CAAC,4BAA4B,CAAC;AAClD,IAAIO,gBAAgB,GAAGP,OAAO,CAAC,6BAA6B,CAAC,CAACQ,GAAG;AACjE,IAAIC,mBAAmB,GAAGT,OAAO,CAAC,yCAAyC,CAAC;AAC5E,IAAIU,eAAe,GAAGV,OAAO,CAAC,qCAAqC,CAAC;AAEpE,IAAIW,aAAa,GAAGN,MAAM,CAAC,uBAAuB,EAAEO,MAAM,CAACC,SAAS,CAACC,OAAO,CAAC;AAC7E,IAAIC,UAAU,GAAGC,MAAM,CAACH,SAAS,CAACI,IAAI;AACtC,IAAIC,WAAW,GAAGH,UAAU;AAC5B,IAAII,MAAM,GAAGlB,WAAW,CAAC,EAAE,CAACkB,MAAM,CAAC;AACnC,IAAIC,OAAO,GAAGnB,WAAW,CAAC,EAAE,CAACmB,OAAO,CAAC;AACrC,IAAIN,OAAO,GAAGb,WAAW,CAAC,EAAE,CAACa,OAAO,CAAC;AACrC,IAAIO,WAAW,GAAGpB,WAAW,CAAC,EAAE,CAACqB,KAAK,CAAC;AAEvC,IAAIC,wBAAwB,GAAI,YAAY;EAC1C,IAAIC,GAAG,GAAG,GAAG;EACb,IAAIC,GAAG,GAAG,KAAK;EACf1B,IAAI,CAACgB,UAAU,EAAES,GAAG,EAAE,GAAG,CAAC;EAC1BzB,IAAI,CAACgB,UAAU,EAAEU,GAAG,EAAE,GAAG,CAAC;EAC1B,OAAOD,GAAG,CAACE,SAAS,KAAK,CAAC,IAAID,GAAG,CAACC,SAAS,KAAK,CAAC;AACnD,CAAC,CAAE,CAAC;AAEJ,IAAIC,aAAa,GAAGvB,aAAa,CAACwB,YAAY;;AAE9C;AACA,IAAIC,aAAa,GAAG,MAAM,CAACZ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAKa,SAAS;AAEpD,IAAIC,KAAK,GAAGR,wBAAwB,IAAIM,aAAa,IAAIF,aAAa,IAAIlB,mBAAmB,IAAIC,eAAe;AAEhH,IAAIqB,KAAK,EAAE;EACTb,WAAW,GAAG,SAASD,IAAIA,CAACe,MAAM,EAAE;IAClC,IAAIC,EAAE,GAAG,IAAI;IACb,IAAIC,KAAK,GAAG3B,gBAAgB,CAAC0B,EAAE,CAAC;IAChC,IAAIE,GAAG,GAAGjC,QAAQ,CAAC8B,MAAM,CAAC;IAC1B,IAAII,GAAG,GAAGF,KAAK,CAACE,GAAG;IACnB,IAAIC,MAAM,EAAEC,MAAM,EAAEZ,SAAS,EAAEa,KAAK,EAAEC,CAAC,EAAEC,MAAM,EAAEC,KAAK;IAEtD,IAAIN,GAAG,EAAE;MACPA,GAAG,CAACV,SAAS,GAAGO,EAAE,CAACP,SAAS;MAC5BW,MAAM,GAAGtC,IAAI,CAACmB,WAAW,EAAEkB,GAAG,EAAED,GAAG,CAAC;MACpCF,EAAE,CAACP,SAAS,GAAGU,GAAG,CAACV,SAAS;MAC5B,OAAOW,MAAM;IACf;IAEA,IAAIM,MAAM,GAAGT,KAAK,CAACS,MAAM;IACzB,IAAIC,MAAM,GAAGjB,aAAa,IAAIM,EAAE,CAACW,MAAM;IACvC,IAAIC,KAAK,GAAG9C,IAAI,CAACI,WAAW,EAAE8B,EAAE,CAAC;IACjC,IAAIa,MAAM,GAAGb,EAAE,CAACa,MAAM;IACtB,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,OAAO,GAAGb,GAAG;IAEjB,IAAIS,MAAM,EAAE;MACVC,KAAK,GAAG/B,OAAO,CAAC+B,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC;MAC/B,IAAIzB,OAAO,CAACyB,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9BA,KAAK,IAAI,GAAG;MACd;MAEAG,OAAO,GAAG3B,WAAW,CAACc,GAAG,EAAEF,EAAE,CAACP,SAAS,CAAC;MACxC;MACA,IAAIO,EAAE,CAACP,SAAS,GAAG,CAAC,KAAK,CAACO,EAAE,CAACgB,SAAS,IAAIhB,EAAE,CAACgB,SAAS,IAAI9B,MAAM,CAACgB,GAAG,EAAEF,EAAE,CAACP,SAAS,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE;QACjGoB,MAAM,GAAG,MAAM,GAAGA,MAAM,GAAG,GAAG;QAC9BE,OAAO,GAAG,GAAG,GAAGA,OAAO;QACvBD,UAAU,EAAE;MACd;MACA;MACA;MACAT,MAAM,GAAG,IAAItB,MAAM,CAAC,MAAM,GAAG8B,MAAM,GAAG,GAAG,EAAED,KAAK,CAAC;IACnD;IAEA,IAAIhB,aAAa,EAAE;MACjBS,MAAM,GAAG,IAAItB,MAAM,CAAC,GAAG,GAAG8B,MAAM,GAAG,UAAU,EAAED,KAAK,CAAC;IACvD;IACA,IAAItB,wBAAwB,EAAEG,SAAS,GAAGO,EAAE,CAACP,SAAS;IAEtDa,KAAK,GAAGxC,IAAI,CAACgB,UAAU,EAAE6B,MAAM,GAAGN,MAAM,GAAGL,EAAE,EAAEe,OAAO,CAAC;IAEvD,IAAIJ,MAAM,EAAE;MACV,IAAIL,KAAK,EAAE;QACTA,KAAK,CAACW,KAAK,GAAG7B,WAAW,CAACkB,KAAK,CAACW,KAAK,EAAEH,UAAU,CAAC;QAClDR,KAAK,CAAC,CAAC,CAAC,GAAGlB,WAAW,CAACkB,KAAK,CAAC,CAAC,CAAC,EAAEQ,UAAU,CAAC;QAC5CR,KAAK,CAACY,KAAK,GAAGlB,EAAE,CAACP,SAAS;QAC1BO,EAAE,CAACP,SAAS,IAAIa,KAAK,CAAC,CAAC,CAAC,CAACa,MAAM;MACjC,CAAC,MAAMnB,EAAE,CAACP,SAAS,GAAG,CAAC;IACzB,CAAC,MAAM,IAAIH,wBAAwB,IAAIgB,KAAK,EAAE;MAC5CN,EAAE,CAACP,SAAS,GAAGO,EAAE,CAACoB,MAAM,GAAGd,KAAK,CAACY,KAAK,GAAGZ,KAAK,CAAC,CAAC,CAAC,CAACa,MAAM,GAAG1B,SAAS;IACtE;IACA,IAAIG,aAAa,IAAIU,KAAK,IAAIA,KAAK,CAACa,MAAM,GAAG,CAAC,EAAE;MAC9C;MACA;MACArD,IAAI,CAACY,aAAa,EAAE4B,KAAK,CAAC,CAAC,CAAC,EAAED,MAAM,EAAE,YAAY;QAChD,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,SAAS,CAACF,MAAM,GAAG,CAAC,EAAEZ,CAAC,EAAE,EAAE;UACzC,IAAIc,SAAS,CAACd,CAAC,CAAC,KAAKV,SAAS,EAAES,KAAK,CAACC,CAAC,CAAC,GAAGV,SAAS;QACtD;MACF,CAAC,CAAC;IACJ;IAEA,IAAIS,KAAK,IAAII,MAAM,EAAE;MACnBJ,KAAK,CAACI,MAAM,GAAGF,MAAM,GAAGnC,MAAM,CAAC,IAAI,CAAC;MACpC,KAAKkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,MAAM,CAACS,MAAM,EAAEZ,CAAC,EAAE,EAAE;QAClCE,KAAK,GAAGC,MAAM,CAACH,CAAC,CAAC;QACjBC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGH,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;MACpC;IACF;IAEA,OAAOH,KAAK;EACd,CAAC;AACH;AAEAgB,MAAM,CAACC,OAAO,GAAGtC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}