{"ast": null, "code": "'use strict';\n\nvar shared = require('../internals/shared');\nvar uid = require('../internals/uid');\nvar keys = shared('keys');\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};", "map": {"version": 3, "names": ["shared", "require", "uid", "keys", "module", "exports", "key"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/shared-key.js"], "sourcesContent": ["'use strict';\nvar shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,GAAG,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAErC,IAAIE,IAAI,GAAGH,MAAM,CAAC,MAAM,CAAC;AAEzBI,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC9B,OAAOH,IAAI,CAACG,GAAG,CAAC,KAAKH,IAAI,CAACG,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC,CAAC;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}