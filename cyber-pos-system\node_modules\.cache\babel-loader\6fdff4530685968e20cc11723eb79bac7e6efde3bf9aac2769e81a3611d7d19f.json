{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\nmodule.exports = function (object, key, method) {\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));\n  } catch (error) {/* empty */}\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "aCallable", "module", "exports", "object", "key", "method", "Object", "getOwnPropertyDescriptor", "error"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/function-uncurry-this-accessor.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nmodule.exports = function (object, key, method) {\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));\n  } catch (error) { /* empty */ }\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIC,SAAS,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAElDE,MAAM,CAACC,OAAO,GAAG,UAAUC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC9C,IAAI;IACF;IACA,OAAOP,WAAW,CAACE,SAAS,CAACM,MAAM,CAACC,wBAAwB,CAACJ,MAAM,EAAEC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;EACrF,CAAC,CAAC,OAAOG,KAAK,EAAE,CAAE;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}