{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Package,AlertTriangle,TrendingDown,TrendingUp,BarChart3,Refresh<PERSON><PERSON>,Filter}from'lucide-react';import{useProducts}from'../../hooks/useProducts';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const InventoryAnalytics=()=>{const{products,loading}=useProducts();const[alerts,setAlerts]=useState([]);const[metrics,setMetrics]=useState(null);const[selectedCategory,setSelectedCategory]=useState('all');const[alertFilter,setAlertFilter]=useState('all');useEffect(()=>{if(products.length>0){calculateMetrics();generateAlerts();}},[products]);const calculateMetrics=()=>{const totalProducts=products.length;const lowStockItems=products.filter(p=>p.stockQuantity<=p.reorderLevel&&p.stockQuantity>0).length;const outOfStockItems=products.filter(p=>p.stockQuantity===0).length;// Calculate expiring soon items (within 30 days)\nconst thirtyDaysFromNow=new Date();const newDate=thirtyDaysFromNow.getDate()+30;thirtyDaysFromNow.setDate(newDate);const expiringSoonItems=products.filter(p=>p.hasExpiry&&p.expiryDate&&p.expiryDate<=thirtyDaysFromNow).length;const totalStockValue=products.reduce((sum,p)=>sum+p.stockQuantity*p.price,0);const averageStockLevel=totalProducts>0?products.reduce((sum,p)=>sum+p.stockQuantity,0)/totalProducts:0;setMetrics({totalProducts,lowStockItems,outOfStockItems,expiringSoonItems,totalStockValue,averageStockLevel});};const generateAlerts=()=>{const newAlerts=[];const thirtyDaysFromNow=new Date();const newDate=thirtyDaysFromNow.getDate()+30;thirtyDaysFromNow.setDate(newDate);products.forEach(product=>{// Out of stock alert\nif(product.stockQuantity===0){newAlerts.push({id:\"\".concat(product.id,\"-out-of-stock\"),productName:product.name,currentStock:product.stockQuantity,reorderLevel:product.reorderLevel,category:product.category,alertType:'out_of_stock'});}// Low stock alert\nelse if(product.stockQuantity<=product.reorderLevel){newAlerts.push({id:\"\".concat(product.id,\"-low-stock\"),productName:product.name,currentStock:product.stockQuantity,reorderLevel:product.reorderLevel,category:product.category,alertType:'low_stock'});}// Expiring soon alert\nif(product.hasExpiry&&product.expiryDate&&product.expiryDate<=thirtyDaysFromNow){newAlerts.push({id:\"\".concat(product.id,\"-expiring\"),productName:product.name,currentStock:product.stockQuantity,reorderLevel:product.reorderLevel,category:product.category,alertType:'expiring_soon',expiryDate:product.expiryDate});}});setAlerts(newAlerts);};const getAlertIcon=alertType=>{switch(alertType){case'out_of_stock':return/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-5 w-5 text-red-600\"});case'low_stock':return/*#__PURE__*/_jsx(TrendingDown,{className:\"h-5 w-5 text-orange-600\"});case'expiring_soon':return/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-5 w-5 text-yellow-600\"});default:return/*#__PURE__*/_jsx(Package,{className:\"h-5 w-5 text-gray-600\"});}};const getAlertColor=alertType=>{switch(alertType){case'out_of_stock':return'border-red-200 bg-red-50';case'low_stock':return'border-orange-200 bg-orange-50';case'expiring_soon':return'border-yellow-200 bg-yellow-50';default:return'border-gray-200 bg-gray-50';}};const categories=['all',...Array.from(new Set(products.map(p=>p.category)))];const filteredAlerts=alerts.filter(alert=>{const categoryMatch=selectedCategory==='all'||alert.category===selectedCategory;const alertMatch=alertFilter==='all'||alert.alertType===alertFilter;return categoryMatch&&alertMatch;});if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center py-12\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"h-8 w-8 text-primary-600 animate-spin mr-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-lg text-gray-600\",children:\"Loading inventory data...\"})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[metrics&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(Package,{className:\"h-8 w-8 text-blue-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-500\",children:\"Total Products\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:metrics.totalProducts})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-8 w-8 text-red-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-500\",children:\"Critical Alerts\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:metrics.outOfStockItems+metrics.lowStockItems}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[metrics.outOfStockItems,\" out of stock, \",metrics.lowStockItems,\" low stock\"]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(TrendingUp,{className:\"h-8 w-8 text-green-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-500\",children:\"Stock Value\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:[\"KSh \",metrics.totalStockValue.toLocaleString()]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(BarChart3,{className:\"h-8 w-8 text-purple-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-500\",children:\"Avg. Stock Level\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:metrics.averageStockLevel.toFixed(0)})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 flex items-center\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-5 w-5 mr-2 text-orange-600\"}),\"Inventory Alerts (\",filteredAlerts.length,\")\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Filter,{className:\"h-4 w-4 text-gray-500\"}),/*#__PURE__*/_jsx(\"select\",{value:selectedCategory,onChange:e=>setSelectedCategory(e.target.value),className:\"border border-gray-300 rounded-md px-3 py-1 text-sm\",children:categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category,children:category==='all'?'All Categories':category},category))})]}),/*#__PURE__*/_jsxs(\"select\",{value:alertFilter,onChange:e=>setAlertFilter(e.target.value),className:\"border border-gray-300 rounded-md px-3 py-1 text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Alerts\"}),/*#__PURE__*/_jsx(\"option\",{value:\"out_of_stock\",children:\"Out of Stock\"}),/*#__PURE__*/_jsx(\"option\",{value:\"low_stock\",children:\"Low Stock\"}),/*#__PURE__*/_jsx(\"option\",{value:\"expiring_soon\",children:\"Expiring Soon\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:filteredAlerts.length>0?filteredAlerts.map(alert=>/*#__PURE__*/_jsx(\"div\",{className:\"border rounded-lg p-4 \".concat(getAlertColor(alert.alertType)),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[getAlertIcon(alert.alertType),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900\",children:alert.productName}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:alert.category})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm font-medium text-gray-900\",children:[\"Stock: \",alert.currentStock]}),alert.alertType!=='expiring_soon'&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[\"Reorder at: \",alert.reorderLevel]}),alert.expiryDate&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-red-600\",children:[\"Expires: \",alert.expiryDate.toLocaleDateString()]})]})]})},alert.id)):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(Package,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"No Alerts\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"All inventory levels are within normal ranges.\"})]})})]})]});};export default InventoryAnalytics;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Package", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrendingDown", "TrendingUp", "BarChart3", "RefreshCw", "Filter", "useProducts", "jsx", "_jsx", "jsxs", "_jsxs", "InventoryAnalytics", "products", "loading", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "metrics", "setMetrics", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "alertFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "calculateMetrics", "generateAlerts", "totalProducts", "lowStockItems", "filter", "p", "stockQuantity", "reorderLevel", "outOfStockItems", "thirtyDaysFromNow", "Date", "newDate", "getDate", "setDate", "expiringSoonItems", "hasEx<PERSON>ry", "expiryDate", "totalStockValue", "reduce", "sum", "price", "averageStockLevel", "new<PERSON><PERSON><PERSON>", "for<PERSON>ach", "product", "push", "id", "concat", "productName", "name", "currentStock", "category", "alertType", "getAlertIcon", "className", "getAlertColor", "categories", "Array", "from", "Set", "map", "filtered<PERSON>lerts", "alert", "categoryMatch", "alertMatch", "children", "toLocaleString", "toFixed", "value", "onChange", "e", "target", "toLocaleDateString"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/components/reports/InventoryAnalytics.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Package,\n  AlertTriangle,\n  TrendingDown,\n  TrendingUp,\n  BarChart3,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Filter\n} from 'lucide-react';\nimport { useProducts } from '../../hooks/useProducts';\nimport { Product } from '../../types';\n\ninterface InventoryAlert {\n  id: string;\n  productName: string;\n  currentStock: number;\n  reorderLevel: number;\n  category: string;\n  alertType: 'low_stock' | 'out_of_stock' | 'expiring_soon';\n  expiryDate?: Date;\n}\n\ninterface InventoryMetrics {\n  totalProducts: number;\n  lowStockItems: number;\n  outOfStockItems: number;\n  expiringSoonItems: number;\n  totalStockValue: number;\n  averageStockLevel: number;\n}\n\nconst InventoryAnalytics: React.FC = () => {\n  const { products, loading } = useProducts();\n  const [alerts, setAlerts] = useState<InventoryAlert[]>([]);\n  const [metrics, setMetrics] = useState<InventoryMetrics | null>(null);\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [alertFilter, setAlertFilter] = useState<string>('all');\n\n  useEffect(() => {\n    if (products.length > 0) {\n      calculateMetrics();\n      generateAlerts();\n    }\n  }, [products]);\n\n  const calculateMetrics = () => {\n    const totalProducts = products.length;\n    const lowStockItems = products.filter(p => p.stockQuantity <= p.reorderLevel && p.stockQuantity > 0).length;\n    const outOfStockItems = products.filter(p => p.stockQuantity === 0).length;\n    \n    // Calculate expiring soon items (within 30 days)\n    const thirtyDaysFromNow = new Date();\n    const newDate = thirtyDaysFromNow.getDate() + 30;\n    thirtyDaysFromNow.setDate(newDate);\n    const expiringSoonItems = products.filter(p => \n      p.hasExpiry && p.expiryDate && p.expiryDate <= thirtyDaysFromNow\n    ).length;\n\n    const totalStockValue = products.reduce((sum, p) => sum + (p.stockQuantity * p.price), 0);\n    const averageStockLevel = totalProducts > 0 ? \n      products.reduce((sum, p) => sum + p.stockQuantity, 0) / totalProducts : 0;\n\n    setMetrics({\n      totalProducts,\n      lowStockItems,\n      outOfStockItems,\n      expiringSoonItems,\n      totalStockValue,\n      averageStockLevel,\n    });\n  };\n\n  const generateAlerts = () => {\n    const newAlerts: InventoryAlert[] = [];\n    const thirtyDaysFromNow = new Date();\n    const newDate = thirtyDaysFromNow.getDate() + 30;\n    thirtyDaysFromNow.setDate(newDate);\n\n    products.forEach(product => {\n      // Out of stock alert\n      if (product.stockQuantity === 0) {\n        newAlerts.push({\n          id: `${product.id}-out-of-stock`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'out_of_stock',\n        });\n      }\n      // Low stock alert\n      else if (product.stockQuantity <= product.reorderLevel) {\n        newAlerts.push({\n          id: `${product.id}-low-stock`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'low_stock',\n        });\n      }\n\n      // Expiring soon alert\n      if (product.hasExpiry && product.expiryDate && product.expiryDate <= thirtyDaysFromNow) {\n        newAlerts.push({\n          id: `${product.id}-expiring`,\n          productName: product.name,\n          currentStock: product.stockQuantity,\n          reorderLevel: product.reorderLevel,\n          category: product.category,\n          alertType: 'expiring_soon',\n          expiryDate: product.expiryDate,\n        });\n      }\n    });\n\n    setAlerts(newAlerts);\n  };\n\n  const getAlertIcon = (alertType: string) => {\n    switch (alertType) {\n      case 'out_of_stock':\n        return <AlertTriangle className=\"h-5 w-5 text-red-600\" />;\n      case 'low_stock':\n        return <TrendingDown className=\"h-5 w-5 text-orange-600\" />;\n      case 'expiring_soon':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />;\n      default:\n        return <Package className=\"h-5 w-5 text-gray-600\" />;\n    }\n  };\n\n  const getAlertColor = (alertType: string) => {\n    switch (alertType) {\n      case 'out_of_stock':\n        return 'border-red-200 bg-red-50';\n      case 'low_stock':\n        return 'border-orange-200 bg-orange-50';\n      case 'expiring_soon':\n        return 'border-yellow-200 bg-yellow-50';\n      default:\n        return 'border-gray-200 bg-gray-50';\n    }\n  };\n\n  const categories = ['all', ...Array.from(new Set(products.map(p => p.category)))];\n  const filteredAlerts = alerts.filter(alert => {\n    const categoryMatch = selectedCategory === 'all' || alert.category === selectedCategory;\n    const alertMatch = alertFilter === 'all' || alert.alertType === alertFilter;\n    return categoryMatch && alertMatch;\n  });\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-center py-12\">\n          <RefreshCw className=\"h-8 w-8 text-primary-600 animate-spin mr-3\" />\n          <span className=\"text-lg text-gray-600\">Loading inventory data...</span>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Inventory Metrics */}\n      {metrics && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Package className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Total Products</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{metrics.totalProducts}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Critical Alerts</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {metrics.outOfStockItems + metrics.lowStockItems}\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  {metrics.outOfStockItems} out of stock, {metrics.lowStockItems} low stock\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <TrendingUp className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Stock Value</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  KSh {metrics.totalStockValue.toLocaleString()}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <BarChart3 className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Avg. Stock Level</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {metrics.averageStockLevel.toFixed(0)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Filters and Alerts */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n            <AlertTriangle className=\"h-5 w-5 mr-2 text-orange-600\" />\n            Inventory Alerts ({filteredAlerts.length})\n          </h3>\n          \n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-4 w-4 text-gray-500\" />\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n              >\n                {categories.map(category => (\n                  <option key={category} value={category}>\n                    {category === 'all' ? 'All Categories' : category}\n                  </option>\n                ))}\n              </select>\n            </div>\n            \n            <select\n              value={alertFilter}\n              onChange={(e) => setAlertFilter(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n            >\n              <option value=\"all\">All Alerts</option>\n              <option value=\"out_of_stock\">Out of Stock</option>\n              <option value=\"low_stock\">Low Stock</option>\n              <option value=\"expiring_soon\">Expiring Soon</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"space-y-3\">\n          {filteredAlerts.length > 0 ? (\n            filteredAlerts.map((alert) => (\n              <div\n                key={alert.id}\n                className={`border rounded-lg p-4 ${getAlertColor(alert.alertType)}`}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    {getAlertIcon(alert.alertType)}\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">{alert.productName}</p>\n                      <p className=\"text-xs text-gray-500\">{alert.category}</p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-medium text-gray-900\">\n                      Stock: {alert.currentStock}\n                    </p>\n                    {alert.alertType !== 'expiring_soon' && (\n                      <p className=\"text-xs text-gray-500\">\n                        Reorder at: {alert.reorderLevel}\n                      </p>\n                    )}\n                    {alert.expiryDate && (\n                      <p className=\"text-xs text-red-600\">\n                        Expires: {alert.expiryDate.toLocaleDateString()}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))\n          ) : (\n            <div className=\"text-center py-8\">\n              <Package className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No Alerts</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                All inventory levels are within normal ranges.\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InventoryAnalytics;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,OAAO,CACPC,aAAa,CACbC,YAAY,CACZC,UAAU,CACVC,SAAS,CACTC,SAAS,CACTC,MAAM,KACD,cAAc,CACrB,OAASC,WAAW,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAsBtD,KAAM,CAAAC,kBAA4B,CAAGA,CAAA,GAAM,CACzC,KAAM,CAAEC,QAAQ,CAAEC,OAAQ,CAAC,CAAGP,WAAW,CAAC,CAAC,CAC3C,KAAM,CAACQ,MAAM,CAAEC,SAAS,CAAC,CAAGlB,QAAQ,CAAmB,EAAE,CAAC,CAC1D,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAA0B,IAAI,CAAC,CACrE,KAAM,CAACqB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtB,QAAQ,CAAS,KAAK,CAAC,CACvE,KAAM,CAACuB,WAAW,CAAEC,cAAc,CAAC,CAAGxB,QAAQ,CAAS,KAAK,CAAC,CAE7DC,SAAS,CAAC,IAAM,CACd,GAAIc,QAAQ,CAACU,MAAM,CAAG,CAAC,CAAE,CACvBC,gBAAgB,CAAC,CAAC,CAClBC,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACZ,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAW,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAE,aAAa,CAAGb,QAAQ,CAACU,MAAM,CACrC,KAAM,CAAAI,aAAa,CAAGd,QAAQ,CAACe,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,aAAa,EAAID,CAAC,CAACE,YAAY,EAAIF,CAAC,CAACC,aAAa,CAAG,CAAC,CAAC,CAACP,MAAM,CAC3G,KAAM,CAAAS,eAAe,CAAGnB,QAAQ,CAACe,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,aAAa,GAAK,CAAC,CAAC,CAACP,MAAM,CAE1E;AACA,KAAM,CAAAU,iBAAiB,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACpC,KAAM,CAAAC,OAAO,CAAGF,iBAAiB,CAACG,OAAO,CAAC,CAAC,CAAG,EAAE,CAChDH,iBAAiB,CAACI,OAAO,CAACF,OAAO,CAAC,CAClC,KAAM,CAAAG,iBAAiB,CAAGzB,QAAQ,CAACe,MAAM,CAACC,CAAC,EACzCA,CAAC,CAACU,SAAS,EAAIV,CAAC,CAACW,UAAU,EAAIX,CAAC,CAACW,UAAU,EAAIP,iBACjD,CAAC,CAACV,MAAM,CAER,KAAM,CAAAkB,eAAe,CAAG5B,QAAQ,CAAC6B,MAAM,CAAC,CAACC,GAAG,CAAEd,CAAC,GAAKc,GAAG,CAAId,CAAC,CAACC,aAAa,CAAGD,CAAC,CAACe,KAAM,CAAE,CAAC,CAAC,CACzF,KAAM,CAAAC,iBAAiB,CAAGnB,aAAa,CAAG,CAAC,CACzCb,QAAQ,CAAC6B,MAAM,CAAC,CAACC,GAAG,CAAEd,CAAC,GAAKc,GAAG,CAAGd,CAAC,CAACC,aAAa,CAAE,CAAC,CAAC,CAAGJ,aAAa,CAAG,CAAC,CAE3ER,UAAU,CAAC,CACTQ,aAAa,CACbC,aAAa,CACbK,eAAe,CACfM,iBAAiB,CACjBG,eAAe,CACfI,iBACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAApB,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAqB,SAA2B,CAAG,EAAE,CACtC,KAAM,CAAAb,iBAAiB,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACpC,KAAM,CAAAC,OAAO,CAAGF,iBAAiB,CAACG,OAAO,CAAC,CAAC,CAAG,EAAE,CAChDH,iBAAiB,CAACI,OAAO,CAACF,OAAO,CAAC,CAElCtB,QAAQ,CAACkC,OAAO,CAACC,OAAO,EAAI,CAC1B;AACA,GAAIA,OAAO,CAAClB,aAAa,GAAK,CAAC,CAAE,CAC/BgB,SAAS,CAACG,IAAI,CAAC,CACbC,EAAE,IAAAC,MAAA,CAAKH,OAAO,CAACE,EAAE,iBAAe,CAChCE,WAAW,CAAEJ,OAAO,CAACK,IAAI,CACzBC,YAAY,CAAEN,OAAO,CAAClB,aAAa,CACnCC,YAAY,CAAEiB,OAAO,CAACjB,YAAY,CAClCwB,QAAQ,CAAEP,OAAO,CAACO,QAAQ,CAC1BC,SAAS,CAAE,cACb,CAAC,CAAC,CACJ,CACA;AAAA,IACK,IAAIR,OAAO,CAAClB,aAAa,EAAIkB,OAAO,CAACjB,YAAY,CAAE,CACtDe,SAAS,CAACG,IAAI,CAAC,CACbC,EAAE,IAAAC,MAAA,CAAKH,OAAO,CAACE,EAAE,cAAY,CAC7BE,WAAW,CAAEJ,OAAO,CAACK,IAAI,CACzBC,YAAY,CAAEN,OAAO,CAAClB,aAAa,CACnCC,YAAY,CAAEiB,OAAO,CAACjB,YAAY,CAClCwB,QAAQ,CAAEP,OAAO,CAACO,QAAQ,CAC1BC,SAAS,CAAE,WACb,CAAC,CAAC,CACJ,CAEA;AACA,GAAIR,OAAO,CAACT,SAAS,EAAIS,OAAO,CAACR,UAAU,EAAIQ,OAAO,CAACR,UAAU,EAAIP,iBAAiB,CAAE,CACtFa,SAAS,CAACG,IAAI,CAAC,CACbC,EAAE,IAAAC,MAAA,CAAKH,OAAO,CAACE,EAAE,aAAW,CAC5BE,WAAW,CAAEJ,OAAO,CAACK,IAAI,CACzBC,YAAY,CAAEN,OAAO,CAAClB,aAAa,CACnCC,YAAY,CAAEiB,OAAO,CAACjB,YAAY,CAClCwB,QAAQ,CAAEP,OAAO,CAACO,QAAQ,CAC1BC,SAAS,CAAE,eAAe,CAC1BhB,UAAU,CAAEQ,OAAO,CAACR,UACtB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFxB,SAAS,CAAC8B,SAAS,CAAC,CACtB,CAAC,CAED,KAAM,CAAAW,YAAY,CAAID,SAAiB,EAAK,CAC1C,OAAQA,SAAS,EACf,IAAK,cAAc,CACjB,mBAAO/C,IAAA,CAACR,aAAa,EAACyD,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAC3D,IAAK,WAAW,CACd,mBAAOjD,IAAA,CAACP,YAAY,EAACwD,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC7D,IAAK,eAAe,CAClB,mBAAOjD,IAAA,CAACR,aAAa,EAACyD,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC9D,QACE,mBAAOjD,IAAA,CAACT,OAAO,EAAC0D,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACxD,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAIH,SAAiB,EAAK,CAC3C,OAAQA,SAAS,EACf,IAAK,cAAc,CACjB,MAAO,0BAA0B,CACnC,IAAK,WAAW,CACd,MAAO,gCAAgC,CACzC,IAAK,eAAe,CAClB,MAAO,gCAAgC,CACzC,QACE,MAAO,4BAA4B,CACvC,CACF,CAAC,CAED,KAAM,CAAAI,UAAU,CAAG,CAAC,KAAK,CAAE,GAAGC,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAAClD,QAAQ,CAACmD,GAAG,CAACnC,CAAC,EAAIA,CAAC,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC,CACjF,KAAM,CAAAU,cAAc,CAAGlD,MAAM,CAACa,MAAM,CAACsC,KAAK,EAAI,CAC5C,KAAM,CAAAC,aAAa,CAAGhD,gBAAgB,GAAK,KAAK,EAAI+C,KAAK,CAACX,QAAQ,GAAKpC,gBAAgB,CACvF,KAAM,CAAAiD,UAAU,CAAG/C,WAAW,GAAK,KAAK,EAAI6C,KAAK,CAACV,SAAS,GAAKnC,WAAW,CAC3E,MAAO,CAAA8C,aAAa,EAAIC,UAAU,CACpC,CAAC,CAAC,CAEF,GAAItD,OAAO,CAAE,CACX,mBACEL,IAAA,QAAKiD,SAAS,CAAC,gCAAgC,CAAAW,QAAA,cAC7C1D,KAAA,QAAK+C,SAAS,CAAC,wCAAwC,CAAAW,QAAA,eACrD5D,IAAA,CAACJ,SAAS,EAACqD,SAAS,CAAC,4CAA4C,CAAE,CAAC,cACpEjD,IAAA,SAAMiD,SAAS,CAAC,uBAAuB,CAAAW,QAAA,CAAC,2BAAyB,CAAM,CAAC,EACrE,CAAC,CACH,CAAC,CAEV,CAEA,mBACE1D,KAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAW,QAAA,EAEvBpD,OAAO,eACNN,KAAA,QAAK+C,SAAS,CAAC,sDAAsD,CAAAW,QAAA,eACnE5D,IAAA,QAAKiD,SAAS,CAAC,gCAAgC,CAAAW,QAAA,cAC7C1D,KAAA,QAAK+C,SAAS,CAAC,mBAAmB,CAAAW,QAAA,eAChC5D,IAAA,QAAKiD,SAAS,CAAC,eAAe,CAAAW,QAAA,cAC5B5D,IAAA,CAACT,OAAO,EAAC0D,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC1C,CAAC,cACN/C,KAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAW,QAAA,eACnB5D,IAAA,MAAGiD,SAAS,CAAC,mCAAmC,CAAAW,QAAA,CAAC,gBAAc,CAAG,CAAC,cACnE5D,IAAA,MAAGiD,SAAS,CAAC,kCAAkC,CAAAW,QAAA,CAAEpD,OAAO,CAACS,aAAa,CAAI,CAAC,EACxE,CAAC,EACH,CAAC,CACH,CAAC,cAENjB,IAAA,QAAKiD,SAAS,CAAC,gCAAgC,CAAAW,QAAA,cAC7C1D,KAAA,QAAK+C,SAAS,CAAC,mBAAmB,CAAAW,QAAA,eAChC5D,IAAA,QAAKiD,SAAS,CAAC,eAAe,CAAAW,QAAA,cAC5B5D,IAAA,CAACR,aAAa,EAACyD,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAC/C,CAAC,cACN/C,KAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAW,QAAA,eACnB5D,IAAA,MAAGiD,SAAS,CAAC,mCAAmC,CAAAW,QAAA,CAAC,iBAAe,CAAG,CAAC,cACpE5D,IAAA,MAAGiD,SAAS,CAAC,kCAAkC,CAAAW,QAAA,CAC5CpD,OAAO,CAACe,eAAe,CAAGf,OAAO,CAACU,aAAa,CAC/C,CAAC,cACJhB,KAAA,MAAG+C,SAAS,CAAC,uBAAuB,CAAAW,QAAA,EACjCpD,OAAO,CAACe,eAAe,CAAC,iBAAe,CAACf,OAAO,CAACU,aAAa,CAAC,YACjE,EAAG,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cAENlB,IAAA,QAAKiD,SAAS,CAAC,gCAAgC,CAAAW,QAAA,cAC7C1D,KAAA,QAAK+C,SAAS,CAAC,mBAAmB,CAAAW,QAAA,eAChC5D,IAAA,QAAKiD,SAAS,CAAC,eAAe,CAAAW,QAAA,cAC5B5D,IAAA,CAACN,UAAU,EAACuD,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC9C,CAAC,cACN/C,KAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAW,QAAA,eACnB5D,IAAA,MAAGiD,SAAS,CAAC,mCAAmC,CAAAW,QAAA,CAAC,aAAW,CAAG,CAAC,cAChE1D,KAAA,MAAG+C,SAAS,CAAC,kCAAkC,CAAAW,QAAA,EAAC,MAC1C,CAACpD,OAAO,CAACwB,eAAe,CAAC6B,cAAc,CAAC,CAAC,EAC5C,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cAEN7D,IAAA,QAAKiD,SAAS,CAAC,gCAAgC,CAAAW,QAAA,cAC7C1D,KAAA,QAAK+C,SAAS,CAAC,mBAAmB,CAAAW,QAAA,eAChC5D,IAAA,QAAKiD,SAAS,CAAC,eAAe,CAAAW,QAAA,cAC5B5D,IAAA,CAACL,SAAS,EAACsD,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC9C,CAAC,cACN/C,KAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAW,QAAA,eACnB5D,IAAA,MAAGiD,SAAS,CAAC,mCAAmC,CAAAW,QAAA,CAAC,kBAAgB,CAAG,CAAC,cACrE5D,IAAA,MAAGiD,SAAS,CAAC,kCAAkC,CAAAW,QAAA,CAC5CpD,OAAO,CAAC4B,iBAAiB,CAAC0B,OAAO,CAAC,CAAC,CAAC,CACpC,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CACN,cAGD5D,KAAA,QAAK+C,SAAS,CAAC,gCAAgC,CAAAW,QAAA,eAC7C1D,KAAA,QAAK+C,SAAS,CAAC,wCAAwC,CAAAW,QAAA,eACrD1D,KAAA,OAAI+C,SAAS,CAAC,qDAAqD,CAAAW,QAAA,eACjE5D,IAAA,CAACR,aAAa,EAACyD,SAAS,CAAC,8BAA8B,CAAE,CAAC,qBACxC,CAACO,cAAc,CAAC1C,MAAM,CAAC,GAC3C,EAAI,CAAC,cAELZ,KAAA,QAAK+C,SAAS,CAAC,6BAA6B,CAAAW,QAAA,eAC1C1D,KAAA,QAAK+C,SAAS,CAAC,6BAA6B,CAAAW,QAAA,eAC1C5D,IAAA,CAACH,MAAM,EAACoD,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC5CjD,IAAA,WACE+D,KAAK,CAAErD,gBAAiB,CACxBsD,QAAQ,CAAGC,CAAC,EAAKtD,mBAAmB,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDd,SAAS,CAAC,qDAAqD,CAAAW,QAAA,CAE9DT,UAAU,CAACI,GAAG,CAACT,QAAQ,eACtB9C,IAAA,WAAuB+D,KAAK,CAAEjB,QAAS,CAAAc,QAAA,CACpCd,QAAQ,GAAK,KAAK,CAAG,gBAAgB,CAAGA,QAAQ,EADtCA,QAEL,CACT,CAAC,CACI,CAAC,EACN,CAAC,cAEN5C,KAAA,WACE6D,KAAK,CAAEnD,WAAY,CACnBoD,QAAQ,CAAGC,CAAC,EAAKpD,cAAc,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChDd,SAAS,CAAC,qDAAqD,CAAAW,QAAA,eAE/D5D,IAAA,WAAQ+D,KAAK,CAAC,KAAK,CAAAH,QAAA,CAAC,YAAU,CAAQ,CAAC,cACvC5D,IAAA,WAAQ+D,KAAK,CAAC,cAAc,CAAAH,QAAA,CAAC,cAAY,CAAQ,CAAC,cAClD5D,IAAA,WAAQ+D,KAAK,CAAC,WAAW,CAAAH,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5C5D,IAAA,WAAQ+D,KAAK,CAAC,eAAe,CAAAH,QAAA,CAAC,eAAa,CAAQ,CAAC,EAC9C,CAAC,EACN,CAAC,EACH,CAAC,cAEN5D,IAAA,QAAKiD,SAAS,CAAC,WAAW,CAAAW,QAAA,CACvBJ,cAAc,CAAC1C,MAAM,CAAG,CAAC,CACxB0C,cAAc,CAACD,GAAG,CAAEE,KAAK,eACvBzD,IAAA,QAEEiD,SAAS,0BAAAP,MAAA,CAA2BQ,aAAa,CAACO,KAAK,CAACV,SAAS,CAAC,CAAG,CAAAa,QAAA,cAErE1D,KAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAW,QAAA,eAChD1D,KAAA,QAAK+C,SAAS,CAAC,mBAAmB,CAAAW,QAAA,EAC/BZ,YAAY,CAACS,KAAK,CAACV,SAAS,CAAC,cAC9B7C,KAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAW,QAAA,eACnB5D,IAAA,MAAGiD,SAAS,CAAC,mCAAmC,CAAAW,QAAA,CAAEH,KAAK,CAACd,WAAW,CAAI,CAAC,cACxE3C,IAAA,MAAGiD,SAAS,CAAC,uBAAuB,CAAAW,QAAA,CAAEH,KAAK,CAACX,QAAQ,CAAI,CAAC,EACtD,CAAC,EACH,CAAC,cACN5C,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAW,QAAA,eACzB1D,KAAA,MAAG+C,SAAS,CAAC,mCAAmC,CAAAW,QAAA,EAAC,SACxC,CAACH,KAAK,CAACZ,YAAY,EACzB,CAAC,CACHY,KAAK,CAACV,SAAS,GAAK,eAAe,eAClC7C,KAAA,MAAG+C,SAAS,CAAC,uBAAuB,CAAAW,QAAA,EAAC,cACvB,CAACH,KAAK,CAACnC,YAAY,EAC9B,CACJ,CACAmC,KAAK,CAAC1B,UAAU,eACf7B,KAAA,MAAG+C,SAAS,CAAC,sBAAsB,CAAAW,QAAA,EAAC,WACzB,CAACH,KAAK,CAAC1B,UAAU,CAACoC,kBAAkB,CAAC,CAAC,EAC9C,CACJ,EACE,CAAC,EACH,CAAC,EA1BDV,KAAK,CAAChB,EA2BR,CACN,CAAC,cAEFvC,KAAA,QAAK+C,SAAS,CAAC,kBAAkB,CAAAW,QAAA,eAC/B5D,IAAA,CAACT,OAAO,EAAC0D,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACvDjD,IAAA,OAAIiD,SAAS,CAAC,wCAAwC,CAAAW,QAAA,CAAC,WAAS,CAAI,CAAC,cACrE5D,IAAA,MAAGiD,SAAS,CAAC,4BAA4B,CAAAW,QAAA,CAAC,gDAE1C,CAAG,CAAC,EACD,CACN,CACE,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}