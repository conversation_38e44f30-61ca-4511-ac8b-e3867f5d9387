{"ast": null, "code": "'use strict';\n\nvar $ = require('../internals/export');\nvar $trim = require('../internals/string-trim').trim;\nvar forcedStringTrimMethod = require('../internals/string-trim-forced');\n\n// `String.prototype.trim` method\n// https://tc39.es/ecma262/#sec-string.prototype.trim\n$({\n  target: 'String',\n  proto: true,\n  forced: forcedStringTrimMethod('trim')\n}, {\n  trim: function trim() {\n    return $trim(this);\n  }\n});", "map": {"version": 3, "names": ["$", "require", "$trim", "trim", "forcedStringTrimMethod", "target", "proto", "forced"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/modules/es.string.trim.js"], "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar $trim = require('../internals/string-trim').trim;\nvar forcedStringTrimMethod = require('../internals/string-trim-forced');\n\n// `String.prototype.trim` method\n// https://tc39.es/ecma262/#sec-string.prototype.trim\n$({ target: 'String', proto: true, forced: forcedStringTrimMethod('trim') }, {\n  trim: function trim() {\n    return $trim(this);\n  }\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AACtC,IAAIC,KAAK,GAAGD,OAAO,CAAC,0BAA0B,CAAC,CAACE,IAAI;AACpD,IAAIC,sBAAsB,GAAGH,OAAO,CAAC,iCAAiC,CAAC;;AAEvE;AACA;AACAD,CAAC,CAAC;EAAEK,MAAM,EAAE,QAAQ;EAAEC,KAAK,EAAE,IAAI;EAAEC,MAAM,EAAEH,sBAAsB,CAAC,MAAM;AAAE,CAAC,EAAE;EAC3ED,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;IACpB,OAAOD,KAAK,CAAC,IAAI,CAAC;EACpB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}