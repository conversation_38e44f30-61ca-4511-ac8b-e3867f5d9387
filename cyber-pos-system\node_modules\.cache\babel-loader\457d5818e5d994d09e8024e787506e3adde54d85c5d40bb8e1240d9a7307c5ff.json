{"ast": null, "code": "'use strict';\n\nvar hasOwn = require('../internals/has-own-property');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\nmodule.exports = function (target, source, exceptions) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!hasOwn(target, key) && !(exceptions && hasOwn(exceptions, key))) {\n      defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n    }\n  }\n};", "map": {"version": 3, "names": ["hasOwn", "require", "ownKeys", "getOwnPropertyDescriptorModule", "definePropertyModule", "module", "exports", "target", "source", "exceptions", "keys", "defineProperty", "f", "getOwnPropertyDescriptor", "i", "length", "key"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/copy-constructor-properties.js"], "sourcesContent": ["'use strict';\nvar hasOwn = require('../internals/has-own-property');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source, exceptions) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!hasOwn(target, key) && !(exceptions && hasOwn(exceptions, key))) {\n      defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n    }\n  }\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AACrD,IAAIC,OAAO,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAC9C,IAAIE,8BAA8B,GAAGF,OAAO,CAAC,iDAAiD,CAAC;AAC/F,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,qCAAqC,CAAC;AAEzEI,MAAM,CAACC,OAAO,GAAG,UAAUC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;EACrD,IAAIC,IAAI,GAAGR,OAAO,CAACM,MAAM,CAAC;EAC1B,IAAIG,cAAc,GAAGP,oBAAoB,CAACQ,CAAC;EAC3C,IAAIC,wBAAwB,GAAGV,8BAA8B,CAACS,CAAC;EAC/D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIE,GAAG,GAAGN,IAAI,CAACI,CAAC,CAAC;IACjB,IAAI,CAACd,MAAM,CAACO,MAAM,EAAES,GAAG,CAAC,IAAI,EAAEP,UAAU,IAAIT,MAAM,CAACS,UAAU,EAAEO,GAAG,CAAC,CAAC,EAAE;MACpEL,cAAc,CAACJ,MAAM,EAAES,GAAG,EAAEH,wBAAwB,CAACL,MAAM,EAAEQ,GAAG,CAAC,CAAC;IACpE;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}