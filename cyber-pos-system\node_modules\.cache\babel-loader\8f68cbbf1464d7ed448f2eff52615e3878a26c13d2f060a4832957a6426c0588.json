{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar DOMIterables = require('../internals/dom-iterables');\nvar DOMTokenListPrototype = require('../internals/dom-token-list-prototype');\nvar ArrayIteratorMethods = require('../modules/es.array.iterator');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar ITERATOR = wellKnownSymbol('iterator');\nvar ArrayValues = ArrayIteratorMethods.values;\nvar handlePrototype = function (CollectionPrototype, COLLECTION_NAME) {\n  if (CollectionPrototype) {\n    // some Chrome versions have non-configurable methods on DOMTokenList\n    if (CollectionPrototype[ITERATOR] !== ArrayValues) try {\n      createNonEnumerableProperty(CollectionPrototype, ITERATOR, ArrayValues);\n    } catch (error) {\n      CollectionPrototype[ITERATOR] = ArrayValues;\n    }\n    setToStringTag(CollectionPrototype, COLLECTION_NAME, true);\n    if (DOMIterables[COLLECTION_NAME]) for (var METHOD_NAME in ArrayIteratorMethods) {\n      // some Chrome versions have non-configurable methods on DOMTokenList\n      if (CollectionPrototype[METHOD_NAME] !== ArrayIteratorMethods[METHOD_NAME]) try {\n        createNonEnumerableProperty(CollectionPrototype, METHOD_NAME, ArrayIteratorMethods[METHOD_NAME]);\n      } catch (error) {\n        CollectionPrototype[METHOD_NAME] = ArrayIteratorMethods[METHOD_NAME];\n      }\n    }\n  }\n};\nfor (var COLLECTION_NAME in DOMIterables) {\n  handlePrototype(globalThis[COLLECTION_NAME] && globalThis[COLLECTION_NAME].prototype, COLLECTION_NAME);\n}\nhandlePrototype(DOMTokenListPrototype, 'DOMTokenList');", "map": {"version": 3, "names": ["globalThis", "require", "DOMIterables", "DOMTokenListPrototype", "ArrayIteratorMethods", "createNonEnumerableProperty", "setToStringTag", "wellKnownSymbol", "ITERATOR", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "values", "handlePrototype", "CollectionPrototype", "COLLECTION_NAME", "error", "METHOD_NAME", "prototype"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/modules/web.dom-collections.iterator.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar DOMIterables = require('../internals/dom-iterables');\nvar DOMTokenListPrototype = require('../internals/dom-token-list-prototype');\nvar ArrayIteratorMethods = require('../modules/es.array.iterator');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar ArrayValues = ArrayIteratorMethods.values;\n\nvar handlePrototype = function (CollectionPrototype, COLLECTION_NAME) {\n  if (CollectionPrototype) {\n    // some Chrome versions have non-configurable methods on DOMTokenList\n    if (CollectionPrototype[ITERATOR] !== ArrayValues) try {\n      createNonEnumerableProperty(CollectionPrototype, ITERATOR, ArrayValues);\n    } catch (error) {\n      CollectionPrototype[ITERATOR] = ArrayValues;\n    }\n    setToStringTag(CollectionPrototype, COLLECTION_NAME, true);\n    if (DOMIterables[COLLECTION_NAME]) for (var METHOD_NAME in ArrayIteratorMethods) {\n      // some Chrome versions have non-configurable methods on DOMTokenList\n      if (CollectionPrototype[METHOD_NAME] !== ArrayIteratorMethods[METHOD_NAME]) try {\n        createNonEnumerableProperty(CollectionPrototype, METHOD_NAME, ArrayIteratorMethods[METHOD_NAME]);\n      } catch (error) {\n        CollectionPrototype[METHOD_NAME] = ArrayIteratorMethods[METHOD_NAME];\n      }\n    }\n  }\n};\n\nfor (var COLLECTION_NAME in DOMIterables) {\n  handlePrototype(globalThis[COLLECTION_NAME] && globalThis[COLLECTION_NAME].prototype, COLLECTION_NAME);\n}\n\nhandlePrototype(DOMTokenListPrototype, 'DOMTokenList');\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,YAAY,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AACxD,IAAIE,qBAAqB,GAAGF,OAAO,CAAC,uCAAuC,CAAC;AAC5E,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,8BAA8B,CAAC;AAClE,IAAII,2BAA2B,GAAGJ,OAAO,CAAC,6CAA6C,CAAC;AACxF,IAAIK,cAAc,GAAGL,OAAO,CAAC,gCAAgC,CAAC;AAC9D,IAAIM,eAAe,GAAGN,OAAO,CAAC,gCAAgC,CAAC;AAE/D,IAAIO,QAAQ,GAAGD,eAAe,CAAC,UAAU,CAAC;AAC1C,IAAIE,WAAW,GAAGL,oBAAoB,CAACM,MAAM;AAE7C,IAAIC,eAAe,GAAG,SAAAA,CAAUC,mBAAmB,EAAEC,eAAe,EAAE;EACpE,IAAID,mBAAmB,EAAE;IACvB;IACA,IAAIA,mBAAmB,CAACJ,QAAQ,CAAC,KAAKC,WAAW,EAAE,IAAI;MACrDJ,2BAA2B,CAACO,mBAAmB,EAAEJ,QAAQ,EAAEC,WAAW,CAAC;IACzE,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,mBAAmB,CAACJ,QAAQ,CAAC,GAAGC,WAAW;IAC7C;IACAH,cAAc,CAACM,mBAAmB,EAAEC,eAAe,EAAE,IAAI,CAAC;IAC1D,IAAIX,YAAY,CAACW,eAAe,CAAC,EAAE,KAAK,IAAIE,WAAW,IAAIX,oBAAoB,EAAE;MAC/E;MACA,IAAIQ,mBAAmB,CAACG,WAAW,CAAC,KAAKX,oBAAoB,CAACW,WAAW,CAAC,EAAE,IAAI;QAC9EV,2BAA2B,CAACO,mBAAmB,EAAEG,WAAW,EAAEX,oBAAoB,CAACW,WAAW,CAAC,CAAC;MAClG,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdF,mBAAmB,CAACG,WAAW,CAAC,GAAGX,oBAAoB,CAACW,WAAW,CAAC;MACtE;IACF;EACF;AACF,CAAC;AAED,KAAK,IAAIF,eAAe,IAAIX,YAAY,EAAE;EACxCS,eAAe,CAACX,UAAU,CAACa,eAAe,CAAC,IAAIb,UAAU,CAACa,eAAe,CAAC,CAACG,SAAS,EAAEH,eAAe,CAAC;AACxG;AAEAF,eAAe,CAACR,qBAAqB,EAAE,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}