{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar CONFIGURABLE_FUNCTION_NAME = require('../internals/function-name').CONFIGURABLE;\nvar inspectSource = require('../internals/inspect-source');\nvar InternalStateModule = require('../internals/internal-state');\nvar enforceInternalState = InternalStateModule.enforce;\nvar getInternalState = InternalStateModule.get;\nvar $String = String;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nvar stringSlice = uncurryThis(''.slice);\nvar replace = uncurryThis(''.replace);\nvar join = uncurryThis([].join);\nvar CONFIGURABLE_LENGTH = DESCRIPTORS && !fails(function () {\n  return defineProperty(function () {/* empty */}, 'length', {\n    value: 8\n  }).length !== 8;\n});\nvar TEMPLATE = String(String).split('String');\nvar makeBuiltIn = module.exports = function (value, name, options) {\n  if (stringSlice($String(name), 0, 7) === 'Symbol(') {\n    name = '[' + replace($String(name), /^Symbol\\(([^)]*)\\).*$/, '$1') + ']';\n  }\n  if (options && options.getter) name = 'get ' + name;\n  if (options && options.setter) name = 'set ' + name;\n  if (!hasOwn(value, 'name') || CONFIGURABLE_FUNCTION_NAME && value.name !== name) {\n    if (DESCRIPTORS) defineProperty(value, 'name', {\n      value: name,\n      configurable: true\n    });else value.name = name;\n  }\n  if (CONFIGURABLE_LENGTH && options && hasOwn(options, 'arity') && value.length !== options.arity) {\n    defineProperty(value, 'length', {\n      value: options.arity\n    });\n  }\n  try {\n    if (options && hasOwn(options, 'constructor') && options.constructor) {\n      if (DESCRIPTORS) defineProperty(value, 'prototype', {\n        writable: false\n      });\n      // in V8 ~ Chrome 53, prototypes of some methods, like `Array.prototype.values`, are non-writable\n    } else if (value.prototype) value.prototype = undefined;\n  } catch (error) {/* empty */}\n  var state = enforceInternalState(value);\n  if (!hasOwn(state, 'source')) {\n    state.source = join(TEMPLATE, typeof name == 'string' ? name : '');\n  }\n  return value;\n};\n\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n// eslint-disable-next-line no-extend-native -- required\nFunction.prototype.toString = makeBuiltIn(function toString() {\n  return isCallable(this) && getInternalState(this).source || inspectSource(this);\n}, 'toString');", "map": {"version": 3, "names": ["uncurryThis", "require", "fails", "isCallable", "hasOwn", "DESCRIPTORS", "CONFIGURABLE_FUNCTION_NAME", "CONFIGURABLE", "inspectSource", "InternalStateModule", "enforceInternalState", "enforce", "getInternalState", "get", "$String", "String", "defineProperty", "Object", "stringSlice", "slice", "replace", "join", "CONFIGURABLE_LENGTH", "value", "length", "TEMPLATE", "split", "makeBuiltIn", "module", "exports", "name", "options", "getter", "setter", "configurable", "arity", "constructor", "writable", "prototype", "undefined", "error", "state", "source", "Function", "toString"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/make-built-in.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar CONFIGURABLE_FUNCTION_NAME = require('../internals/function-name').CONFIGURABLE;\nvar inspectSource = require('../internals/inspect-source');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar enforceInternalState = InternalStateModule.enforce;\nvar getInternalState = InternalStateModule.get;\nvar $String = String;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nvar stringSlice = uncurryThis(''.slice);\nvar replace = uncurryThis(''.replace);\nvar join = uncurryThis([].join);\n\nvar CONFIGURABLE_LENGTH = DESCRIPTORS && !fails(function () {\n  return defineProperty(function () { /* empty */ }, 'length', { value: 8 }).length !== 8;\n});\n\nvar TEMPLATE = String(String).split('String');\n\nvar makeBuiltIn = module.exports = function (value, name, options) {\n  if (stringSlice($String(name), 0, 7) === 'Symbol(') {\n    name = '[' + replace($String(name), /^Symbol\\(([^)]*)\\).*$/, '$1') + ']';\n  }\n  if (options && options.getter) name = 'get ' + name;\n  if (options && options.setter) name = 'set ' + name;\n  if (!hasOwn(value, 'name') || (CONFIGURABLE_FUNCTION_NAME && value.name !== name)) {\n    if (DESCRIPTORS) defineProperty(value, 'name', { value: name, configurable: true });\n    else value.name = name;\n  }\n  if (CONFIGURABLE_LENGTH && options && hasOwn(options, 'arity') && value.length !== options.arity) {\n    defineProperty(value, 'length', { value: options.arity });\n  }\n  try {\n    if (options && hasOwn(options, 'constructor') && options.constructor) {\n      if (DESCRIPTORS) defineProperty(value, 'prototype', { writable: false });\n    // in V8 ~ Chrome 53, prototypes of some methods, like `Array.prototype.values`, are non-writable\n    } else if (value.prototype) value.prototype = undefined;\n  } catch (error) { /* empty */ }\n  var state = enforceInternalState(value);\n  if (!hasOwn(state, 'source')) {\n    state.source = join(TEMPLATE, typeof name == 'string' ? name : '');\n  } return value;\n};\n\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n// eslint-disable-next-line no-extend-native -- required\nFunction.prototype.toString = makeBuiltIn(function toString() {\n  return isCallable(this) && getInternalState(this).source || inspectSource(this);\n}, 'toString');\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIG,MAAM,GAAGH,OAAO,CAAC,+BAA+B,CAAC;AACrD,IAAII,WAAW,GAAGJ,OAAO,CAAC,0BAA0B,CAAC;AACrD,IAAIK,0BAA0B,GAAGL,OAAO,CAAC,4BAA4B,CAAC,CAACM,YAAY;AACnF,IAAIC,aAAa,GAAGP,OAAO,CAAC,6BAA6B,CAAC;AAC1D,IAAIQ,mBAAmB,GAAGR,OAAO,CAAC,6BAA6B,CAAC;AAEhE,IAAIS,oBAAoB,GAAGD,mBAAmB,CAACE,OAAO;AACtD,IAAIC,gBAAgB,GAAGH,mBAAmB,CAACI,GAAG;AAC9C,IAAIC,OAAO,GAAGC,MAAM;AACpB;AACA,IAAIC,cAAc,GAAGC,MAAM,CAACD,cAAc;AAC1C,IAAIE,WAAW,GAAGlB,WAAW,CAAC,EAAE,CAACmB,KAAK,CAAC;AACvC,IAAIC,OAAO,GAAGpB,WAAW,CAAC,EAAE,CAACoB,OAAO,CAAC;AACrC,IAAIC,IAAI,GAAGrB,WAAW,CAAC,EAAE,CAACqB,IAAI,CAAC;AAE/B,IAAIC,mBAAmB,GAAGjB,WAAW,IAAI,CAACH,KAAK,CAAC,YAAY;EAC1D,OAAOc,cAAc,CAAC,YAAY,CAAE,YAAa,EAAE,QAAQ,EAAE;IAAEO,KAAK,EAAE;EAAE,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC;AACzF,CAAC,CAAC;AAEF,IAAIC,QAAQ,GAAGV,MAAM,CAACA,MAAM,CAAC,CAACW,KAAK,CAAC,QAAQ,CAAC;AAE7C,IAAIC,WAAW,GAAGC,MAAM,CAACC,OAAO,GAAG,UAAUN,KAAK,EAAEO,IAAI,EAAEC,OAAO,EAAE;EACjE,IAAIb,WAAW,CAACJ,OAAO,CAACgB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,EAAE;IAClDA,IAAI,GAAG,GAAG,GAAGV,OAAO,CAACN,OAAO,CAACgB,IAAI,CAAC,EAAE,uBAAuB,EAAE,IAAI,CAAC,GAAG,GAAG;EAC1E;EACA,IAAIC,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAEF,IAAI,GAAG,MAAM,GAAGA,IAAI;EACnD,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAEH,IAAI,GAAG,MAAM,GAAGA,IAAI;EACnD,IAAI,CAAC1B,MAAM,CAACmB,KAAK,EAAE,MAAM,CAAC,IAAKjB,0BAA0B,IAAIiB,KAAK,CAACO,IAAI,KAAKA,IAAK,EAAE;IACjF,IAAIzB,WAAW,EAAEW,cAAc,CAACO,KAAK,EAAE,MAAM,EAAE;MAAEA,KAAK,EAAEO,IAAI;MAAEI,YAAY,EAAE;IAAK,CAAC,CAAC,CAAC,KAC/EX,KAAK,CAACO,IAAI,GAAGA,IAAI;EACxB;EACA,IAAIR,mBAAmB,IAAIS,OAAO,IAAI3B,MAAM,CAAC2B,OAAO,EAAE,OAAO,CAAC,IAAIR,KAAK,CAACC,MAAM,KAAKO,OAAO,CAACI,KAAK,EAAE;IAChGnB,cAAc,CAACO,KAAK,EAAE,QAAQ,EAAE;MAAEA,KAAK,EAAEQ,OAAO,CAACI;IAAM,CAAC,CAAC;EAC3D;EACA,IAAI;IACF,IAAIJ,OAAO,IAAI3B,MAAM,CAAC2B,OAAO,EAAE,aAAa,CAAC,IAAIA,OAAO,CAACK,WAAW,EAAE;MACpE,IAAI/B,WAAW,EAAEW,cAAc,CAACO,KAAK,EAAE,WAAW,EAAE;QAAEc,QAAQ,EAAE;MAAM,CAAC,CAAC;MAC1E;IACA,CAAC,MAAM,IAAId,KAAK,CAACe,SAAS,EAAEf,KAAK,CAACe,SAAS,GAAGC,SAAS;EACzD,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAE;EAClB,IAAIC,KAAK,GAAG/B,oBAAoB,CAACa,KAAK,CAAC;EACvC,IAAI,CAACnB,MAAM,CAACqC,KAAK,EAAE,QAAQ,CAAC,EAAE;IAC5BA,KAAK,CAACC,MAAM,GAAGrB,IAAI,CAACI,QAAQ,EAAE,OAAOK,IAAI,IAAI,QAAQ,GAAGA,IAAI,GAAG,EAAE,CAAC;EACpE;EAAE,OAAOP,KAAK;AAChB,CAAC;;AAED;AACA;AACAoB,QAAQ,CAACL,SAAS,CAACM,QAAQ,GAAGjB,WAAW,CAAC,SAASiB,QAAQA,CAAA,EAAG;EAC5D,OAAOzC,UAAU,CAAC,IAAI,CAAC,IAAIS,gBAAgB,CAAC,IAAI,CAAC,CAAC8B,MAAM,IAAIlC,aAAa,CAAC,IAAI,CAAC;AACjF,CAAC,EAAE,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}