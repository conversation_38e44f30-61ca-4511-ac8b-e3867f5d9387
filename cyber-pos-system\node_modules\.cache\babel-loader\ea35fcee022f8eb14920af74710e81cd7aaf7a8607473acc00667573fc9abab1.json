{"ast": null, "code": "'use strict';\n\nvar PROPER_FUNCTION_NAME = require('../internals/function-name').PROPER;\nvar fails = require('../internals/fails');\nvar whitespaces = require('../internals/whitespaces');\nvar non = '\\u200B\\u0085\\u180E';\n\n// check that a method works with the correct list\n// of whitespaces and has a correct name\nmodule.exports = function (METHOD_NAME) {\n  return fails(function () {\n    return !!whitespaces[METHOD_NAME]() || non[METHOD_NAME]() !== non || PROPER_FUNCTION_NAME && whitespaces[METHOD_NAME].name !== METHOD_NAME;\n  });\n};", "map": {"version": 3, "names": ["PROPER_FUNCTION_NAME", "require", "PROPER", "fails", "whitespaces", "non", "module", "exports", "METHOD_NAME", "name"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/string-trim-forced.js"], "sourcesContent": ["'use strict';\nvar PROPER_FUNCTION_NAME = require('../internals/function-name').PROPER;\nvar fails = require('../internals/fails');\nvar whitespaces = require('../internals/whitespaces');\n\nvar non = '\\u200B\\u0085\\u180E';\n\n// check that a method works with the correct list\n// of whitespaces and has a correct name\nmodule.exports = function (METHOD_NAME) {\n  return fails(function () {\n    return !!whitespaces[METHOD_NAME]()\n      || non[METHOD_NAME]() !== non\n      || (PROPER_FUNCTION_NAME && whitespaces[METHOD_NAME].name !== METHOD_NAME);\n  });\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,oBAAoB,GAAGC,OAAO,CAAC,4BAA4B,CAAC,CAACC,MAAM;AACvE,IAAIC,KAAK,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIG,WAAW,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AAErD,IAAII,GAAG,GAAG,oBAAoB;;AAE9B;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,WAAW,EAAE;EACtC,OAAOL,KAAK,CAAC,YAAY;IACvB,OAAO,CAAC,CAACC,WAAW,CAACI,WAAW,CAAC,CAAC,CAAC,IAC9BH,GAAG,CAACG,WAAW,CAAC,CAAC,CAAC,KAAKH,GAAG,IACzBL,oBAAoB,IAAII,WAAW,CAACI,WAAW,CAAC,CAACC,IAAI,KAAKD,WAAY;EAC9E,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}