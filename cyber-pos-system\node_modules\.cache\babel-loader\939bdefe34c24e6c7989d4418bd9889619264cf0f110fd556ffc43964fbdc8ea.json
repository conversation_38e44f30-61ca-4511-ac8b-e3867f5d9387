{"ast": null, "code": "'use strict';\n\n/* eslint-disable no-proto -- safe */\nvar uncurryThisAccessor = require('../internals/function-uncurry-this-accessor');\nvar isObject = require('../internals/is-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = uncurryThisAccessor(Object.prototype, '__proto__', 'set');\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) {/* empty */}\n  return function setPrototypeOf(O, proto) {\n    requireObjectCoercible(O);\n    aPossiblePrototype(proto);\n    if (!isObject(O)) return O;\n    if (CORRECT_SETTER) setter(O, proto);else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);", "map": {"version": 3, "names": ["uncurry<PERSON><PERSON><PERSON><PERSON><PERSON>or", "require", "isObject", "requireObjectCoercible", "aPossiblePrototype", "module", "exports", "Object", "setPrototypeOf", "CORRECT_SETTER", "test", "setter", "prototype", "Array", "error", "O", "proto", "__proto__", "undefined"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/object-set-prototype-of.js"], "sourcesContent": ["'use strict';\n/* eslint-disable no-proto -- safe */\nvar uncurryThisAccessor = require('../internals/function-uncurry-this-accessor');\nvar isObject = require('../internals/is-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = uncurryThisAccessor(Object.prototype, '__proto__', 'set');\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    requireObjectCoercible(O);\n    aPossiblePrototype(proto);\n    if (!isObject(O)) return O;\n    if (CORRECT_SETTER) setter(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n"], "mappings": "AAAA,YAAY;;AACZ;AACA,IAAIA,mBAAmB,GAAGC,OAAO,CAAC,6CAA6C,CAAC;AAChF,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIE,sBAAsB,GAAGF,OAAO,CAAC,uCAAuC,CAAC;AAC7E,IAAIG,kBAAkB,GAAGH,OAAO,CAAC,mCAAmC,CAAC;;AAErE;AACA;AACA;AACA;AACAI,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,cAAc,KAAK,WAAW,IAAI,CAAC,CAAC,GAAG,YAAY;EACzE,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,IAAI,GAAG,CAAC,CAAC;EACb,IAAIC,MAAM;EACV,IAAI;IACFA,MAAM,GAAGX,mBAAmB,CAACO,MAAM,CAACK,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC;IAClED,MAAM,CAACD,IAAI,EAAE,EAAE,CAAC;IAChBD,cAAc,GAAGC,IAAI,YAAYG,KAAK;EACxC,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAE;EAClB,OAAO,SAASN,cAAcA,CAACO,CAAC,EAAEC,KAAK,EAAE;IACvCb,sBAAsB,CAACY,CAAC,CAAC;IACzBX,kBAAkB,CAACY,KAAK,CAAC;IACzB,IAAI,CAACd,QAAQ,CAACa,CAAC,CAAC,EAAE,OAAOA,CAAC;IAC1B,IAAIN,cAAc,EAAEE,MAAM,CAACI,CAAC,EAAEC,KAAK,CAAC,CAAC,KAChCD,CAAC,CAACE,SAAS,GAAGD,KAAK;IACxB,OAAOD,CAAC;EACV,CAAC;AACH,CAAC,CAAC,CAAC,GAAGG,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}