import React, { useState } from 'react';
import { Settings as SettingsIcon, Save, Users, Database } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import UserManagement from '../users/UserManagement';
import { initializeDemoData } from '../../utils/seedData';
import { seedTestData } from '../../utils/testDataSeeder';

const Settings: React.FC = () => {
  const { hasPermission, currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const tabs = [
    { id: 'general', name: 'General', icon: SettingsIcon },
    ...(hasPermission('admin') ? [{ id: 'users', name: 'User Management', icon: Users }] : []),
    { id: 'data', name: 'Data Management', icon: Database },
  ];

  const handleInitializeDemo = async () => {
    setLoading(true);
    setMessage('');

    try {
      await initializeDemoData();
      setMessage('Demo data initialized successfully!');
    } catch (error: any) {
      setMessage(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSeedTestData = async () => {
    if (!currentUser) {
      setMessage('Error: User not authenticated');
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      await seedTestData(currentUser.id);
      setMessage('Test data with transactions seeded successfully! You can now view reports with sample data.');
    } catch (error: any) {
      setMessage(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <SettingsIcon className="h-6 w-6 text-primary-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">General Settings</h3>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Business Name</label>
                    <input
                      type="text"
                      defaultValue="Cyber Services & Stationery"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Currency</label>
                    <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                      <option value="KSh">KSh (Kenyan Shilling)</option>
                      <option value="USD">USD (US Dollar)</option>
                      <option value="EUR">EUR (Euro)</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tax Rate (%)</label>
                    <input
                      type="number"
                      defaultValue="16"
                      step="0.01"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Receipt Footer</label>
                    <textarea
                      defaultValue="Thank you for your business!"
                      rows={3}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center">
                    <Save className="h-4 w-4 mr-2" />
                    Save General Settings
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'users' && hasPermission('admin') && (
            <UserManagement />
          )}

          {activeTab === 'data' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Data Management</h3>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Database className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Demo Data Initialization
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          Initialize the system with demo services and products for testing purposes.
                          This will add sample cyber services and stationery items to your database.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {message && (
                  <div className={`p-4 rounded-md mb-4 ${
                    message.includes('Error')
                      ? 'bg-red-50 text-red-700 border border-red-200'
                      : 'bg-green-50 text-green-700 border border-green-200'
                  }`}>
                    {message}
                  </div>
                )}

                <div className="space-y-4">
                  <button
                    onClick={handleInitializeDemo}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 mr-4"
                  >
                    <Database className="h-4 w-4 mr-2" />
                    {loading ? 'Initializing...' : 'Initialize Demo Data'}
                  </button>

                  <button
                    onClick={handleSeedTestData}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                  >
                    <Database className="h-4 w-4 mr-2" />
                    {loading ? 'Seeding...' : 'Seed Test Data with Transactions'}
                  </button>

                  <div className="text-sm text-gray-600 mt-2">
                    <p><strong>Initialize Demo Data:</strong> Adds basic services and products</p>
                    <p><strong>Seed Test Data:</strong> Adds comprehensive test data including 30 days of sample transactions for testing reports</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
