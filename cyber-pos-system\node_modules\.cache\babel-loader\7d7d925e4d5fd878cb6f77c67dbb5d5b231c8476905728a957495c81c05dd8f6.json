{"ast": null, "code": "'use strict';\n\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('a', 'y') -> /a/y and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\nvar UNSUPPORTED_Y = fails(function () {\n  var re = $RegExp('a', 'y');\n  re.lastIndex = 2;\n  return re.exec('abcd') !== null;\n});\n\n// UC Browser bug\n// https://github.com/zloirock/core-js/issues/1008\nvar MISSED_STICKY = UNSUPPORTED_Y || fails(function () {\n  return !$RegExp('a', 'y').sticky;\n});\nvar BROKEN_CARET = UNSUPPORTED_Y || fails(function () {\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=773687\n  var re = $RegExp('^r', 'gy');\n  re.lastIndex = 2;\n  return re.exec('str') !== null;\n});\nmodule.exports = {\n  BROKEN_CARET: BROKEN_CARET,\n  MISSED_STICKY: MISSED_STICKY,\n  UNSUPPORTED_Y: UNSUPPORTED_Y\n};", "map": {"version": 3, "names": ["fails", "require", "globalThis", "$RegExp", "RegExp", "UNSUPPORTED_Y", "re", "lastIndex", "exec", "MISSED_STICKY", "sticky", "BROKEN_CARET", "module", "exports"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/regexp-sticky-helpers.js"], "sourcesContent": ["'use strict';\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('a', 'y') -> /a/y and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\n\nvar UNSUPPORTED_Y = fails(function () {\n  var re = $RegExp('a', 'y');\n  re.lastIndex = 2;\n  return re.exec('abcd') !== null;\n});\n\n// UC Browser bug\n// https://github.com/zloirock/core-js/issues/1008\nvar MISSED_STICKY = UNSUPPORTED_Y || fails(function () {\n  return !$RegExp('a', 'y').sticky;\n});\n\nvar BROKEN_CARET = UNSUPPORTED_Y || fails(function () {\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=773687\n  var re = $RegExp('^r', 'gy');\n  re.lastIndex = 2;\n  return re.exec('str') !== null;\n});\n\nmodule.exports = {\n  BROKEN_CARET: BROKEN_CARET,\n  MISSED_STICKY: MISSED_STICKY,\n  UNSUPPORTED_Y: UNSUPPORTED_Y\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;;AAEpD;AACA,IAAIE,OAAO,GAAGD,UAAU,CAACE,MAAM;AAE/B,IAAIC,aAAa,GAAGL,KAAK,CAAC,YAAY;EACpC,IAAIM,EAAE,GAAGH,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC1BG,EAAE,CAACC,SAAS,GAAG,CAAC;EAChB,OAAOD,EAAE,CAACE,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI;AACjC,CAAC,CAAC;;AAEF;AACA;AACA,IAAIC,aAAa,GAAGJ,aAAa,IAAIL,KAAK,CAAC,YAAY;EACrD,OAAO,CAACG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACO,MAAM;AAClC,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAGN,aAAa,IAAIL,KAAK,CAAC,YAAY;EACpD;EACA,IAAIM,EAAE,GAAGH,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;EAC5BG,EAAE,CAACC,SAAS,GAAG,CAAC;EAChB,OAAOD,EAAE,CAACE,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI;AAChC,CAAC,CAAC;AAEFI,MAAM,CAACC,OAAO,GAAG;EACfF,YAAY,EAAEA,YAAY;EAC1BF,aAAa,EAAEA,aAAa;EAC5BJ,aAAa,EAAEA;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}