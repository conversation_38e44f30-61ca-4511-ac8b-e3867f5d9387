{"ast": null, "code": "'use strict';\n\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('.', 's') -> /./s and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\nmodule.exports = fails(function () {\n  var re = $RegExp('.', 's');\n  return !(re.dotAll && re.test('\\n') && re.flags === 's');\n});", "map": {"version": 3, "names": ["fails", "require", "globalThis", "$RegExp", "RegExp", "module", "exports", "re", "dotAll", "test", "flags"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/regexp-unsupported-dot-all.js"], "sourcesContent": ["'use strict';\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('.', 's') -> /./s and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\n\nmodule.exports = fails(function () {\n  var re = $RegExp('.', 's');\n  return !(re.dotAll && re.test('\\n') && re.flags === 's');\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;;AAEpD;AACA,IAAIE,OAAO,GAAGD,UAAU,CAACE,MAAM;AAE/BC,MAAM,CAACC,OAAO,GAAGN,KAAK,CAAC,YAAY;EACjC,IAAIO,EAAE,GAAGJ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC1B,OAAO,EAAEI,EAAE,CAACC,MAAM,IAAID,EAAE,CAACE,IAAI,CAAC,IAAI,CAAC,IAAIF,EAAE,CAACG,KAAK,KAAK,GAAG,CAAC;AAC1D,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}