{"ast": null, "code": "'use strict';\n\nvar call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar isCallable = require('../internals/is-callable');\nvar classof = require('../internals/classof-raw');\nvar regexpExec = require('../internals/regexp-exec');\nvar $TypeError = TypeError;\n\n// `RegExpExec` abstract operation\n// https://tc39.es/ecma262/#sec-regexpexec\nmodule.exports = function (R, S) {\n  var exec = R.exec;\n  if (isCallable(exec)) {\n    var result = call(exec, R, S);\n    if (result !== null) anObject(result);\n    return result;\n  }\n  if (classof(R) === 'RegExp') return call(regexpExec, R, S);\n  throw new $TypeError('RegExp#exec called on incompatible receiver');\n};", "map": {"version": 3, "names": ["call", "require", "anObject", "isCallable", "classof", "regexpExec", "$TypeError", "TypeError", "module", "exports", "R", "S", "exec", "result"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/regexp-exec-abstract.js"], "sourcesContent": ["'use strict';\nvar call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar isCallable = require('../internals/is-callable');\nvar classof = require('../internals/classof-raw');\nvar regexpExec = require('../internals/regexp-exec');\n\nvar $TypeError = TypeError;\n\n// `RegExpExec` abstract operation\n// https://tc39.es/ecma262/#sec-regexpexec\nmodule.exports = function (R, S) {\n  var exec = R.exec;\n  if (isCallable(exec)) {\n    var result = call(exec, R, S);\n    if (result !== null) anObject(result);\n    return result;\n  }\n  if (classof(R) === 'RegExp') return call(regexpExec, R, S);\n  throw new $TypeError('RegExp#exec called on incompatible receiver');\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIG,OAAO,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AACjD,IAAII,UAAU,GAAGJ,OAAO,CAAC,0BAA0B,CAAC;AAEpD,IAAIK,UAAU,GAAGC,SAAS;;AAE1B;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAC/B,IAAIC,IAAI,GAAGF,CAAC,CAACE,IAAI;EACjB,IAAIT,UAAU,CAACS,IAAI,CAAC,EAAE;IACpB,IAAIC,MAAM,GAAGb,IAAI,CAACY,IAAI,EAAEF,CAAC,EAAEC,CAAC,CAAC;IAC7B,IAAIE,MAAM,KAAK,IAAI,EAAEX,QAAQ,CAACW,MAAM,CAAC;IACrC,OAAOA,MAAM;EACf;EACA,IAAIT,OAAO,CAACM,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAOV,IAAI,CAACK,UAAU,EAAEK,CAAC,EAAEC,CAAC,CAAC;EAC1D,MAAM,IAAIL,UAAU,CAAC,6CAA6C,CAAC;AACrE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}