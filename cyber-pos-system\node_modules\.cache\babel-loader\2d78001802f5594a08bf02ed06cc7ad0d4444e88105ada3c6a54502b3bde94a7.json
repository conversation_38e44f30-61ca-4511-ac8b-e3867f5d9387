{"ast": null, "code": "'use strict';\n\nvar defineProperty = require('../internals/object-define-property').f;\nvar hasOwn = require('../internals/has-own-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nmodule.exports = function (target, TAG, STATIC) {\n  if (target && !STATIC) target = target.prototype;\n  if (target && !hasOwn(target, TO_STRING_TAG)) {\n    defineProperty(target, TO_STRING_TAG, {\n      configurable: true,\n      value: TAG\n    });\n  }\n};", "map": {"version": 3, "names": ["defineProperty", "require", "f", "hasOwn", "wellKnownSymbol", "TO_STRING_TAG", "module", "exports", "target", "TAG", "STATIC", "prototype", "configurable", "value"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/node_modules/core-js/internals/set-to-string-tag.js"], "sourcesContent": ["'use strict';\nvar defineProperty = require('../internals/object-define-property').f;\nvar hasOwn = require('../internals/has-own-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n\nmodule.exports = function (target, TAG, STATIC) {\n  if (target && !STATIC) target = target.prototype;\n  if (target && !hasOwn(target, TO_STRING_TAG)) {\n    defineProperty(target, TO_STRING_TAG, { configurable: true, value: TAG });\n  }\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,cAAc,GAAGC,OAAO,CAAC,qCAAqC,CAAC,CAACC,CAAC;AACrE,IAAIC,MAAM,GAAGF,OAAO,CAAC,+BAA+B,CAAC;AACrD,IAAIG,eAAe,GAAGH,OAAO,CAAC,gCAAgC,CAAC;AAE/D,IAAII,aAAa,GAAGD,eAAe,CAAC,aAAa,CAAC;AAElDE,MAAM,CAACC,OAAO,GAAG,UAAUC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC9C,IAAIF,MAAM,IAAI,CAACE,MAAM,EAAEF,MAAM,GAAGA,MAAM,CAACG,SAAS;EAChD,IAAIH,MAAM,IAAI,CAACL,MAAM,CAACK,MAAM,EAAEH,aAAa,CAAC,EAAE;IAC5CL,cAAc,CAACQ,MAAM,EAAEH,aAAa,EAAE;MAAEO,YAAY,EAAE,IAAI;MAAEC,KAAK,EAAEJ;IAAI,CAAC,CAAC;EAC3E;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}